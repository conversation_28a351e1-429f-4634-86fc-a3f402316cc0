# pydicom Programming Guide

This guide provides comprehensive guidance on how the DICOM standard is implemented specifically in the pydicom library, focusing on practical usage patterns and data type handling.

## Table of Contents

- [DICOM Element Value Representations (VRs) and Python Types](#dicom-element-value-representations-vrs-and-python-types)
- [Working with DICOM Elements](#working-with-dicom-elements)
- [Data Type Conversions](#data-type-conversions)
- [Special Cases and Best Practices](#special-cases-and-best-practices)
- [PyRT-DICOM Integration Patterns](#pyrt-dicom-integration-patterns)

## DICOM Element Value Representations (VRs) and Python Types

DICOM elements can contain various data types including ASCII strings, unicode text, decimals, floating-point numbers, integers, and encoded binary data. The Value Representation (VR) defines the format of an element's value, and pydicom provides seamless mapping between DICOM VRs and Python types.

### Core Principles

1. **Native Python Types**: Most DICOM VRs can be set using standard Python types
2. **Automatic Conversion**: pydicom handles conversion between Python types and DICOM wire format
3. **Empty Values**: Elements can be set as empty using `None`
4. **Multiplicity Support**: Most elements support multiple values using Python lists
5. **Type Safety**: pydicom provides type checking and validation

### Complete VR to Python Type Mapping

| DICOM VR | Description | Python Type | Examples |
|----------|-------------|-------------|----------|
| **String VRs** | | | |
| AE | Application Entity | `str` | `"MYAETITLE"` |
| AS | Age String | `str` | `"030Y"`, `"006M"` |
| CS | Code String | `str` | `"STATIC"`, `"DYNAMIC"` |
| DA | Date | `str` | `"20240101"` |
| DS | Decimal String | `str` or `float` or `int` | `"3.14159"`, `3.14159`, `3` |
| DT | Date Time | `str` | `"20240101123000.000000+0000"` |
| IS | Integer String | `str` or `int` | `"42"`, `42` |
| LO | Long String | `str` | `"Patient Name"` |
| LT | Long Text | `str` | `"Long description text..."` |
| PN | Person Name | `str` or `PersonName` | `"Doe^John"` |
| SH | Short String | `str` | `"Study ID"` |
| ST | Short Text | `str` | `"Brief description"` |
| TM | Time | `str` | `"123000.000000"` |
| UC | Unlimited Characters | `str` | `"Unlimited text content"` |
| UI | Unique Identifier | `str` | `"1.2.840.10008.1.2"` |
| UR | Universal Resource | `str` | `"http://example.com"` |
| UT | Unlimited Text | `str` | `"Very long text content..."` |
| **Numeric VRs** | | | |
| FL | Floating Point Single | `float` | `3.14159` |
| FD | Floating Point Double | `float` | `3.141592653589793` |
| SL | Signed Long | `int` | `-**********` to `**********` |
| SS | Signed Short | `int` | `-32768` to `32767` |
| UL | Unsigned Long | `int` | `0` to `**********` |
| US | Unsigned Short | `int` | `0` to `65535` |
| **Binary VRs** | | | |
| OB | Other Byte | `bytes` | `b'\x00\x01\x02\x03'` |
| OD | Other Double | `bytes` | Binary double data |
| OF | Other Float | `bytes` | Binary float data |
| OL | Other Long | `bytes` | Binary long data |
| OV | Other 64-bit Very Long | `bytes` | Binary 64-bit data |
| OW | Other Word | `bytes` | Binary word data |
| UN | Unknown | `bytes` | Unknown binary data |
| **Special VRs** | | | |
| AT | Attribute Tag | `int` or `tuple` | `0x00100010` or `(0x0010, 0x0010)` |
| SQ | Sequence of Items | `list[Dataset]` | `[Dataset(), Dataset()]` |
| SV | Signed 64-bit Very Long | `int` | Large signed integers |
| UV | Unsigned 64-bit Very Long | `int` | Large unsigned integers |

### String VRs in Detail

String VRs are the most common DICOM element types and map directly to Python strings:

```python
from pydicom import Dataset

# Create a dataset
ds = Dataset()

# String VRs - all use Python str
ds.PatientName = "Doe^John"                    # PN - Person Name
ds.StudyDescription = "CT Chest"               # LO - Long String
ds.Modality = "CT"                            # CS - Code String
ds.StudyDate = "20240101"                     # DA - Date
ds.StudyTime = "120000"                       # TM - Time
ds.AccessionNumber = "ACC123456"              # SH - Short String
ds.StudyComments = "Patient cooperative"      # LT - Long Text
```

### Numeric VRs in Detail

Numeric VRs map to Python's `int` and `float` types with automatic conversion:

```python
# Integer VRs
ds.SeriesNumber = 1                           # IS - Integer String
ds.Rows = 512                                 # US - Unsigned Short
ds.Columns = 512                              # US - Unsigned Short
ds.NumberOfFrames = 1                         # IS - Integer String

# Float VRs
ds.SliceThickness = 5.0                       # DS - Decimal String
ds.PixelSpacing = [0.5, 0.5]                 # DS - Multiple values
ds.WindowCenter = 40.0                        # DS - Decimal String
ds.WindowWidth = 400.0                        # DS - Decimal String

# Direct numeric VRs
ds.SmallestImagePixelValue = 0                # US
ds.LargestImagePixelValue = 4095              # US
```

### Binary VRs and Large Data

Binary VRs handle raw data and support both `bytes` objects and buffered I/O:

```python
import numpy as np

# Small binary data
ds.PixelData = b'\x00\x01\x02\x03'          # OW/OB - Direct bytes

# Large binary data from numpy array
pixel_array = np.random.randint(0, 4096, (512, 512), dtype=np.uint16)
ds.PixelData = pixel_array.tobytes()         # OW - Convert to bytes

# Buffered I/O for very large data
with open('large_pixel_data.bin', 'rb') as f:
    ds.PixelData = f                         # Use file object directly
```

### Sequence VRs (SQ)

Sequence VRs contain nested datasets and must use `list[Dataset]`:

```python
# Create sequence with multiple items
contour_sequence = []

for i in range(3):
    contour_item = Dataset()
    contour_item.ContourNumber = i + 1
    contour_item.ContourData = [1.0, 2.0, 3.0, 4.0, 5.0, 6.0]
    contour_sequence.append(contour_item)

ds.ContourSequence = contour_sequence        # SQ - Sequence of Items
```

### Person Name VR (PN)

Person names have special handling with the `PersonName` class:

```python
from pydicom.valuerep import PersonName

# Simple string format
ds.PatientName = "Doe^John^Middle^Jr^Dr"    # Family^Given^Middle^Suffix^Prefix

# Using PersonName object for better control
name = PersonName("Doe^John")
name.family_name = "Doe"
name.given_name = "John"
name.middle_name = "Michael"
ds.PatientName = name

# Access components
print(name.family_name)     # "Doe"
print(name.given_name)      # "John"
```

## Working with DICOM Elements

### Setting Element Values

```python
# Direct assignment
ds.PatientID = "12345"

# Using setattr for dynamic element names
setattr(ds, 'PatientID', "12345")

# Using tag numbers
ds[0x0010, 0x0020].value = "12345"          # PatientID tag

# Setting multiple values (multiplicity)
ds.ImagePositionPatient = [100.0, 200.0, 50.0]  # List for multiple values
ds.WindowCenter = [40.0, 400.0]             # Multiple window centers
```

### Getting Element Values

```python
# Direct access
patient_id = ds.PatientID

# Safe access with default
patient_id = getattr(ds, 'PatientID', 'Unknown')

# Check if element exists
if 'PatientID' in ds:
    patient_id = ds.PatientID

# Access by tag
patient_id = ds[0x0010, 0x0020].value
```

### Empty Values and None

```python
# Set empty values
ds.PatientComments = None                    # Empty element
ds.StudyDescription = ""                     # Empty string

# Check for empty values
if ds.get('PatientComments') is None:
    print("No comments")
```

## Data Type Conversions

### Automatic Conversions

pydicom automatically converts between Python types and DICOM wire format:

```python
# These are all equivalent for DS (Decimal String) VR
ds.SliceThickness = "5.0"      # String
ds.SliceThickness = 5.0        # Float  
ds.SliceThickness = 5          # Integer

# pydicom stores as string but converts for calculations
thickness = float(ds.SliceThickness)  # Always works
```

### Date and Time Handling

```python
from datetime import datetime, date

# Date VR (DA) - multiple input formats
ds.StudyDate = "20240101"                    # String format
ds.StudyDate = datetime(2024, 1, 1)         # datetime object
ds.StudyDate = date(2024, 1, 1)            # date object

# Time VR (TM) - multiple input formats  
ds.StudyTime = "120000"                      # String format
ds.StudyTime = datetime.now().time()        # time object

# DateTime VR (DT) - combined date/time
ds.AcquisitionDateTime = "20240101120000"    # String format
ds.AcquisitionDateTime = datetime.now()     # datetime object
```

### Numeric Precision

```python
# Decimal String (DS) - finite precision
ds.SliceThickness = 5.123456789             # Input float
print(ds.SliceThickness)                    # May be truncated to "5.12346"

# Use string for exact precision
ds.SliceThickness = "5.123456789"          # Exact string preserved

# Integer String (IS)
ds.SeriesNumber = 42                        # Integer
ds.SeriesNumber = "42"                      # String - both valid
```

## Special Cases and Best Practices

### Working with Large Pixel Data

```python
import numpy as np

# Efficient handling of large arrays
pixel_array = np.random.randint(0, 4096, (512, 512, 100), dtype=np.uint16)

# Convert to bytes for storage
ds.PixelData = pixel_array.tobytes()
ds.Rows = pixel_array.shape[0]
ds.Columns = pixel_array.shape[1]  
ds.NumberOfFrames = pixel_array.shape[2] if len(pixel_array.shape) > 2 else 1

# Set required pixel data elements
ds.SamplesPerPixel = 1
ds.PhotometricInterpretation = "MONOCHROME2"
ds.BitsAllocated = 16
ds.BitsStored = 16
ds.HighBit = 15
ds.PixelRepresentation = 0                  # Unsigned
```

### Validation and Type Safety

```python
from pydicom.valuerep import validate_value

# Validate values before assignment
try:
    validate_value("US", 65536)              # Will raise exception - out of range
except ValueError as e:
    print(f"Invalid value: {e}")

# Safe assignment with validation
def safe_assign(ds, element_name, value, vr):
    try:
        validate_value(vr, value)
        setattr(ds, element_name, value)
    except ValueError as e:
        print(f"Cannot assign {value} to {element_name}: {e}")
```

### Handling Unknown VRs

```python
# Unknown VR (UN) - binary data of unknown format
ds.PrivateCreator = b'\x00\x01\x02\x03'    # Binary data
ds[0x7777, 0x0001].VR = 'UN'               # Explicitly set VR

# Private elements often use UN
ds[0x0009, 0x0010].value = "PRIVATE_CREATOR"
ds[0x0009, 0x1001].value = b'\x01\x02\x03\x04'  # Private binary data
```

### Memory Optimization

```python
# Use buffered I/O for large datasets
import tempfile

# Create temporary file for large pixel data
with tempfile.NamedTemporaryFile() as temp_file:
    pixel_array.tofile(temp_file.name)
    
    with open(temp_file.name, 'rb') as f:
        ds.PixelData = f.read()              # Load from file
```

## PyRT-DICOM Integration Patterns

### Module Construction with VR Awareness

When using PyRT-DICOM modules, understanding VR mappings helps with type-safe construction:

```python
from pyrt_dicom.modules.modules.patient_module import PatientModule
from pyrt_dicom.enums.patient_enums import PatientSex
from pydicom.valuerep import PersonName

# Type-safe module construction
patient = PatientModule.from_required_elements(
    patient_name=PersonName("Doe^John"),     # PN VR
    patient_id="12345",                      # LO VR  
    patient_birth_date="19900101",           # DA VR
    patient_sex=PatientSex.MALE             # CS VR (enum)
)

# Add optional elements with proper types
patient.with_optional_elements(
    patient_age="034Y",                      # AS VR
    patient_weight="70.5",                   # DS VR
    patient_size="1.75"                      # DS VR
)
```

### ValidationResult Integration

PyRT-DICOM's `ValidationResult` system works with VR validation:

```python
from pyrt_dicom.validators import ValidationResult

# Validation includes VR type checking
result = patient.validate()

if not result.is_valid:
    for error in result.errors:
        if "VR" in error.message:
            print(f"VR validation error: {error.message}")
```

### IOD Dataset Generation

Understanding how VRs are preserved during dataset generation:

```python
from pyrt_dicom.iods.rt_dose_iod import RTDoseIOD

# Create IOD with proper VR handling
dose_iod = RTDoseIOD(...)

# Generate dataset - all VRs preserved
dataset = dose_iod.to_dataset()

# VR information is maintained
for elem in dataset:
    print(f"{elem.name}: {elem.VR} = {elem.value}")
```

### Best Practices for PyRT-DICOM

1. **Use Enums**: PyRT-DICOM enums ensure valid CS VR values
2. **Type Hints**: Module constructors provide VR-aware type hints  
3. **Validation**: Always validate to catch VR type mismatches
4. **Dataset Generation**: Use IOD methods for proper VR handling

```python
# Good: Using enums for CS VRs
from pyrt_dicom.enums.dose_enums import DoseType
dose_module.dose_type = DoseType.PHYSICAL

# Bad: Using raw strings (no validation)
dose_module.dose_type = "PHYSICAL"  # Could be typo

# Good: Type-safe construction
module = Module.from_required_elements(
    integer_param=42,           # Will be IS VR
    float_param=3.14,          # Will be DS VR  
    string_param="value"       # Will be appropriate string VR
)

# Good: Validation before use
result = module.validate()
if result.is_valid:
    dataset = iod.to_dataset()
else:
    print("Fix validation errors first")
```

This comprehensive mapping between DICOM VRs and Python types ensures that PyRT-DICOM provides type-safe, validated DICOM file generation while maintaining full compatibility with the pydicom library.