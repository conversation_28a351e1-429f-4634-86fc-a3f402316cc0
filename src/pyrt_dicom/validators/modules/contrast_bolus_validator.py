"""Contrast/Bolus Module Validator - DICOM PS3.3 C.7.6.4

Validates Contrast/Bolus Module datasets for DICOM compliance.
This validator enforces all Type 1, Type 2, Type 3, and conditional requirements
from the DICOM PS3.3 C.7.6.4 specification.

Key Validation Areas:
- Type 2 element presence (ContrastBolusAgent must be present)
- Enumerated value compliance (ContrastBolusIngredient)
- Sequence structure validation (Code Sequence Macro compliance)
- Flow rate/duration correspondence (VM 1-n relationship)
- Timing consistency and logical relationships
- Administration route single-item constraint
"""

from pydicom import Dataset
from .base_validator import BaseValidator, ValidationConfig
from ..validation_result import ValidationResult
from ...enums.contrast_ct_enums import ContrastBolusIngredient


class ContrastBolusValidator(BaseValidator):
    """Validator for Contrast/Bolus Module (DICOM PS3.3 C.7.6.4).
    
    Validates dataset compliance with DICOM PS3.3 C.7.6.4 Contrast/Bolus Module 
    specification including all required elements, conditional logic, and
    sequence structure requirements.
    """
    
    @staticmethod
    def validate(dataset: Dataset, config: ValidationConfig | None = None) -> ValidationResult:
        """Validate Contrast/Bolus Module requirements on a pydicom Dataset.
        
        Validates all Type 1, Type 2, Type 3, and conditional requirements from
        DICOM PS3.3 C.7.6.4 Contrast/Bolus Module specification.
        
        Args:
            dataset: pydicom Dataset containing contrast/bolus module attributes
            config: Validation configuration options (optional)
            
        Returns:
            ValidationResult: Structured validation results with errors and warnings.
                            Errors indicate DICOM standard violations requiring correction.
                            Warnings indicate recommendations for better compliance.
        """
        config = config or ValidationConfig()
        result = BaseValidator.create_validation_result()
        
        # Validate Type 2 required elements
        ContrastBolusValidator._validate_required_elements(dataset, result)
        
        # Validate enumerated values
        if config.check_enumerated_values:
            ContrastBolusValidator._validate_enumerated_values(dataset, result)
        
        # Validate flow rate/duration consistency 
        ContrastBolusValidator._validate_flow_consistency(dataset, result)
        
        # Validate sequence structures
        if config.validate_sequences:
            ContrastBolusValidator._validate_sequence_structures(dataset, result)
        
        # Validate timing consistency
        ContrastBolusValidator._validate_timing_consistency(dataset, result)
        
        # Validate logical consistency using dataset attributes
        ContrastBolusValidator._validate_logical_consistency(dataset, result)
        
        return result
    
    @staticmethod
    def _validate_required_elements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate required (Type 2) elements are present.
        
        Per DICOM PS3.3 C.7.6.4:
        - Contrast/Bolus Agent (0018,0010) is Type 2 - must be present, may be empty
        """
        # Contrast/Bolus Agent (0018,0010) - Type 2 (required but can be empty)
        if not hasattr(dataset, 'ContrastBolusAgent'):
            result.add_error(
                "Contrast/Bolus Agent (0018,0010) is required (Type 2) per DICOM PS3.3 C.7.6.4. "
                "This element must be present but may have an empty value."
            )
    
    @staticmethod
    def _validate_enumerated_values(dataset: Dataset, result: ValidationResult) -> None:
        """Validate enumerated values against DICOM standard defined terms.
        
        Per DICOM PS3.3 C.7.6.4:
        - Contrast/Bolus Ingredient (0018,1048) has defined terms: 
          IODINE, GADOLINIUM, CARBON DIOXIDE, BARIUM
        """
        # Contrast/Bolus Ingredient (0018,1048)
        ingredient = getattr(dataset, 'ContrastBolusIngredient', '')
        if ingredient:
            valid_ingredients = [ingredient_enum.value for ingredient_enum in ContrastBolusIngredient]
            BaseValidator.validate_enumerated_value(
                str(ingredient), valid_ingredients,
                "Contrast/Bolus Ingredient (0018,1048)", result
            )
            
            if str(ingredient) not in valid_ingredients:
                result.add_error(
                    f"Contrast/Bolus Ingredient (0018,1048) value '{ingredient}' is not a valid "
                    f"defined term per DICOM PS3.3 C.7.6.4. Valid values are: {', '.join(valid_ingredients)}. "
                    f"Use pyrt_dicom.enums.contrast_ct_enums.ContrastBolusIngredient enum for correct values."
                )
    
    @staticmethod
    def _validate_flow_consistency(dataset: Dataset, result: ValidationResult) -> None:
        """Validate consistency between flow rate and flow duration.
        
        Per DICOM PS3.3 C.7.6.4:
        - Contrast Flow Rate (0018,1046) and Contrast Flow Duration (0018,1047) both have VM 1-n
        - Each Flow Duration Value shall correspond to a Value of Contrast Flow Rate
        - This enables stepped injection protocols with multiple flow rates and durations
        """
        flow_rate = getattr(dataset, 'ContrastFlowRate', [])
        flow_duration = getattr(dataset, 'ContrastFlowDuration', [])
        
        # Convert single values to lists for consistent handling
        if flow_rate and not hasattr(flow_rate, '__iter__'):
            flow_rate = [flow_rate]
        if flow_duration and not hasattr(flow_duration, '__iter__'):
            flow_duration = [flow_duration]
            
        # If both are present, they must have the same number of values
        if flow_rate and flow_duration:
            if len(flow_rate) != len(flow_duration):
                result.add_error(
                    f"Contrast Flow Rate (0018,1046) and Contrast Flow Duration (0018,1047) "
                    f"must have the same number of values per DICOM PS3.3 C.7.6.4. "
                    f"Flow Rate has {len(flow_rate)} values, Flow Duration has {len(flow_duration)} values. "
                    f"Each Flow Duration Value shall correspond to a Flow Rate Value to enable "
                    f"stepped injection protocols. Ensure both attributes have matching value counts."
                )
    
    @staticmethod
    def _validate_sequence_structures(dataset: Dataset, result: ValidationResult) -> None:
        """Validate sequence structures and their code items."""
        
        # Validate Contrast/Bolus Agent Sequence
        agent_seq = getattr(dataset, 'ContrastBolusAgentSequence', [])
        for i, agent_item in enumerate(agent_seq):
            ContrastBolusValidator._validate_code_sequence_item(
                agent_item, f"Contrast/Bolus Agent Sequence item {i+1}", result
            )
        
        # Validate Administration Route Sequence
        route_seq = getattr(dataset, 'ContrastBolusAdministrationRouteSequence', [])
        if len(route_seq) > 1:
            result.add_error(
                "Contrast/Bolus Administration Route Sequence (0018,0014) "
                f"should contain only a single Item per DICOM PS3.3 C.7.6.4. "
                f"Found {len(route_seq)} items. Remove extra sequence items to comply with "
                f"the single-item constraint specified in the DICOM standard."
            )
        
        for i, route_item in enumerate(route_seq):
            ContrastBolusValidator._validate_code_sequence_item(
                route_item, f"Administration Route Sequence item {i+1}", result
            )
            
            # Validate nested Additional Drug Sequence
            drug_seq = getattr(route_item, 'AdditionalDrugSequence', [])
            for j, drug_item in enumerate(drug_seq):
                ContrastBolusValidator._validate_code_sequence_item(
                    drug_item, f"Additional Drug Sequence item {j+1} in route item {i+1}", result
                )
    
    @staticmethod
    def _validate_code_sequence_item(item: Dataset, item_description: str, result: ValidationResult) -> None:
        """Validate a code sequence item structure (Code Sequence Macro).
        
        Per DICOM PS3.3 Table 8.8-1 Code Sequence Macro Attributes:
        - Code Value (0008,0100) Type 1 - Required, must have value
        - Coding Scheme Designator (0008,0102) Type 1 - Required, must have value
        - Code Meaning (0008,0104) Type 1 - Required, must have value
        
        This macro is included in:
        - Contrast/Bolus Agent Sequence (0018,0012)
        - Contrast/Bolus Administration Route Sequence (0018,0014) 
        - Additional Drug Sequence (0018,002A)
        """
        # Code Value is required (Type 1)
        if not hasattr(item, 'CodeValue') or not item.CodeValue:
            result.add_error(
                f"Code Value (0008,0100) is required (Type 1) in {item_description} "
                f"per DICOM PS3.3 Table 8.8-1 Code Sequence Macro. "
                f"Provide a valid code value from the appropriate coding scheme."
            )
        
        # Coding Scheme Designator is required (Type 1)
        if not hasattr(item, 'CodingSchemeDesignator') or not item.CodingSchemeDesignator:
            result.add_error(
                f"Coding Scheme Designator (0008,0102) is required (Type 1) in {item_description} "
                f"per DICOM PS3.3 Table 8.8-1 Code Sequence Macro. "
                f"Specify the coding scheme (e.g., 'SCT' for SNOMED CT, 'SRT' for SNOMED RT)."
            )
        
        # Code Meaning is required (Type 1) per Code Sequence Macro
        if not hasattr(item, 'CodeMeaning') or not item.CodeMeaning:
            result.add_error(
                f"Code Meaning (0008,0104) is required (Type 1) in {item_description} "
                f"per DICOM PS3.3 Table 8.8-1 Code Sequence Macro. "
                f"Provide a human-readable description of the code value."
            )
    
    @staticmethod
    def _validate_timing_consistency(dataset: Dataset, result: ValidationResult) -> None:
        """Validate timing information consistency and format.
        
        Per DICOM PS3.3 C.7.6.4:
        - Start Time and Stop Time should be in logical order
        - Flow Duration is an alternative method of specifying stop time
        - All times should be in DICOM TM format (HHMMSS or HHMMSS.FFFFFF)
        """
        start_time = getattr(dataset, 'ContrastBolusStartTime', '')
        stop_time = getattr(dataset, 'ContrastBolusStopTime', '')
        flow_duration = getattr(dataset, 'ContrastFlowDuration', [])
        
        # Validate start/stop time ordering
        if start_time and stop_time:
            try:
                # Parse DICOM TM format (HHMMSS or HHMMSS.FFFFFF)
                start_parts = start_time.split('.')
                stop_parts = stop_time.split('.')
                
                start_hhmmss = start_parts[0]
                stop_hhmmss = stop_parts[0]
                
                if len(start_hhmmss) >= 6 and len(stop_hhmmss) >= 6:
                    start_seconds = (int(start_hhmmss[:2]) * 3600 + 
                                   int(start_hhmmss[2:4]) * 60 + 
                                   int(start_hhmmss[4:6]))
                    stop_seconds = (int(stop_hhmmss[:2]) * 3600 + 
                                  int(stop_hhmmss[2:4]) * 60 + 
                                  int(stop_hhmmss[4:6]))
                    
                    if start_seconds >= stop_seconds:
                        result.add_warning(
                            "Contrast/Bolus Start Time (0018,1042) should be before "
                            "Contrast/Bolus Stop Time (0018,1043) for logical consistency. "
                            f"Start: {start_time}, Stop: {stop_time}. "
                            f"Verify injection timing parameters are correct."
                        )
            except (ValueError, IndexError) as e:
                result.add_warning(
                    f"Could not validate time order due to invalid DICOM TM format. "
                    f"Start Time: '{start_time}', Stop Time: '{stop_time}'. "
                    f"Use HHMMSS or HHMMSS.FFFFFF format per DICOM VR TM specification."
                )
        
        # Validate alternative timing specification methods
        if stop_time and flow_duration:
            result.add_warning(
                "Both Contrast/Bolus Stop Time (0018,1043) and Contrast Flow Duration (0018,1047) "
                "are present. Per DICOM PS3.3 C.7.6.4, flow duration is an alternate method of "
                "specifying stop time. Consider using only one timing method for consistency."
            )
    
    @staticmethod
    def _validate_logical_consistency(dataset: Dataset, result: ValidationResult) -> None:
        """Validate logical consistency between related attributes.
        
        Per DICOM PS3.3 C.7.6.4 and standard example:
        - Total dose should not exceed injected volume (dilution logic)
        - Ingredient and concentration should be paired for completeness
        - Volume represents diluted contrast amount injected
        - Total dose represents undiluted contrast agent amount
        """
        volume = getattr(dataset, 'ContrastBolusVolume', None)
        total_dose = getattr(dataset, 'ContrastBolusTotalDose', None)
        ingredient = getattr(dataset, 'ContrastBolusIngredient', '')
        concentration = getattr(dataset, 'ContrastBolusIngredientConcentration', None)
        
        # Validate dose/volume relationship based on DICOM standard example
        if volume is not None and total_dose is not None:
            if total_dose > volume:
                result.add_warning(
                    f"Contrast/Bolus Total Dose ({total_dose} ml) should not exceed "
                    f"Contrast/Bolus Volume ({volume} ml). Per DICOM PS3.3 C.7.6.4 example, "
                    f"Total Dose represents undiluted contrast agent amount and Volume represents "
                    f"diluted amount injected. For 1:1 dilution, Total Dose should be ≤ Volume/2."
                )
        
        # Validate ingredient/concentration pairing for complete characterization
        if ingredient and concentration is None:
            result.add_warning(
                "Contrast/Bolus Ingredient Concentration (0018,1049) is recommended "
                "when Contrast/Bolus Ingredient (0018,1048) is specified for "
                "complete contrast agent characterization. Concentration specifies "
                "milligrams of active ingredient per milliliter of diluted agent."
            )
        
        if concentration is not None and not ingredient:
            result.add_warning(
                "Contrast/Bolus Ingredient (0018,1048) is recommended "
                "when Contrast/Bolus Ingredient Concentration (0018,1049) is specified "
                "for complete contrast agent characterization. Use ContrastBolusIngredient "
                "enum values: IODINE, GADOLINIUM, CARBON DIOXIDE, or BARIUM."
            )
