"""Clinical Trial Subject Module DICOM validation - PS3.3 C.7.1.3

This validator ensures complete compliance with DICOM PS3.3 C.7.1.3 Clinical Trial Subject Module
requirements, including all Type 1, Type 2, Type 1C conditional requirements, and sequence validation.
"""

from pydicom import Dataset
from .base_validator import BaseValidator, ValidationConfig
from ..validation_result import ValidationResult


class ClinicalTrialSubjectValidator:
    """Validator for DICOM Clinical Trial Subject Module (PS3.3 C.7.1.3).

    Validates all requirements for clinical trial subject identification including:
    - Type 1 required elements (sponsor name, protocol ID)
    - Type 2 required but can be empty elements (protocol name, site ID/name)
    - Type 1C conditional requirements (subject identification, ethics committee)
    - Type 3 optional elements validation
    - Sequence structure validation for Other Clinical Trial Protocol IDs

    Provides comprehensive error messages with DICOM tag references and guidance
    for resolving validation issues.
    """
    
    @staticmethod
    def validate(dataset: Dataset, config: ValidationConfig | None = None) -> ValidationResult:
        """Validate Clinical Trial Subject Module requirements on any pydicom Dataset.

        Performs comprehensive validation of all DICOM PS3.3 C.7.1.3 requirements including
        Type 1/2 elements, conditional logic, and sequence structures.

        Args:
            dataset: pydicom Dataset to validate against Clinical Trial Subject Module requirements
            config: Validation configuration options controlling validation scope and behavior

        Returns:
            ValidationResult with detailed errors and warnings including DICOM tag references
            and actionable guidance for resolving validation issues
        """
        config = config or ValidationConfig()
        result = BaseValidator.create_validation_result()

        # Validate Type 1 required elements (must be present and non-empty)
        ClinicalTrialSubjectValidator._validate_type_1_elements(dataset, result)

        # Validate Type 2 required elements (must be present but can be empty)
        ClinicalTrialSubjectValidator._validate_type_2_elements(dataset, result)

        # Validate Type 1C conditional requirements (context-dependent requirements)
        if config.validate_conditional_requirements:
            ClinicalTrialSubjectValidator._validate_conditional_requirements(dataset, result)

        # Validate sequence structures and their required sub-attributes
        if config.validate_sequences:
            ClinicalTrialSubjectValidator._validate_sequence_requirements(dataset, result)

        # Validate value representation constraints and semantic consistency
        if config.check_enumerated_values:
            ClinicalTrialSubjectValidator._validate_value_constraints(dataset, result)

        return result
    
    @staticmethod
    def _validate_type_1_elements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate Type 1 (required) elements per DICOM PS3.3 C.7.1.3.

        Type 1 elements must be present and contain non-empty values.
        """

        # Clinical Trial Sponsor Name (0012,0010) Type 1
        if not hasattr(dataset, 'ClinicalTrialSponsorName') or not getattr(dataset, 'ClinicalTrialSponsorName', '').strip():
            result.add_error(
                "Clinical Trial Sponsor Name (0012,0010) is required (Type 1) per DICOM PS3.3 C.7.1.3. "
                "This identifies the entity responsible for conducting the clinical trial. "
                "Provide a non-empty string value for the sponsor organization name."
            )

        # Clinical Trial Protocol ID (0012,0020) Type 1
        if not hasattr(dataset, 'ClinicalTrialProtocolID') or not getattr(dataset, 'ClinicalTrialProtocolID', '').strip():
            result.add_error(
                "Clinical Trial Protocol ID (0012,0020) is required (Type 1) per DICOM PS3.3 C.7.1.3. "
                "This uniquely identifies the investigational protocol. "
                "Provide a non-empty string value for the protocol identifier."
            )
    
    @staticmethod
    def _validate_type_2_elements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate Type 2 (required but can be empty) elements per DICOM PS3.3 C.7.1.3.

        Type 2 elements must be present in the dataset but may contain empty values.
        """

        # Clinical Trial Protocol Name (0012,0021) Type 2
        if not hasattr(dataset, 'ClinicalTrialProtocolName'):
            result.add_error(
                "Clinical Trial Protocol Name (0012,0021) must be present (Type 2) per DICOM PS3.3 C.7.1.3. "
                "This contains the title of the investigational protocol. "
                "Add this attribute to the dataset (value may be empty string if unknown)."
            )

        # Clinical Trial Site ID (0012,0030) Type 2
        if not hasattr(dataset, 'ClinicalTrialSiteID'):
            result.add_error(
                "Clinical Trial Site ID (0012,0030) must be present (Type 2) per DICOM PS3.3 C.7.1.3. "
                "This identifies the site responsible for submitting clinical trial data. "
                "Add this attribute to the dataset (value may be empty string if not applicable)."
            )

        # Clinical Trial Site Name (0012,0031) Type 2
        if not hasattr(dataset, 'ClinicalTrialSiteName'):
            result.add_error(
                "Clinical Trial Site Name (0012,0031) must be present (Type 2) per DICOM PS3.3 C.7.1.3. "
                "This is the name of the site responsible for submitting clinical trial data. "
                "Add this attribute to the dataset (value may be empty string if not applicable)."
            )
    
    @staticmethod
    def _validate_conditional_requirements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate Type 1C conditional requirements per DICOM PS3.3 C.7.1.3.

        Type 1C elements are required only under specific conditions as defined by the DICOM standard.
        """

        # Type 1C: Clinical Trial Subject ID (0012,0040) or Clinical Trial Subject Reading ID (0012,0042) required
        # DICOM PS3.3 C.7.1.3: "Shall be present if Clinical Trial Subject Reading ID (0012,0042) is absent. May be present otherwise."
        # DICOM PS3.3 C.7.1.3: "Shall be present if Clinical Trial Subject ID (0012,0040) is absent. May be present otherwise."
        has_subject_id = hasattr(dataset, 'ClinicalTrialSubjectID') and getattr(dataset, 'ClinicalTrialSubjectID', '').strip()
        has_reading_id = hasattr(dataset, 'ClinicalTrialSubjectReadingID') and getattr(dataset, 'ClinicalTrialSubjectReadingID', '').strip()

        if not has_subject_id and not has_reading_id:
            result.add_error(
                "Either Clinical Trial Subject ID (0012,0040) or Clinical Trial Subject Reading ID (0012,0042) "
                "is required (Type 1C) per DICOM PS3.3 C.7.1.3. At least one must be present to identify the subject. "
                "Use Clinical Trial Subject ID for standard identification or Clinical Trial Subject Reading ID "
                "for blinded evaluations. Both may be present if needed."
            )

        # Type 1C: Ethics Committee Name (0012,0081) required if Ethics Committee Approval Number (0012,0082) is present
        # DICOM PS3.3 C.7.1.3: "Required if Clinical Trial Protocol Ethics Committee Approval Number (0012,0082) is present."
        has_approval_number = (hasattr(dataset, 'ClinicalTrialProtocolEthicsCommitteeApprovalNumber') and
                              getattr(dataset, 'ClinicalTrialProtocolEthicsCommitteeApprovalNumber', '').strip())
        has_committee_name = (hasattr(dataset, 'ClinicalTrialProtocolEthicsCommitteeName') and
                             getattr(dataset, 'ClinicalTrialProtocolEthicsCommitteeName', '').strip())

        if has_approval_number and not has_committee_name:
            result.add_error(
                "Clinical Trial Protocol Ethics Committee Name (0012,0081) is required (Type 1C) when "
                "Clinical Trial Protocol Ethics Committee Approval Number (0012,0082) is present "
                "per DICOM PS3.3 C.7.1.3. Provide the name of the Ethics Committee, IRB, or IACUC "
                "that issued the approval number."
            )
    
    @staticmethod
    def _validate_sequence_requirements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate sequence structure requirements per DICOM PS3.3 C.7.1.3.

        Validates the Other Clinical Trial Protocol IDs Sequence structure and required sub-attributes.
        """

        # Other Clinical Trial Protocol IDs Sequence (0012,0023) validation
        if hasattr(dataset, 'OtherClinicalTrialProtocolIDsSequence'):
            other_protocol_ids_seq = getattr(dataset, 'OtherClinicalTrialProtocolIDsSequence', [])

            # Check if it's iterable (handles pydicom.Sequence, list, tuple)
            if not hasattr(other_protocol_ids_seq, '__iter__'):
                result.add_error(
                    "Other Clinical Trial Protocol IDs Sequence (0012,0023) must be a sequence "
                    "of Dataset objects per DICOM PS3.3 C.7.1.3"
                )
                return

            for i, item in enumerate(other_protocol_ids_seq):
                # Check that sequence item is a Dataset
                if not isinstance(item, Dataset):
                    result.add_error(
                        f"Other Clinical Trial Protocol IDs Sequence item {i}: "
                        "Sequence items must be pydicom Dataset objects. "
                        "Use ClinicalTrialSubjectModule.create_other_protocol_id_item() to create valid items."
                    )
                    continue

                # Clinical Trial Protocol ID (0012,0020) Type 1 within sequence
                if not hasattr(item, 'ClinicalTrialProtocolID') or not getattr(item, 'ClinicalTrialProtocolID', '').strip():
                    result.add_error(
                        f"Other Clinical Trial Protocol IDs Sequence item {i}: "
                        "Clinical Trial Protocol ID (0012,0020) is required (Type 1) within sequence items "
                        "per DICOM PS3.3 C.7.1.3. Provide a non-empty protocol identifier."
                    )

                # Issuer of Clinical Trial Protocol ID (0012,0022) Type 1 within sequence
                if not hasattr(item, 'IssuerOfClinicalTrialProtocolID') or not getattr(item, 'IssuerOfClinicalTrialProtocolID', '').strip():
                    result.add_error(
                        f"Other Clinical Trial Protocol IDs Sequence item {i}: "
                        "Issuer of Clinical Trial Protocol ID (0012,0022) is required (Type 1) within sequence items "
                        "per DICOM PS3.3 C.7.1.3. Provide the assigning authority for the protocol ID."
                    )

    @staticmethod
    def _validate_value_constraints(dataset: Dataset, result: ValidationResult) -> None:
        """Validate value representation constraints and semantic consistency per DICOM PS3.3 C.7.1.3.

        Validates that all present values conform to DICOM VR requirements and semantic constraints.
        """

        # Validate LO (Long String) VR constraints for all string attributes
        lo_attributes = [
            ('ClinicalTrialSponsorName', '0012,0010'),
            ('ClinicalTrialProtocolID', '0012,0020'),
            ('ClinicalTrialProtocolName', '0012,0021'),
            ('IssuerOfClinicalTrialProtocolID', '0012,0022'),
            ('ClinicalTrialSiteID', '0012,0030'),
            ('ClinicalTrialSiteName', '0012,0031'),
            ('IssuerOfClinicalTrialSiteID', '0012,0032'),
            ('ClinicalTrialSubjectID', '0012,0040'),
            ('IssuerOfClinicalTrialSubjectID', '0012,0041'),
            ('ClinicalTrialSubjectReadingID', '0012,0042'),
            ('IssuerOfClinicalTrialSubjectReadingID', '0012,0043'),
            ('ClinicalTrialProtocolEthicsCommitteeName', '0012,0081'),
            ('ClinicalTrialProtocolEthicsCommitteeApprovalNumber', '0012,0082')
        ]

        for attr_name, tag in lo_attributes:
            if hasattr(dataset, attr_name):
                value = getattr(dataset, attr_name, '')
                if isinstance(value, str) and len(value) > 64:
                    result.add_warning(
                        f"{attr_name} ({tag}) value exceeds 64 characters (LO VR constraint). "
                        f"Current length: {len(value)}. Consider shortening the value for DICOM compliance."
                    )

        # Validate semantic consistency: warn if both subject ID and reading ID are present
        # (allowed but may indicate confusion about intended use)
        if (hasattr(dataset, 'ClinicalTrialSubjectID') and
            hasattr(dataset, 'ClinicalTrialSubjectReadingID') and
            getattr(dataset, 'ClinicalTrialSubjectID', '').strip() and
            getattr(dataset, 'ClinicalTrialSubjectReadingID', '').strip()):
            result.add_warning(
                "Both Clinical Trial Subject ID (0012,0040) and Clinical Trial Subject Reading ID (0012,0042) "
                "are present. While allowed by DICOM PS3.3 C.7.1.3, ensure this is intentional. "
                "Use Subject ID for standard identification and Reading ID for blinded evaluations."
            )
