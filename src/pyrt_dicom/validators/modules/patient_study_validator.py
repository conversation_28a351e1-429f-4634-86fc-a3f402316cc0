"""Patient Study Module DICOM validation - PS3.3 C.7.2.2"""

from pydicom import Dataset
from .base_validator import BaseValidator, ValidationConfig
from ..validation_result import ValidationResult


class PatientStudyValidator:
    """Validator for DICOM Patient Study Module (PS3.3 C.7.2.2)."""
    
    @staticmethod
    def validate(dataset: Dataset, config: ValidationConfig | None = None) -> ValidationResult:
        """Validate Patient Study Module requirements on any pydicom Dataset.
        
        Args:
            dataset: pydicom Dataset to validate
            config: Validation configuration options
            
        Returns:
            ValidationResult with errors and warnings
        """
        config = config or ValidationConfig()
        result = BaseValidator.create_validation_result()
        
        # Validate conditional requirements
        if config.validate_conditional_requirements:
            PatientStudyValidator._validate_conditional_requirements(dataset, result)
        
        # Validate enumerated values
        if config.check_enumerated_values:
            PatientStudyValidator._validate_enumerated_values(dataset, result)
        
        # Validate sequence structures
        if config.validate_sequences:
            PatientStudyValidator._validate_sequence_requirements(dataset, result)
        
        return result
    
    @staticmethod
    def _validate_conditional_requirements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate Type 1C and 2C conditional requirements."""
        
        # Type 2C: Patient's Sex Neutered required for non-human organisms
        is_non_human = (hasattr(dataset, 'PatientSpeciesDescription') or 
                       hasattr(dataset, 'PatientSpeciesCodeSequence'))
        
        if is_non_human and not hasattr(dataset, 'PatientsSexNeutered'):
            result.add_error(
                "Patient's Sex Neutered (0010,2203) is required for non-human organisms"
            )
        
        # Type 2C: Sex Parameters for Clinical Use Category Comment required for "Specified" code
        spcu_seq = getattr(dataset, 'SexParametersForClinicalUseCategorySequence', [])
        for i, item in enumerate(spcu_seq):
            code_seq = item.get('SexParametersForClinicalUseCategoryCodeSequence', [])
            for code_item in code_seq:
                if (code_item.get('CodeValue') == '131232' and 
                    code_item.get('CodingSchemeDesignator') == 'DCM'):
                    if not item.get('SexParametersForClinicalUseCategoryComment'):
                        result.add_error(
                            f"Sex Parameters for Clinical Use Category Sequence item {i}: "
                            "Sex Parameters for Clinical Use Category Comment (0010,0042) is required "
                            "when code is 'Specified'"
                        )
                    if not item.get('SexParametersForClinicalUseCategoryReference'):
                        result.add_error(
                            f"Sex Parameters for Clinical Use Category Sequence item {i}: "
                            "Sex Parameters for Clinical Use Category Reference (0010,0047) is required "
                            "when code is 'Specified'"
                        )

    
    @staticmethod
    def _validate_enumerated_values(dataset: Dataset, result: ValidationResult) -> None:
        """Validate enumerated values against DICOM specifications."""
        
        # Smoking Status (0010,21A0)
        smoking_status = getattr(dataset, 'SmokingStatus', '')
        if smoking_status:
            BaseValidator.validate_enumerated_value(
                smoking_status, ["YES", "NO", "UNKNOWN"],
                "Smoking Status (0010,21A0)", result
            )
        
        # Pregnancy Status (0010,21C0)
        pregnancy_status = getattr(dataset, 'PregnancyStatus', '')
        if pregnancy_status:
            BaseValidator.validate_enumerated_value(
                pregnancy_status, ["0001H", "0002H", "0003H", "0004H"],
                "Pregnancy Status (0010,21C0)", result
            )
        
        # Patient's Sex Neutered (0010,2203)
        sex_neutered = getattr(dataset, 'PatientsSexNeutered', '')
        if sex_neutered:
            BaseValidator.validate_enumerated_value(
                sex_neutered, ["ALTERED", "UNALTERED"],
                "Patient's Sex Neutered (0010,2203)", result
            )

    
    @staticmethod
    def _validate_sequence_requirements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate sequence structure requirements."""
        
        # Gender Identity Sequence validation
        gender_identity_seq = getattr(dataset, 'GenderIdentitySequence', [])
        for i, item in enumerate(gender_identity_seq):
            if not item.get('GenderIdentityCodeSequence'):
                result.add_error(
                    f"Gender Identity Sequence item {i}: "
                    "Gender Identity Code Sequence (0010,0044) is required"
                )
        
        # Sex Parameters for Clinical Use Category Sequence validation
        spcu_seq = getattr(dataset, 'SexParametersForClinicalUseCategorySequence', [])
        for i, item in enumerate(spcu_seq):
            if not item.get('SexParametersForClinicalUseCategoryCodeSequence'):
                result.add_error(
                    f"Sex Parameters for Clinical Use Category Sequence item {i}: "
                    "Sex Parameters for Clinical Use Category Code Sequence (0010,0046) is required"
                )
        
        # Person Names to Use Sequence validation
        names_to_use_seq = getattr(dataset, 'PersonNamesToUseSequence', [])
        for i, item in enumerate(names_to_use_seq):
            if not item.get('NameToUse'):
                result.add_error(
                    f"Person Names to Use Sequence item {i}: "
                    "Name to Use (0010,0012) is required"
                )
        
        # Third Person Pronouns Sequence validation
        pronouns_seq = getattr(dataset, 'ThirdPersonPronounsSequence', [])
        for i, item in enumerate(pronouns_seq):
            if not item.get('PronounCodeSequence'):
                result.add_error(
                    f"Third Person Pronouns Sequence item {i}: "
                    "Pronoun Code Sequence (0010,0015) is required"
                )

