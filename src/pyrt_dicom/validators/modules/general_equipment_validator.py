"""General Equipment Module DICOM validation - PS3.3 C.7.5.1"""

from pydicom import Dataset
from .base_validator import BaseValidator, ValidationConfig
from ..validation_result import ValidationResult


class GeneralEquipmentValidator:
    """Validator for DICOM General Equipment Module (PS3.3 C.7.5.1)."""
    
    @staticmethod
    def validate(dataset: Dataset, config: ValidationConfig | None = None) -> ValidationResult:
        """Validate General Equipment Module requirements on any pydicom Dataset.
        
        Args:
            dataset: pydicom Dataset to validate
            config: Validation configuration options
            
        Returns:
            ValidationResult with errors and warnings
        """
        config = config or ValidationConfig()
        result = BaseValidator.create_validation_result()

        # Validate Type 2 required elements
        GeneralEquipmentValidator._validate_type2_requirements(dataset, result)

        # Validate Type 1C conditional requirements
        if config.validate_conditional_requirements:
            GeneralEquipmentValidator._validate_conditional_requirements(dataset, result)

        # Validate sequence structures
        if config.validate_sequences:
            GeneralEquipmentValidator._validate_sequence_requirements(dataset, result)

        # Validate calibration date/time pairing
        GeneralEquipmentValidator._validate_calibration_pairing(dataset, result)

        return result

    @staticmethod
    def _validate_type2_requirements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate Type 2 (required but may be empty) elements."""

        # Type 2: Manufacturer (0008,0070) - required but may be empty
        if not hasattr(dataset, 'Manufacturer'):
            result.add_error(
                "Manufacturer (0008,0070) is required (Type 2) - must be present but may be empty"
            )

    @staticmethod
    def _validate_conditional_requirements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate Type 1C conditional requirements."""
        
        # Type 1C: Pixel Padding Value required if Pixel Padding Range Limit is present 
        # and either Pixel Data or Pixel Data Provider URL is present
        has_pixel_padding_range_limit = hasattr(dataset, 'PixelPaddingRangeLimit')
        has_pixel_data = hasattr(dataset, 'PixelData') or hasattr(dataset, 'PixelDataProviderURL')
        
        if has_pixel_padding_range_limit and has_pixel_data:
            if not hasattr(dataset, 'PixelPaddingValue'):
                result.add_error(
                    "Pixel Padding Value (0028,0120) is required when Pixel Padding Range Limit (0028,0121) "
                    "is present and either Pixel Data (7FE0,0010) or Pixel Data Provider URL (0028,7FE0) is present"
                )
    
    @staticmethod
    def _validate_sequence_requirements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate sequence structure requirements per DICOM PS3.3 C.7.5.1."""

        # Institutional Department Type Code Sequence (0008,1041) - Type 3
        # Each item requires Code Sequence Macro attributes per Table 8.8-1
        dept_type_seq = getattr(dataset, 'InstitutionalDepartmentTypeCodeSequence', [])
        if dept_type_seq:
            # DICOM standard specifies only a single item is permitted
            if len(dept_type_seq) > 1:
                result.add_warning(
                    f"Institutional Department Type Code Sequence (0008,1041) contains {len(dept_type_seq)} items. "
                    "DICOM PS3.3 C.7.5.1 specifies only a single item is permitted"
                )

            for i, item in enumerate(dept_type_seq):
                if not item.get('CodeValue'):
                    result.add_error(
                        f"Institutional Department Type Code Sequence (0008,1041) item {i}: "
                        "Code Value (0008,0100) is required per Code Sequence Macro"
                    )
                if not item.get('CodingSchemeDesignator'):
                    result.add_error(
                        f"Institutional Department Type Code Sequence (0008,1041) item {i}: "
                        "Coding Scheme Designator (0008,0102) is required per Code Sequence Macro"
                    )
                if not item.get('CodeMeaning'):
                    result.add_error(
                        f"Institutional Department Type Code Sequence (0008,1041) item {i}: "
                        "Code Meaning (0008,0104) is required per Code Sequence Macro"
                    )

        # UDI Sequence (0018,100A) - Type 3
        # Each item requires UDI Macro attributes per Table 10.29-1
        udi_seq = getattr(dataset, 'UDISequence', [])
        for i, item in enumerate(udi_seq):
            if not item.get('UniqueDeviceIdentifier'):
                result.add_error(
                    f"UDI Sequence (0018,100A) item {i}: "
                    "Unique Device Identifier is required per UDI Macro"
                )
    
    @staticmethod
    def _validate_calibration_pairing(dataset: Dataset, result: ValidationResult) -> None:
        """Validate calibration date/time pairing requirements per DICOM PS3.3 C.7.5.1.1.1."""

        # Get calibration attributes, handling both single values and lists
        date_calibration = getattr(dataset, 'DateOfLastCalibration', None)
        time_calibration = getattr(dataset, 'TimeOfLastCalibration', None)

        # Convert single values to lists for consistent processing
        date_list = []
        time_list = []

        if date_calibration is not None:
            # Handle both pydicom MultiValue and regular lists/single values
            if hasattr(date_calibration, '__len__') and not isinstance(date_calibration, str):
                date_list = list(date_calibration)
            else:
                date_list = [date_calibration]

        if time_calibration is not None:
            # Handle both pydicom MultiValue and regular lists/single values
            if hasattr(time_calibration, '__len__') and not isinstance(time_calibration, str):
                time_list = list(time_calibration)
            else:
                time_list = [time_calibration]

        # Time of Last Calibration has no meaning unless Date of Last Calibration is also present
        if time_list and not date_list:
            result.add_warning(
                "Time of Last Calibration (0018,1201) has no meaning unless "
                "Date of Last Calibration (0018,1200) is also present (DICOM PS3.3 C.7.5.1.1.1)"
            )

        # When both are present, they should have the same number of values (paired)
        if date_list and time_list:
            if len(date_list) != len(time_list):
                result.add_warning(
                    f"Date of Last Calibration (0018,1200) has {len(date_list)} values but "
                    f"Time of Last Calibration (0018,1201) has {len(time_list)} values. "
                    "When both are present, they should have the same number of values (paired) "
                    "per DICOM PS3.3 C.7.5.1.1.1"
                )
