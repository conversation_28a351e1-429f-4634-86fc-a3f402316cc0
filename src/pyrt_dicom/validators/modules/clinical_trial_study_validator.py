"""Clinical Trial Study Module DICOM validation - PS3.3 C.7.2.3

Validates Clinical Trial Study Module according to DICOM standard requirements
including Type 1, Type 1C conditional logic for temporal events and consent sequences,
and comprehensive sequence structure validation.

This validator ensures 100% compliance with DICOM PS3.3 C.7.2.3 Clinical Trial Study Module
specifications, providing clear error messages with DICOM tag references to guide users
in creating valid DICOM datasets.
"""

from pydicom import Dataset
from .base_validator import BaseValidator, ValidationConfig
from ..validation_result import ValidationResult


class ClinicalTrialStudyValidator:
    """Validator for DICOM Clinical Trial Study Module (PS3.3 C.7.2.3).

    Provides comprehensive validation of all Clinical Trial Study Module attributes
    according to DICOM standard requirements, with focus on:
    - Type 1 required element validation
    - Type 1C conditional requirement validation
    - Enumerated value validation
    - Sequence structure validation
    - Clear error messages with DICOM tag references
    """

    @staticmethod
    def validate(dataset: Dataset, config: ValidationConfig | None = None) -> ValidationResult:
        """Validate Clinical Trial Study Module requirements on any pydicom Dataset.

        Performs comprehensive validation including Type 1 requirements, conditional logic,
        enumerated values, and sequence structures according to DICOM PS3.3 C.7.2.3.

        Args:
            dataset: pydicom Dataset to validate
            config: Validation configuration options (defaults to ValidationConfig())

        Returns:
            ValidationResult with detailed errors and warnings including DICOM tag references
        """
        config = config or ValidationConfig()
        result = BaseValidator.create_validation_result()

        # Validate Type 1 required elements
        ClinicalTrialStudyValidator._validate_type1_requirements(dataset, result)

        # Validate Type 1C conditional requirements
        if config.validate_conditional_requirements:
            ClinicalTrialStudyValidator._validate_conditional_requirements(dataset, result)

        # Validate enumerated values
        if config.check_enumerated_values:
            ClinicalTrialStudyValidator._validate_enumerated_values(dataset, result)

        # Validate sequence structures
        if config.validate_sequences:
            ClinicalTrialStudyValidator._validate_sequence_requirements(dataset, result)

        return result

    @staticmethod
    def _validate_type1_requirements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate Type 1 (required) elements according to DICOM PS3.3 C.7.2.3.

        Note: Clinical Trial Time Point ID (0012,0050) is Type 2, not Type 1.
        The Clinical Trial Study Module has no Type 1 elements - all elements are
        Type 2 (required but may be empty) or Type 3 (optional).

        Args:
            dataset: Dataset to validate
            result: ValidationResult to update with any errors
        """
        # No Type 1 elements in Clinical Trial Study Module per DICOM PS3.3 C.7.2.3
        # All elements are Type 2 (required but may be empty) or Type 3 (optional)
        pass

    @staticmethod
    def _validate_conditional_requirements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate Type 1C conditional requirements according to DICOM PS3.3 C.7.2.3.

        Args:
            dataset: Dataset to validate
            result: ValidationResult to update with any errors
        """
        # Type 1C: Longitudinal Temporal Event Type (0012,0053) required if
        # Longitudinal Temporal Offset from Event (0012,0052) is present
        BaseValidator.validate_conditional_requirement(
            condition=hasattr(dataset, 'LongitudinalTemporalOffsetFromEvent'),
            required_fields=['LongitudinalTemporalEventType'],
            dataset=dataset,
            error_message=(
                "Longitudinal Temporal Event Type (0012,0053) is required when "
                "Longitudinal Temporal Offset from Event (0012,0052) is present (Type 1C)"
            ),
            result=result
        )
    
    @staticmethod
    def _validate_enumerated_values(dataset: Dataset, result: ValidationResult) -> None:
        """Validate enumerated values against DICOM PS3.3 C.7.2.3 specifications.

        Args:
            dataset: Dataset to validate
            result: ValidationResult to update with any warnings for invalid enum values
        """
        # Longitudinal Temporal Event Type (0012,0053) - Defined Terms: ENROLLMENT, BASELINE
        event_type = getattr(dataset, 'LongitudinalTemporalEventType', '')
        if event_type:
            BaseValidator.validate_enumerated_value(
                value=event_type,
                allowed_values=["ENROLLMENT", "BASELINE"],
                field_name="Longitudinal Temporal Event Type (0012,0053)",
                result=result
            )

        # Validate enumerated values within Consent for Clinical Trial Use Sequence (0012,0083)
        consent_seq = getattr(dataset, 'ConsentForClinicalTrialUseSequence', [])
        for i, item in enumerate(consent_seq):
            # Consent for Distribution Flag (0012,0085) - Enumerated Values: NO, YES, WITHDRAWN
            flag_value = getattr(item, 'ConsentForDistributionFlag', '')
            if flag_value:
                BaseValidator.validate_enumerated_value(
                    value=flag_value,
                    allowed_values=["NO", "YES", "WITHDRAWN"],
                    field_name=f"Consent for Clinical Trial Use Sequence item {i+1}: Consent for Distribution Flag (0012,0085)",
                    result=result
                )

            # Distribution Type (0012,0084) - Defined Terms: NAMED_PROTOCOL, RESTRICTED_REUSE, PUBLIC_RELEASE
            dist_type = getattr(item, 'DistributionType', '')
            if dist_type:
                BaseValidator.validate_enumerated_value(
                    value=dist_type,
                    allowed_values=["NAMED_PROTOCOL", "RESTRICTED_REUSE", "PUBLIC_RELEASE"],
                    field_name=f"Consent for Clinical Trial Use Sequence item {i+1}: Distribution Type (0012,0084)",
                    result=result
                )
    
    @staticmethod
    def _validate_sequence_requirements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate sequence structure requirements according to DICOM PS3.3 C.7.2.3.

        Args:
            dataset: Dataset to validate
            result: ValidationResult to update with any errors for sequence validation issues
        """
        # Validate Consent for Clinical Trial Use Sequence (0012,0083) structure
        ClinicalTrialStudyValidator._validate_consent_sequence(dataset, result)

        # Validate Clinical Trial Time Point Type Code Sequence (0012,0054) structure
        ClinicalTrialStudyValidator._validate_time_point_type_code_sequence(dataset, result)

    @staticmethod
    def _validate_consent_sequence(dataset: Dataset, result: ValidationResult) -> None:
        """Validate Consent for Clinical Trial Use Sequence (0012,0083) structure and conditional logic.

        Args:
            dataset: Dataset to validate
            result: ValidationResult to update with any errors
        """
        consent_seq = getattr(dataset, 'ConsentForClinicalTrialUseSequence', [])
        for i, item in enumerate(consent_seq):
            item_prefix = f"Consent for Clinical Trial Use Sequence item {i}"

            # Type 1: Consent for Distribution Flag (0012,0085) is required within sequence
            if not hasattr(item, 'ConsentForDistributionFlag') or not getattr(item, 'ConsentForDistributionFlag', ''):
                result.add_error(
                    f"{item_prefix}: Consent for Distribution Flag (0012,0085) is required (Type 1)"
                )
                continue  # Skip further validation for this item if required field is missing

            flag_value = getattr(item, 'ConsentForDistributionFlag', '')

            # Type 1C: Distribution Type (0012,0084) required if flag is YES or WITHDRAWN
            if flag_value in ["YES", "WITHDRAWN"]:
                if not hasattr(item, 'DistributionType') or not getattr(item, 'DistributionType', ''):
                    result.add_error(
                        f"{item_prefix}: Distribution Type (0012,0084) is required when "
                        "Consent for Distribution Flag (0012,0085) is YES or WITHDRAWN (Type 1C)"
                    )
                else:
                    dist_type = getattr(item, 'DistributionType', '')

                    # Type 1C: Clinical Trial Protocol ID (0012,0020) required if Distribution Type is NAMED_PROTOCOL
                    if dist_type == "NAMED_PROTOCOL":
                        if not hasattr(item, 'ClinicalTrialProtocolID') or not getattr(item, 'ClinicalTrialProtocolID', ''):
                            result.add_error(
                                f"{item_prefix}: Clinical Trial Protocol ID (0012,0020) is required when "
                                "Distribution Type (0012,0084) is NAMED_PROTOCOL (Type 1C)"
                            )

    @staticmethod
    def _validate_time_point_type_code_sequence(dataset: Dataset, result: ValidationResult) -> None:
        """Validate Clinical Trial Time Point Type Code Sequence (0012,0054) structure.

        Args:
            dataset: Dataset to validate
            result: ValidationResult to update with any errors
        """
        time_point_seq = getattr(dataset, 'ClinicalTrialTimePointTypeCodeSequence', [])
        for i, item in enumerate(time_point_seq):
            item_prefix = f"Clinical Trial Time Point Type Code Sequence item {i}"

            # Validate Code Sequence Macro attributes (Table 8.8-1)
            # These are required when the sequence is present and contains items
            required_code_attrs = ['CodeValue', 'CodingSchemeDesignator', 'CodeMeaning']

            for attr_name in required_code_attrs:
                if not hasattr(item, attr_name) or not getattr(item, attr_name, ''):
                    result.add_error(
                        f"{item_prefix}: {attr_name} is required for Code Sequence Macro"
                    )
