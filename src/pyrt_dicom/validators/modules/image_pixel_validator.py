"""Image Pixel Module DICOM validation - PS3.3 C.7.6.3"""

from pydicom import Dataset
from .base_validator import BaseValidator, ValidationConfig
from ..validation_result import ValidationResult
from ...enums.image_enums import PhotometricInterpretation, PlanarConfiguration, PixelRepresentation


class ImagePixelValidator:
    """Validator for DICOM Image Pixel Module (PS3.3 C.7.6.3)."""
    
    @staticmethod
    def validate(dataset: Dataset, config: ValidationConfig | None = None) -> ValidationResult:
        """Validate Image Pixel Module requirements on any pydicom Dataset.
        
        Args:
            dataset: pydicom Dataset to validate
            config: Validation configuration options
            
        Returns:
            ValidationResult with errors and warnings
        """
        config = config or ValidationConfig()
        result = BaseValidator.create_validation_result()
        
        # Validate Type 1 requirements
        ImagePixelValidator._validate_type1_requirements(dataset, result)
        
        # Validate Type 1C conditional requirements
        if config.validate_conditional_requirements:
            ImagePixelValidator._validate_conditional_requirements(dataset, result)
        
        # Validate enumerated values
        if config.check_enumerated_values:
            ImagePixelValidator._validate_enumerated_values(dataset, result)
        
        # Validate pixel data consistency
        ImagePixelValidator._validate_pixel_data_consistency(dataset, result)
        
        return result
    
    @staticmethod
    def _validate_type1_requirements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate Type 1 (required) attributes."""
        
        required_attrs = [
            ('SamplesPerPixel', '0028,0002'),
            ('PhotometricInterpretation', '0028,0004'),
            ('Rows', '0028,0010'),
            ('Columns', '0028,0011'),
            ('BitsAllocated', '0028,0100'),
            ('BitsStored', '0028,0101'),
            ('HighBit', '0028,0102'),
            ('PixelRepresentation', '0028,0103')
        ]
        
        for attr_name, tag in required_attrs:
            if not hasattr(dataset, attr_name):
                result.add_error(f"{attr_name} ({tag}) is required (Type 1)")
        
        # Validate specific Type 1 constraints
        if hasattr(dataset, 'SamplesPerPixel'):
            if not isinstance(dataset.SamplesPerPixel, int) or dataset.SamplesPerPixel < 1:
                result.add_error("Samples per Pixel (0028,0002) must be a positive integer")
        
        if hasattr(dataset, 'Rows'):
            if not isinstance(dataset.Rows, int) or dataset.Rows < 1:
                result.add_error("Rows (0028,0010) must be a positive integer")
        
        if hasattr(dataset, 'Columns'):
            if not isinstance(dataset.Columns, int) or dataset.Columns < 1:
                result.add_error("Columns (0028,0011) must be a positive integer")
        
        if hasattr(dataset, 'BitsAllocated'):
            if not isinstance(dataset.BitsAllocated, int) or dataset.BitsAllocated < 1:
                result.add_error("Bits Allocated (0028,0100) must be a positive integer")
            elif dataset.BitsAllocated not in [1] and dataset.BitsAllocated % 8 != 0:
                result.add_error("Bits Allocated (0028,0100) must be 1 or a multiple of 8")
        
        if hasattr(dataset, 'BitsStored'):
            if not isinstance(dataset.BitsStored, int) or dataset.BitsStored < 1:
                result.add_error("Bits Stored (0028,0101) must be a positive integer")
        
        if hasattr(dataset, 'HighBit'):
            if not isinstance(dataset.HighBit, int) or dataset.HighBit < 0:
                result.add_error("High Bit (0028,0102) must be a non-negative integer")
        
        # Cross-validation of bit-related attributes
        if all(hasattr(dataset, attr) for attr in ['BitsAllocated', 'BitsStored', 'HighBit']):
            if dataset.BitsStored > dataset.BitsAllocated:
                result.add_error("Bits Stored (0028,0101) cannot exceed Bits Allocated (0028,0100)")
            
            if dataset.HighBit != dataset.BitsStored - 1:
                result.add_error(
                    f"High Bit (0028,0102) must be one less than Bits Stored (0028,0101). "
                    f"Expected {dataset.BitsStored - 1}, got {dataset.HighBit}"
                )
    
    @staticmethod
    def _validate_conditional_requirements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate Type 1C conditional requirements."""
        
        # Pixel Data or Pixel Data Provider URL (one required)
        has_pixel_data = hasattr(dataset, 'PixelData')
        has_pixel_data_url = hasattr(dataset, 'PixelDataProviderURL')
        
        if not has_pixel_data and not has_pixel_data_url:
            result.add_error(
                "Either Pixel Data (7FE0,0010) or Pixel Data Provider URL (0028,7FE0) is required"
            )
        elif has_pixel_data and has_pixel_data_url:
            result.add_warning(
                "Both Pixel Data (7FE0,0010) and Pixel Data Provider URL (0028,7FE0) are present. "
                "Typically only one should be used."
            )
        
        # Planar Configuration (required if Samples per Pixel > 1)
        if hasattr(dataset, 'SamplesPerPixel') and dataset.SamplesPerPixel > 1:
            if not hasattr(dataset, 'PlanarConfiguration'):
                result.add_error(
                    "Planar Configuration (0028,0006) is required when Samples per Pixel (0028,0002) > 1"
                )
        elif hasattr(dataset, 'PlanarConfiguration'):
            result.add_warning(
                "Planar Configuration (0028,0006) should not be present when Samples per Pixel (0028,0002) = 1"
            )
        
        # Palette Color Lookup Tables (required if Photometric Interpretation is PALETTE COLOR)
        if hasattr(dataset, 'PhotometricInterpretation') and dataset.PhotometricInterpretation == "PALETTE COLOR":
            required_palette_attrs = [
                ('RedPaletteColorLookupTableDescriptor', '0028,1101'),
                ('GreenPaletteColorLookupTableDescriptor', '0028,1102'),
                ('BluePaletteColorLookupTableDescriptor', '0028,1103'),
                ('RedPaletteColorLookupTableData', '0028,1201'),
                ('GreenPaletteColorLookupTableData', '0028,1202'),
                ('BluePaletteColorLookupTableData', '0028,1203')
            ]
            
            for attr_name, tag in required_palette_attrs:
                if not hasattr(dataset, attr_name):
                    result.add_error(
                        f"{attr_name} ({tag}) is required when Photometric Interpretation is PALETTE COLOR"
                    )
        
        # Extended Offset Table Lengths (required if Extended Offset Table present)
        if hasattr(dataset, 'ExtendedOffsetTable') and not hasattr(dataset, 'ExtendedOffsetTableLengths'):
            result.add_error(
                "Extended Offset Table Lengths (7FE0,0002) is required when "
                "Extended Offset Table (7FE0,0001) is present"
            )
    
    @staticmethod
    def _validate_enumerated_values(dataset: Dataset, result: ValidationResult) -> None:
        """Validate enumerated values against DICOM standard."""
        
        # Photometric Interpretation
        if hasattr(dataset, 'PhotometricInterpretation'):
            BaseValidator.validate_enumerated_value(
                dataset.PhotometricInterpretation,
                [e.value for e in PhotometricInterpretation],
                "Photometric Interpretation (0028,0004)",
                result
            )
        
        # Planar Configuration
        if hasattr(dataset, 'PlanarConfiguration'):
            BaseValidator.validate_enumerated_value(
                str(dataset.PlanarConfiguration),
                [str(e.value) for e in PlanarConfiguration],
                "Planar Configuration (0028,0006)",
                result
            )
        
        # Pixel Representation
        if hasattr(dataset, 'PixelRepresentation'):
            BaseValidator.validate_enumerated_value(
                str(dataset.PixelRepresentation),
                [str(e.value) for e in PixelRepresentation],
                "Pixel Representation (0028,0103)",
                result
            )
    
    @staticmethod
    def _validate_pixel_data_consistency(dataset: Dataset, result: ValidationResult) -> None:
        """Validate pixel data consistency and constraints."""
        
        # Validate photometric interpretation constraints
        if hasattr(dataset, 'PhotometricInterpretation') and hasattr(dataset, 'SamplesPerPixel'):
            photometric = dataset.PhotometricInterpretation
            samples = dataset.SamplesPerPixel
            
            if photometric in ["MONOCHROME1", "MONOCHROME2", "PALETTE COLOR"] and samples != 1:
                result.add_error(
                    f"Photometric Interpretation '{photometric}' requires Samples per Pixel = 1, got {samples}"
                )
            elif photometric in ["RGB", "YBR_FULL", "YBR_FULL_422", "YBR_PARTIAL_420", "YBR_ICT", "YBR_RCT", "XYB"] and samples != 3:
                result.add_error(
                    f"Photometric Interpretation '{photometric}' requires Samples per Pixel = 3, got {samples}"
                )
        
        # Validate YBR_FULL_422 constraints
        if hasattr(dataset, 'PhotometricInterpretation') and dataset.PhotometricInterpretation == "YBR_FULL_422":
            if hasattr(dataset, 'PlanarConfiguration') and dataset.PlanarConfiguration != 0:
                result.add_error(
                    "YBR_FULL_422 Photometric Interpretation requires Planar Configuration = 0"
                )
            
            # Check if columns is even (required for horizontal subsampling)
            if hasattr(dataset, 'Columns') and dataset.Columns % 2 != 0:
                result.add_error(
                    "YBR_FULL_422 Photometric Interpretation requires even number of Columns for horizontal subsampling"
                )
        
        # Validate palette color descriptor consistency
        if hasattr(dataset, 'PhotometricInterpretation') and dataset.PhotometricInterpretation == "PALETTE COLOR":
            descriptors = []
            for color in ['Red', 'Green', 'Blue']:
                attr_name = f'{color}PaletteColorLookupTableDescriptor'
                if hasattr(dataset, attr_name):
                    descriptors.append(getattr(dataset, attr_name))
            
            if len(descriptors) == 3:
                # Check if all descriptors have same first and second values
                if not all(desc[0] == descriptors[0][0] for desc in descriptors):
                    result.add_error(
                        "All Palette Color Lookup Table Descriptors must have the same first value (number of entries)"
                    )
                
                if not all(desc[1] == descriptors[0][1] for desc in descriptors):
                    result.add_error(
                        "All Palette Color Lookup Table Descriptors must have the same second value (first input value)"
                    )
