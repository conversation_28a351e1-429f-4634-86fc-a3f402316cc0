"""General Image Module DICOM validation - PS3.3 C.7.6.1

Validates General Image Module attributes according to DICOM PS3.3 C.7.6.1 specification.
Provides comprehensive validation of Type 1, Type 2, Type 2C, and Type 3 elements
with detailed error messages and DICOM standard references.

This validator ensures 100% DICOM standard compliance for General Image Module
attributes and provides actionable error messages to guide users toward
compliant implementations.
"""

from pydicom import Dataset
from pydicom.multival import MultiValue
from .base_validator import BaseValidator, ValidationConfig
from ..validation_result import ValidationResult
from ...enums.image_enums import (
    QualityControlImage, BurnedInAnnotation, RecognizableVisualFeatures,
    LossyImageCompression, PresentationLUTShape, ImageLaterality,
    LossyImageCompressionMethod
)


class GeneralImageValidator:
    """Validator for DICOM General Image Module (PS3.3 C.7.6.1).

    Provides comprehensive validation of all General Image Module attributes
    according to DICOM PS3.3 C.7.6.1 specification. This validator ensures
    100% compliance with DICOM standards and provides actionable error messages
    to guide users toward compliant implementations.

    Validation Coverage:
    - Type 2 elements: Instance Number (required, may be empty)
    - Type 2C elements: Patient Orientation, Content Date/Time (conditional requirements)
    - Type 3 elements: All optional attributes with enumerated value checking
    - Sequence validation: Icon Image Sequence, Real World Value Mapping Sequence
    - Cross-field validation: Lossy compression attribute consistency
    - Format validation: Image Type structure, Patient Orientation format
    - Conditional logic: Spatial orientation requirements, temporal consistency

    Error Message Standards:
    - Include DICOM tag references (e.g., "(0020,0013)")
    - Reference DICOM PS3.3 sections for complex requirements
    - Provide actionable guidance for resolving issues
    - Explain the clinical/technical context of requirements
    - Suggest valid values for enumerated attributes

    Usage:
        # Basic validation
        result = GeneralImageValidator.validate(dataset)
        
        # Validation with custom configuration
        config = ValidationConfig(
            validate_conditional_requirements=True,
            check_enumerated_values=True,
            validate_sequences=True
        )
        result = GeneralImageValidator.validate(dataset, config)
        
        # Process validation results
        if not result.is_valid:
            for error in result.errors:
                print(f"ERROR: {error}")
            for warning in result.warnings:
                print(f"WARNING: {warning}")
    """

    @staticmethod
    def validate(dataset: Dataset, config: ValidationConfig | None = None) -> ValidationResult:
        """Validate General Image Module requirements on any pydicom Dataset.

        Performs comprehensive validation of all DICOM PS3.3 C.7.6.1 requirements
        with intelligent conditional logic and detailed error reporting.

        This method validates:
        1. Type 2 required elements (Instance Number)
        2. Type 2C conditional requirements with context-aware validation
        3. Type 3 optional elements with enumerated value checking
        4. Complex sequence structures and their required sub-attributes
        5. Cross-field consistency (e.g., lossy compression attributes)
        6. DICOM format compliance (multi-value fields, date/time formats)
        7. Clinical workflow requirements (spatial orientation, temporal relationships)

        Args:
            dataset (Dataset): pydicom Dataset containing General Image Module data.
                             Must be a valid pydicom Dataset object.
            config (ValidationConfig | None): Optional validation configuration to control
                                             validation scope and behavior. If None, uses
                                             default configuration with all validations enabled.

        Returns:
            ValidationResult: Comprehensive validation result with structured error and warning lists.
                            Each message includes DICOM tag references, standard citations,
                            and actionable guidance for resolution.
                            
        Raises:
            TypeError: If dataset is not a valid pydicom Dataset
            
        Examples:
            # Validate minimal dataset
            dataset = Dataset()
            dataset.InstanceNumber = "1"
            result = GeneralImageValidator.validate(dataset)
            
            # Validate with spatial orientation
            dataset.PatientOrientation = ["A", "F"]
            result = GeneralImageValidator.validate(dataset)
            
            # Validate temporal elements
            dataset.ContentDate = "20240315"
            dataset.ContentTime = "143022.123"
            result = GeneralImageValidator.validate(dataset)
        """
        config = config or ValidationConfig()
        result = BaseValidator.create_validation_result()

        # Validate Type 2 required elements (always validated)
        GeneralImageValidator._validate_type2_elements(dataset, result)

        # Validate Type 2C conditional requirements
        if config.validate_conditional_requirements:
            GeneralImageValidator._validate_conditional_requirements(dataset, result)

        # Validate Type 3 enumerated values
        if config.check_enumerated_values:
            GeneralImageValidator._validate_enumerated_values(dataset, result)

        # Validate sequence structures and format requirements
        if config.validate_sequences:
            GeneralImageValidator._validate_sequence_requirements(dataset, result)
            GeneralImageValidator._validate_real_world_value_mapping_sequence(dataset, result)

        # Validate cross-field consistency and relationships
        if config.validate_conditional_requirements:
            GeneralImageValidator._validate_lossy_compression_consistency(dataset, result)
            GeneralImageValidator._validate_image_type_consistency(dataset, result)

        return result

    @staticmethod
    def _validate_type2_elements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate Type 2 (required but may be empty) elements.

        Per DICOM PS3.3 C.7.6.1, Instance Number is the only Type 2 element.
        Type 2 elements must be present but may contain an empty value.
        """
        # Instance Number (0020,0013) - Type 2
        if not hasattr(dataset, 'InstanceNumber'):
            result.add_error(
                "Instance Number (0020,0013) is required (Type 2). "
                "This attribute identifies this image within its Series and must be present "
                "even if empty. Previously named 'Image Number' in earlier DICOM versions. "
                "To fix: Add dataset.InstanceNumber = '1' or appropriate sequence number. "
                "See DICOM PS3.3 C.7.6.1 for requirements."
            )
        else:
            # Validate Instance Number format (VR = IS - Integer String)
            try:
                instance_number = str(dataset.InstanceNumber)
                if instance_number and not instance_number.isdigit():
                    result.add_warning(
                        f"Instance Number (0020,0013) should contain only digits for VR IS compliance, "
                        f"got '{instance_number}'. While pydicom may accept this, strict DICOM compliance "
                        f"requires integer string format. See DICOM PS3.5 for VR IS definition."
                    )
            except (ValueError, TypeError):
                # If pydicom throws an error accessing the value, it means there's an invalid IS value
                # Try to access the raw value from the DataElement
                try:
                    data_element = dataset.get_item(0x0020, 0x0013)
                    if data_element and hasattr(data_element, 'value'):
                        raw_value = str(data_element.value)
                        if raw_value and not raw_value.isdigit():
                            result.add_warning(
                                f"Instance Number (0020,0013) should contain only digits for VR IS compliance, "
                                f"got '{raw_value}'. While pydicom may accept this, strict DICOM compliance "
                                f"requires integer string format. See DICOM PS3.5 for VR IS definition."
                            )
                except Exception:
                    # If we can't access the value at all, just note the issue
                    result.add_warning(
                        "Instance Number (0020,0013) contains a value that cannot be validated "
                        "for VR IS compliance. Ensure the value contains only digits."
                    )

    @staticmethod
    def _validate_image_type_structure(dataset: Dataset, result: ValidationResult) -> None:
        """Validate Image Type multi-value structure per DICOM PS3.3 C.7.6.1.1.2."""
        image_type = dataset.ImageType

        # Check if it's a multi-value field (list or MultiValue)
        if isinstance(image_type, str):
            result.add_error(
                "Image Type (0008,0008) must be multi-valued. "
                "See DICOM PS3.3 C.7.6.1.1.2."
            )
            return

        # Accept both list and MultiValue types
        if not isinstance(image_type, (list, MultiValue)):
            result.add_error(
                "Image Type (0008,0008) must be multi-valued. "
                "See DICOM PS3.3 C.7.6.1.1.2."
            )
            return

        if len(image_type) < 2:
            result.add_error(
                f"Image Type (0008,0008) must contain at least 2 values, got {len(image_type)}. "
                "Value 1: Pixel Data Characteristics, Value 2: Patient Examination Characteristics. "
                "See DICOM PS3.3 C.7.6.1.1.2."
            )
            return

        # Value 1 - Pixel Data Characteristics
        if image_type[0] not in ["ORIGINAL", "DERIVED"]:
            result.add_error(
                f"Image Type (0008,0008) Value 1 (Pixel Data Characteristics) has invalid value '{image_type[0]}'. "
                "Valid values: ORIGINAL (original/source data), DERIVED (derived from other images). "
                "See DICOM PS3.3 C.7.6.1.1.2."
            )

        # Value 2 - Patient Examination Characteristics
        if image_type[1] not in ["PRIMARY", "SECONDARY"]:
            result.add_error(
                f"Image Type (0008,0008) Value 2 (Patient Examination Characteristics) has invalid value '{image_type[1]}'. "
                "Valid values: PRIMARY (direct result of examination), SECONDARY (created after examination). "
                "See DICOM PS3.3 C.7.6.1.1.2."
            )
    
    @staticmethod
    def _validate_conditional_requirements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate Type 2C conditional requirements per DICOM PS3.3 C.7.6.1.

        Validates all conditional requirements including:
        - Patient Orientation (required if no spatial orientation from other modules)
        - Content Date/Time (required if part of temporally related series)
        """

        # Type 2C: Patient Orientation conditional requirement
        GeneralImageValidator._validate_patient_orientation_requirement(dataset, result)

        # Type 2C: Content Date and Content Time for temporally related series
        GeneralImageValidator._validate_temporal_requirements(dataset, result)

    @staticmethod
    def _validate_patient_orientation_requirement(dataset: Dataset, result: ValidationResult) -> None:
        """Validate Patient Orientation Type 2C conditional requirement.

        Per DICOM PS3.3 C.7.6.1: Patient Orientation is required if image does not require
        Image Orientation (Patient) (0020,0037) and Image Position (Patient) (0020,0032)
        or if image does not require Image Orientation (Slide) (0048,0102).
        """
        has_patient_orientation = hasattr(dataset, 'PatientOrientation')
        has_image_orientation_patient = hasattr(dataset, 'ImageOrientationPatient')
        has_image_position_patient = hasattr(dataset, 'ImagePositionPatient')
        has_image_orientation_slide = hasattr(dataset, 'ImageOrientationSlide')

        # Check if image has spatial orientation information from other modules
        has_spatial_orientation = (
            (has_image_orientation_patient and has_image_position_patient) or
            has_image_orientation_slide
        )

        # If no spatial orientation from other modules, Patient Orientation should be present
        if not has_spatial_orientation and not has_patient_orientation:
            result.add_error(
                "Patient Orientation (0020,0020) is required (Type 2C) when image does not have "
                "Image Orientation (Patient) (0020,0037) with Image Position (Patient) (0020,0032) "
                "or Image Orientation (Slide) (0048,0102). "
                "Patient Orientation specifies anatomical direction of rows and columns. "
                "See DICOM PS3.3 C.7.6.1.1.1 for format requirements."
            )

        # Validate Patient Orientation format when present
        if has_patient_orientation:
            GeneralImageValidator._validate_patient_orientation_format(dataset, result)

    @staticmethod
    def _validate_patient_orientation_format(dataset: Dataset, result: ValidationResult) -> None:
        """Validate Patient Orientation format requirements."""
        orientation = dataset.PatientOrientation

        # Should be 2 values when not empty
        if orientation != "" and orientation != []:
            if isinstance(orientation, str) and orientation != "":
                # Single string should not be used for non-empty orientation
                result.add_warning(
                    "Patient Orientation (0020,0020) should contain two values designating "
                    "row and column directions when not empty. See DICOM PS3.3 C.7.6.1.1.1."
                )
            elif isinstance(orientation, (list, MultiValue)) and len(orientation) != 2:
                result.add_error(
                    f"Patient Orientation (0020,0020) must contain exactly 2 values, got {len(orientation)}. "
                    "See DICOM PS3.3 C.7.6.1.1.1."
                )

    @staticmethod
    def _validate_temporal_requirements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate temporal element requirements for temporally related series.

        Per DICOM PS3.3 C.7.6.1: Content Date and Content Time are Type 2C -
        required if image is part of a Series in which the images are temporally related.
        """
        has_content_date = hasattr(dataset, 'ContentDate')
        has_content_time = hasattr(dataset, 'ContentTime')

        if has_content_date and not has_content_time:
            result.add_error(
                "Content Time (0008,0033) is required (Type 2C) when Content Date (0008,0023) is present "
                "for temporally related series. Both date and time should be provided together. "
                "See DICOM PS3.3 C.7.6.1."
            )
        elif has_content_time and not has_content_date:
            result.add_error(
                "Content Date (0008,0023) is required (Type 2C) when Content Time (0008,0033) is present "
                "for temporally related series. Both date and time should be provided together. "
                "See DICOM PS3.3 C.7.6.1."
            )
    
    @staticmethod
    def _validate_enumerated_values(dataset: Dataset, result: ValidationResult) -> None:
        """Validate enumerated values against DICOM standard."""
        
        # Quality Control Image
        if hasattr(dataset, 'QualityControlImage'):
            valid_values = [e.value for e in QualityControlImage]
            if dataset.QualityControlImage not in valid_values:
                result.add_error(
                    f"Quality Control Image (0028,0300) has invalid value '{dataset.QualityControlImage}'. "
                    f"Valid values: {valid_values}. Indicates presence of quality control material. "
                    "See DICOM PS3.3 C.7.6.1."
                )
        
        # Burned In Annotation
        if hasattr(dataset, 'BurnedInAnnotation'):
            valid_values = [e.value for e in BurnedInAnnotation]
            if dataset.BurnedInAnnotation not in valid_values:
                result.add_error(
                    f"Burned In Annotation (0028,0301) has invalid value '{dataset.BurnedInAnnotation}'. "
                    f"Valid values: {valid_values}. Indicates sufficient annotation to identify patient. "
                    "See DICOM PS3.3 C.7.6.1."
                )
        
        # Recognizable Visual Features
        if hasattr(dataset, 'RecognizableVisualFeatures'):
            valid_values = [e.value for e in RecognizableVisualFeatures]
            if dataset.RecognizableVisualFeatures not in valid_values:
                result.add_error(
                    f"Recognizable Visual Features (0028,0302) has invalid value '{dataset.RecognizableVisualFeatures}'. "
                    f"Valid values: {valid_values}. Indicates if image contains recognizable visual features "
                    "that could identify the patient. See DICOM PS3.3 C.7.6.1."
                )
        
        # Lossy Image Compression
        if hasattr(dataset, 'LossyImageCompression'):
            valid_values = [e.value for e in LossyImageCompression]
            if dataset.LossyImageCompression not in valid_values:
                result.add_error(
                    f"Lossy Image Compression (0028,2110) has invalid value '{dataset.LossyImageCompression}'. "
                    f"Valid values: {valid_values}. Once set to '01', shall not be reset. "
                    "See DICOM PS3.3 C.7.6.1.1.5."
                )
        
        # Presentation LUT Shape
        if hasattr(dataset, 'PresentationLUTShape'):
            valid_values = [e.value for e in PresentationLUTShape]
            if dataset.PresentationLUTShape not in valid_values:
                result.add_error(
                    f"Presentation LUT Shape (2050,0020) has invalid value '{dataset.PresentationLUTShape}'. "
                    f"Valid values: {valid_values}. Specifies identity transformation for Presentation LUT. "
                    "See DICOM PS3.3 C.7.6.1."
                )

        # Image Laterality
        if hasattr(dataset, 'ImageLaterality'):
            valid_values = [e.value for e in ImageLaterality]
            if dataset.ImageLaterality not in valid_values:
                result.add_error(
                    f"Image Laterality (0020,0062) has invalid value '{dataset.ImageLaterality}'. "
                    f"Valid values: {valid_values}. Laterality of (possibly paired) body part examined. "
                    "Must be consistent with Primary Anatomic Structure Modifier Sequence and Laterality (0020,0060). "
                    "See DICOM PS3.3 C.7.6.1."
                )
        
        # Lossy Image Compression Method
        if hasattr(dataset, 'LossyImageCompressionMethod'):
            valid_values = [e.value for e in LossyImageCompressionMethod]
            methods = dataset.LossyImageCompressionMethod
            if isinstance(methods, str):
                methods = [methods]
            for method in methods:
                if method not in valid_values:
                    result.add_error(
                        f"Lossy Image Compression Method (0028,2114) has invalid value '{method}'. "
                        f"Valid values: {valid_values}. Label for lossy compression method(s) applied. "
                        "Order should correspond to Lossy Image Compression Ratio values. "
                        "See DICOM PS3.3 C.7.6.1.1.5.1."
                    )
        
        # Image Type validation
        if hasattr(dataset, 'ImageType'):
            GeneralImageValidator._validate_image_type_structure(dataset, result)
    
    @staticmethod
    def _validate_sequence_requirements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate sequence structure requirements.

        Validates Icon Image Sequence structure and required attributes
        according to DICOM PS3.3 C.7.6.1.1.6.
        """

        # Icon Image Sequence validation
        if hasattr(dataset, 'IconImageSequence'):
            if len(dataset.IconImageSequence) > 1:
                result.add_error(
                    "Icon Image Sequence (0088,0200) may contain only a single Item. "
                    "Icon image is representative of the Image. See DICOM PS3.3 C.7.6.1.1.6."
                )
            
            if len(dataset.IconImageSequence) == 1:
                icon_item = dataset.IconImageSequence[0]
                
                # Required attributes for icon image
                required_attrs = ['Rows', 'Columns', 'SamplesPerPixel', 'PhotometricInterpretation',
                                'BitsAllocated', 'BitsStored', 'HighBit', 'PixelRepresentation']
                
                for attr in required_attrs:
                    if not hasattr(icon_item, attr):
                        result.add_error(
                            f"Icon Image Sequence item missing required attribute {attr}. "
                            "Icon images must include Image Pixel Macro attributes. "
                            "See DICOM PS3.3 C.7.6.1.1.6."
                        )

                # Validate icon image constraints per DICOM PS3.3 C.7.6.1.1.6
                if hasattr(icon_item, 'SamplesPerPixel') and icon_item.SamplesPerPixel != 1:
                    if hasattr(icon_item, 'PhotometricInterpretation'):
                        if icon_item.PhotometricInterpretation not in ["MONOCHROME1", "MONOCHROME2", "PALETTE COLOR"]:
                            result.add_error(
                                "Icon Image must use monochrome or palette color photometric interpretation. "
                                "Only MONOCHROME1, MONOCHROME2, or PALETTE COLOR are supported. "
                                "True color icon images are not supported. See DICOM PS3.3 C.7.6.1.1.6."
                            )

                if hasattr(icon_item, 'BitsAllocated'):
                    if icon_item.BitsAllocated not in [1, 8]:
                        result.add_error(
                            f"Icon Image Bits Allocated must be 1 or 8, got {icon_item.BitsAllocated}. "
                            "See DICOM PS3.3 C.7.6.1.1.6."
                        )
    
    @staticmethod
    def _validate_lossy_compression_consistency(dataset: Dataset, result: ValidationResult) -> None:
        """Validate lossy compression attribute consistency."""
        
        has_lossy_compression = hasattr(dataset, 'LossyImageCompression')
        has_compression_ratio = hasattr(dataset, 'LossyImageCompressionRatio')
        has_compression_method = hasattr(dataset, 'LossyImageCompressionMethod')
        
        # If lossy compression is "01", ratio and method should be present
        if has_lossy_compression and dataset.LossyImageCompression == "01":
            if not has_compression_ratio:
                result.add_warning(
                    "Lossy Image Compression Ratio (0028,2112) should be present when "
                    "Lossy Image Compression (0028,2110) is '01'. This provides important "
                    "information about the degree of compression applied. "
                    "To fix: Add dataset.LossyImageCompressionRatio = [2.5] or appropriate ratio value(s)."
                )
            if not has_compression_method:
                result.add_warning(
                    "Lossy Image Compression Method (0028,2114) should be present when "
                    "Lossy Image Compression (0028,2110) is '01'. This identifies the specific "
                    "compression algorithm used. "
                    "To fix: Add dataset.LossyImageCompressionMethod = ['ISO_10918_1'] or appropriate method."
                )
        
        # If ratio and method are both present, they should have corresponding values
        if has_compression_ratio and has_compression_method:
            ratio_data = dataset.LossyImageCompressionRatio
            method_data = dataset.LossyImageCompressionMethod
            
            # Handle both list/MultiValue and single values
            ratio_count = len(ratio_data) if hasattr(ratio_data, '__len__') and not isinstance(ratio_data, str) else 1
            method_count = len(method_data) if hasattr(method_data, '__len__') and not isinstance(method_data, str) else 1
            
            if ratio_count != method_count:
                result.add_warning(
                    f"Lossy Image Compression Ratio (0028,2112) and Method (0028,2114) "
                    f"should have corresponding number of values. Found {ratio_count} ratio(s) "
                    f"and {method_count} method(s). The order of values should correspond "
                    f"to successive lossy compression steps applied to the image. "
                    f"See DICOM PS3.3 C.7.6.1.1.5.2."
                )

    @staticmethod
    def _validate_real_world_value_mapping_sequence(dataset: Dataset, result: ValidationResult) -> None:
        """Validate Real World Value Mapping Sequence structure."""
        if not hasattr(dataset, 'RealWorldValueMappingSequence'):
            return

        sequence = dataset.RealWorldValueMappingSequence
        # pydicom sequences are already lists, so this check is not needed
        # if not isinstance(sequence, list):
        #     result.add_error(
        #         "Real World Value Mapping Sequence (0040,9096) must be a sequence (list)"
        #     )
        #     return

        for i, item in enumerate(sequence):
            if not hasattr(item, 'RealWorldValueMappingSequence') and \
               not hasattr(item, 'RealWorldValueFirstValueMapped') and \
               not hasattr(item, 'RealWorldValueLastValueMapped') and \
               not hasattr(item, 'RealWorldValueLUTData'):
                result.add_warning(
                    f"Real World Value Mapping Sequence item {i+1} appears to be empty. "
                    f"Empty mapping items provide no value conversion information. "
                    f"Consider including mapping attributes such as RealWorldValueFirstValueMapped, "
                    f"RealWorldValueLastValueMapped, or RealWorldValueLUTData for proper "
                    f"real world value conversion. See DICOM PS3.3 C.7.6.16-12b for macro definition."
                )

    @staticmethod
    def _validate_image_type_consistency(dataset: Dataset, result: ValidationResult) -> None:
        """Validate Image Type consistency and derived image requirements.
        
        Per DICOM PS3.3 C.7.6.1.1.2, derived images should have different SOP Instance UID
        if pixel data differences affect professional interpretation.
        """
        if not hasattr(dataset, 'ImageType'):
            return
            
        image_type = dataset.ImageType
        # Handle both list/tuple and pydicom MultiValue objects
        if isinstance(image_type, (list, tuple, MultiValue)) and len(image_type) >= 1:
            if image_type[0] == "DERIVED":
                # For derived images, provide guidance about SOP Instance UID requirements
                result.add_warning(
                    "Image Type indicates DERIVED image. Per DICOM PS3.3 C.7.6.1.1.2, "
                    "derived images should have a SOP Instance UID different from all source images "
                    "if pixel data differences are expected to affect professional interpretation. "
                    "This validation cannot check SOP Instance UID uniqueness without source image context. "
                    "Ensure derived images have unique SOP Instance UIDs when pixel data changes affect interpretation."
                )
