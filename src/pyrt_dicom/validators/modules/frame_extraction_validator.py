"""Frame Extraction Module Validator - DICOM PS3.3 C.12.3 validation."""

from pydicom import Dataset
from .base_validator import BaseValidator, ValidationConfig
from ..validation_result import ValidationResult


class FrameExtractionValidator(BaseValidator):
    """Validator for Frame Extraction Module requirements.
    
    Validates DICOM PS3.3 C.12.3 Frame Extraction Module compliance including:
    - Required Frame Extraction Sequence with at least one item
    - Required Multi-frame Source SOP Instance UID in each sequence item
    - Conditional requirement that exactly one frame extraction method is present:
      * Simple Frame List (0008,1161) - direct list of frame numbers
      * Calculated Frame List (0008,1162) - triplets of start, end, increment  
      * Time Range (0008,1163) - start and end times as floating point values
    """
    
    @staticmethod
    def validate(dataset: Dataset, config: ValidationConfig | None = None) -> ValidationResult:
        """Validate Frame Extraction Module requirements on any pydicom Dataset.
        
        Args:
            dataset: pydicom Dataset to validate
            config: Validation configuration options
            
        Returns:
            ValidationResult with errors and warnings
        """
        config = config or ValidationConfig()
        result = BaseValidator.create_validation_result()
        
        # Validate Type 1 requirements
        FrameExtractionValidator._validate_required_elements(dataset, result)
        
        # Validate sequence structure
        if config.validate_sequences and hasattr(dataset, 'FrameExtractionSequence'):
            FrameExtractionValidator._validate_sequence_structure(dataset, result)
        
        return result
    
    @staticmethod
    def _validate_required_elements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate Type 1 required elements."""
        if not hasattr(dataset, 'FrameExtractionSequence'):
            result.add_error(
                "Missing required Frame Extraction Sequence (0008,1164). "
                "This Type 1 element is mandatory for all Frame Extraction modules. "
                "Add at least one sequence item using FrameExtractionModule.create_frame_extraction_item()."
            )
            return
        
        sequence = dataset.FrameExtractionSequence
        if len(sequence) == 0:
            result.add_error(
                "Frame Extraction Sequence (0008,1164) is empty but must contain at least one item. "
                "Each item describes how frames were extracted from a source multi-frame SOP Instance. "
                "Use FrameExtractionModule.create_frame_extraction_item() to add extraction details."
            )
    
    @staticmethod
    def _validate_sequence_structure(dataset: Dataset, result: ValidationResult) -> None:
        """Validate Frame Extraction Sequence structure and content."""
        if not hasattr(dataset, 'FrameExtractionSequence'):
            return
        
        sequence = dataset.FrameExtractionSequence
        
        for i, item in enumerate(sequence):
            item_prefix = f"FrameExtractionSequence item {i+1}"
            
            # Validate required Multi-frame Source SOP Instance UID
            if not hasattr(item, 'MultiFrameSourceSOPInstanceUID'):
                result.add_error(
                    f"{item_prefix}: Missing required Multi-frame Source SOP Instance UID (0008,1167). "
                    f"This Type 1 element identifies the source multi-frame SOP Instance from which frames were extracted. "
                    f"Provide a valid DICOM UID when creating the frame extraction item."
                )
            else:
                # Validate UID format
                uid = str(item.MultiFrameSourceSOPInstanceUID)
                if not FrameExtractionValidator._is_valid_uid(uid):
                    result.add_error(
                        f"{item_prefix}: Invalid Multi-frame Source SOP Instance UID (0008,1167) format: '{uid}'. "
                        f"UIDs must consist of numeric components separated by periods (e.g., '1.2.3.4.5.6.7.8.9'). "
                        f"Each component must be a number without leading zeros (except '0' itself)."
                    )
            
            # Validate conditional frame list requirements (exactly one must be present)
            frame_list_fields = ['SimpleFrameList', 'CalculatedFrameList', 'TimeRange']
            present_fields = [field for field in frame_list_fields if hasattr(item, field)]
            
            if len(present_fields) == 0:
                result.add_error(
                    f"{item_prefix}: Missing frame extraction method. Exactly one of the following Type 1C elements "
                    f"must be present: Simple Frame List (0008,1161) for direct frame numbers, "
                    f"Calculated Frame List (0008,1162) for triplet-based selection, or Time Range (0008,1163) "
                    f"for time-based frame extraction. Use FrameExtractionModule.create_frame_extraction_item() "
                    f"with the appropriate parameter (simple_frame_list, calculated_frame_list, or time_range)."
                )
            elif len(present_fields) > 1:
                result.add_error(
                    f"{item_prefix}: Multiple frame extraction methods found: {', '.join(present_fields)}. "
                    f"According to DICOM PS3.3 C.12.3, exactly one extraction method must be present per sequence item. "
                    f"Choose either Simple Frame List (0008,1161), Calculated Frame List (0008,1162), or Time Range (0008,1163) "
                    f"but not multiple methods in the same item."
                )
            else:
                # Validate the specific frame list type
                field = present_fields[0]
                if field == 'SimpleFrameList':
                    FrameExtractionValidator._validate_simple_frame_list(item, result, item_prefix)
                elif field == 'CalculatedFrameList':
                    FrameExtractionValidator._validate_calculated_frame_list(item, result, item_prefix)
                elif field == 'TimeRange':
                    FrameExtractionValidator._validate_time_range(item, result, item_prefix)
    
    @staticmethod
    def _validate_simple_frame_list(item, result: ValidationResult, item_prefix: str) -> None:
        """Validate Simple Frame List format."""
        if not hasattr(item, 'SimpleFrameList'):
            return
        
        frame_list = item.SimpleFrameList
        
        # Check if it's a string (which pydicom treats as iterable but isn't a proper list)
        if isinstance(frame_list, str):
            result.add_error(
                f"{item_prefix}: Simple Frame List (0008,1161) must be a list of integers, not a string. "
                f"Provide frame numbers as a list, e.g., [1, 3, 5, 7] rather than '1357'."
            )
            return
        
        # Check if it's a single integer (pydicom converts single-element lists to scalars)
        if isinstance(frame_list, (int, float)):
            frame_list = [frame_list]  # Convert back to list for validation
        
        # Convert to list if it's a pydicom MultiValue or other sequence type
        try:
            frame_list = list(frame_list)
        except (TypeError, ValueError):
            result.add_error(f"{item_prefix}: Simple Frame List (0008,1161) must be a list")
            return
        
        if len(frame_list) == 0:
            result.add_error(f"{item_prefix}: Simple Frame List (0008,1161) must contain at least one frame number")
            return
        
        # Validate frame numbers are positive integers
        for j, frame_num in enumerate(frame_list):
            try:
                frame_int = int(frame_num)
                if frame_int <= 0:
                    result.add_error(f"{item_prefix}: Simple Frame List (0008,1161) frame {j+1} must be positive, found {frame_int}")
            except (ValueError, TypeError):
                result.add_error(f"{item_prefix}: Simple Frame List (0008,1161) frame {j+1} must be an integer, found {frame_num}")
    
    @staticmethod
    def _validate_calculated_frame_list(item, result: ValidationResult, item_prefix: str) -> None:
        """Validate Calculated Frame List format (triplets of start, end, increment)."""
        if not hasattr(item, 'CalculatedFrameList'):
            return
        
        frame_list = item.CalculatedFrameList
        
        # Check if it's a string (which pydicom treats as iterable but isn't a proper list)
        if isinstance(frame_list, str):
            result.add_error(f"{item_prefix}: Calculated Frame List (0008,1162) must be a list")
            return
        
        # Check if it's a single integer (pydicom converts single-element lists to scalars)
        if isinstance(frame_list, (int, float)):
            frame_list = [frame_list]  # Convert back to list for validation
        
        # Convert to list if it's a pydicom MultiValue or other sequence type
        try:
            frame_list = list(frame_list)
        except (TypeError, ValueError):
            result.add_error(f"{item_prefix}: Calculated Frame List (0008,1162) must be a list")
            return
        
        if len(frame_list) % 3 != 0:
            result.add_error(
                f"{item_prefix}: Calculated Frame List (0008,1162) must contain triplets (start, end, increment). "
                f"Found {len(frame_list)} values, but the count must be divisible by 3. "
                f"Each triplet specifies: start_frame, end_frame, increment. "
                f"Example: [1, 10, 2, 20, 30, 1] represents frames 1-10 step 2, and frames 20-30 step 1."
            )
            return
        
        if len(frame_list) == 0:
            result.add_error(
                f"{item_prefix}: Calculated Frame List (0008,1162) is empty but must contain at least one triplet. "
                f"Provide triplets of (start, end, increment) values to specify frame ranges. "
                f"Example: [1, 10, 2] for frames 1, 3, 5, 7, 9 (start=1, end=10, increment=2)."
            )
            return
        
        # Validate triplets
        for j in range(0, len(frame_list), 3):
            triplet_num = j // 3 + 1
            try:
                start = int(frame_list[j])
                end = int(frame_list[j + 1])
                increment = int(frame_list[j + 2])
                
                if start <= 0:
                    result.add_error(f"{item_prefix}: Calculated Frame List (0008,1162) triplet {triplet_num} start must be positive")
                if end <= 0:
                    result.add_error(f"{item_prefix}: Calculated Frame List (0008,1162) triplet {triplet_num} end must be positive")
                if increment <= 0:
                    result.add_error(f"{item_prefix}: Calculated Frame List (0008,1162) triplet {triplet_num} increment must be positive")
                if start > end:
                    result.add_error(f"{item_prefix}: Calculated Frame List (0008,1162) triplet {triplet_num} start must be <= end")
                
            except (ValueError, TypeError):
                result.add_error(f"{item_prefix}: Calculated Frame List (0008,1162) triplet {triplet_num} values must be integers")
    
    @staticmethod
    def _validate_time_range(item, result: ValidationResult, item_prefix: str) -> None:
        """Validate Time Range format (start and end times as floating point values)."""
        if not hasattr(item, 'TimeRange'):
            return
        
        time_range = item.TimeRange
        
        # Check if it's a string (which pydicom treats as iterable but isn't a proper list)
        if isinstance(time_range, str):
            result.add_error(f"{item_prefix}: Time Range (0008,1163) must be a list")
            return
        
        # Check if it's a single number (pydicom converts single-element lists to scalars)
        if isinstance(time_range, (int, float)):
            time_range = [time_range]  # Convert back to list for validation
        
        # Convert to list if it's a pydicom MultiValue or other sequence type
        try:
            time_range = list(time_range)
        except (TypeError, ValueError):
            result.add_error(f"{item_prefix}: Time Range (0008,1163) must be a list")
            return
        
        if len(time_range) != 2:
            result.add_error(
                f"{item_prefix}: Time Range (0008,1163) must contain exactly 2 floating point values (start_time, end_time). "
                f"Found {len(time_range)} values. Provide start and end times as floating point numbers "
                f"representing DICOM time values, e.g., [120000.0, 130000.0] for 12:00:00 to 13:00:00."
            )
            return
        
        # Validate time values are floating point numbers
        for j, time_val in enumerate(time_range):
            time_name = "start" if j == 0 else "end"
            try:
                float_val = float(time_val)
                if float_val < 0:
                    result.add_error(f"{item_prefix}: Time Range (0008,1163) {time_name} time must be non-negative, found {float_val}")
            except (ValueError, TypeError):
                result.add_error(f"{item_prefix}: Time Range (0008,1163) {time_name} time must be a floating point number, found {time_val}")
        
        # Validate start <= end
        try:
            start_time = float(time_range[0])
            end_time = float(time_range[1])
            if start_time > end_time:
                result.add_error(f"{item_prefix}: Time Range (0008,1163) start time must be <= end time")
        except (ValueError, TypeError, IndexError):
            pass  # Already reported above
    
    @staticmethod
    def _is_valid_uid(uid_str: str) -> bool:
        """Check if UID string is in valid format."""
        if not uid_str:
            return False
        
        # Basic UID format check: numbers and dots, no leading/trailing dots
        if uid_str.startswith('.') or uid_str.endswith('.'):
            return False
        
        parts = uid_str.split('.')
        if len(parts) < 2:  # UID must have at least 2 parts
            return False
        
        for part in parts:
            if not part.isdigit() or part.startswith('0') and len(part) > 1:
                return False
        
        return True
