"""General Reference Module DICOM validation - PS3.3 C.12.4

Validates all requirements for the General Reference Module including:
- Type 1C conditional requirements (Patient Orientation when Spatial Locations Preserved is REORIENTED_ONLY)
- Type 1 requirements within sequences (Purpose of Reference Code Sequence in Referenced Instance Sequence)
- Enumerated value validation (Spatial Locations Preserved)
- Sequence structure validation (required SOP Class and Instance UIDs)
- Semantic validation (images not in Source Instance Sequence)
"""

from pydicom import Dataset
from .base_validator import BaseValidator, ValidationConfig
from ..validation_result import ValidationResult
from ...enums.common_enums import SpatialLocationsPreserved


class GeneralReferenceValidator:
    """Validator for DICOM General Reference Module (PS3.3 C.12.4).

    Validates all conditional requirements, enumerated values, and sequence structures
    according to DICOM PS3.3 C.12.4 specification.
    """
    
    @staticmethod
    def validate(dataset: Dataset, config: ValidationConfig | None = None) -> ValidationResult:
        """Validate General Reference Module requirements on any pydicom Dataset.

        Performs comprehensive validation including:
        - Type 1C conditional requirements (Patient Orientation when Spatial Locations Preserved is REORIENTED_ONLY)
        - Type 1 requirements within sequences (Purpose of Reference Code Sequence in Referenced Instance Sequence)
        - Enumerated value validation (Spatial Locations Preserved values)
        - Sequence structure validation (required SOP Class and Instance UIDs from macros)
        - Semantic validation (business logic constraints)

        Args:
            dataset: pydicom Dataset to validate
            config: Validation configuration options

        Returns:
            ValidationResult with errors and warnings
        """
        config = config or ValidationConfig()
        result = BaseValidator.create_validation_result()

        # Validate Type 1C conditional requirements
        if config.validate_conditional_requirements:
            GeneralReferenceValidator._validate_conditional_requirements(dataset, result)

        # Validate enumerated values
        if config.check_enumerated_values:
            GeneralReferenceValidator._validate_enumerated_values(dataset, result)

        # Validate sequence structures and Type 1 requirements within sequences
        if config.validate_sequences:
            GeneralReferenceValidator._validate_sequence_requirements(dataset, result)

        # Validate semantic constraints
        if config.validate_semantic_constraints:
            GeneralReferenceValidator._validate_semantic_constraints(dataset, result)

        return result
    
    @staticmethod
    def _validate_conditional_requirements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate Type 1C conditional requirements per DICOM PS3.3 C.12.4.

        Validates:
        - Patient Orientation (0020,0020) is Type 1C in Source Image Sequence when
          Spatial Locations Preserved (0028,135A) is REORIENTED_ONLY
        """

        # Type 1C: Patient Orientation required if Spatial Locations Preserved is REORIENTED_ONLY
        source_image_seq = getattr(dataset, 'SourceImageSequence', [])
        for i, item in enumerate(source_image_seq):
            spatial_preserved = getattr(item, 'SpatialLocationsPreserved', '')
            if spatial_preserved == "REORIENTED_ONLY":
                patient_orientation = getattr(item, 'PatientOrientation', None)
                if not patient_orientation:
                    result.add_error(
                        f"Source Image Sequence item {i}: Patient Orientation (0020,0020) is Type 1C "
                        f"and required when Spatial Locations Preserved (0028,135A) is REORIENTED_ONLY. "
                        f"See DICOM PS3.3 C.12.4 General Reference Module."
                    )
                elif len(patient_orientation) != 2:
                    result.add_error(
                        f"Source Image Sequence item {i}: Patient Orientation (0020,0020) must be "
                        f"a list/array of exactly 2 values when present. "
                        f"Current value: {patient_orientation}"
                    )
    
    @staticmethod
    def _validate_enumerated_values(dataset: Dataset, result: ValidationResult) -> None:
        """Validate enumerated values against DICOM specifications per PS3.3 C.12.4.

        Validates:
        - Spatial Locations Preserved (0028,135A) in Source Image Sequence
          Valid values: YES, NO, REORIENTED_ONLY
        """

        # Spatial Locations Preserved (0028,135A) in Source Image Sequence
        source_image_seq = getattr(dataset, 'SourceImageSequence', [])
        for i, item in enumerate(source_image_seq):
            spatial_preserved = getattr(item, 'SpatialLocationsPreserved', '')
            if spatial_preserved:
                valid_values = [val.value for val in SpatialLocationsPreserved]
                BaseValidator.validate_enumerated_value(
                    spatial_preserved, valid_values,
                    f"Source Image Sequence item {i}: Spatial Locations Preserved (0028,135A)", result
                )
    
    @staticmethod
    def _validate_sequence_requirements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate sequence structure requirements per DICOM PS3.3 C.12.4.

        Validates requirements from included macros:
        - Table 10-3 Image SOP Instance Reference Macro (Referenced Image Sequence, Source Image Sequence)
        - Table 10-11 SOP Instance Reference Macro (Referenced Instance Sequence, Source Instance Sequence)
        - Type 1 requirements within sequences
        """

        # Referenced Image Sequence - includes Table 10-3 Image SOP Instance Reference Macro
        GeneralReferenceValidator._validate_image_sop_instance_reference_sequence(
            dataset, 'ReferencedImageSequence', result
        )

        # Referenced Instance Sequence - includes Table 10-11 SOP Instance Reference Macro
        # Purpose of Reference Code Sequence is Type 1 (required) in this sequence
        ref_instance_seq = getattr(dataset, 'ReferencedInstanceSequence', [])
        for i, item in enumerate(ref_instance_seq):
            GeneralReferenceValidator._validate_sop_instance_reference_item(
                item, f"Referenced Instance Sequence item {i}", result
            )

            # Type 1 requirement: Purpose of Reference Code Sequence
            purpose_seq = getattr(item, 'PurposeOfReferenceCodeSequence', None)
            if not purpose_seq:
                result.add_error(
                    f"Referenced Instance Sequence item {i}: Purpose of Reference Code Sequence (0040,A170) "
                    f"is Type 1 (required). Uses CID 7004 for waveforms, CID 7022 for RT instances. "
                    f"See DICOM PS3.3 C.12.4."
                )
            else:
                # Check if it's a sequence-like object with length
                try:
                    seq_length = len(purpose_seq)
                    if seq_length == 0:
                        result.add_error(
                            f"Referenced Instance Sequence item {i}: Purpose of Reference Code Sequence (0040,A170) "
                            f"must contain at least one item when present."
                        )
                    elif seq_length > 1:
                        result.add_warning(
                            f"Referenced Instance Sequence item {i}: Purpose of Reference Code Sequence (0040,A170) "
                            f"should contain only a single item per DICOM standard. Found {seq_length} items."
                        )
                except (TypeError, AttributeError):
                    # If it's not a sequence-like object, that's also an error
                    result.add_error(
                        f"Referenced Instance Sequence item {i}: Purpose of Reference Code Sequence (0040,A170) "
                        f"must be a sequence containing at least one item."
                    )

        # Source Image Sequence - includes Table 10-3 Image SOP Instance Reference Macro
        GeneralReferenceValidator._validate_image_sop_instance_reference_sequence(
            dataset, 'SourceImageSequence', result
        )

        # Source Instance Sequence - includes Table 10-11 SOP Instance Reference Macro
        source_instance_seq = getattr(dataset, 'SourceInstanceSequence', [])
        for i, item in enumerate(source_instance_seq):
            GeneralReferenceValidator._validate_sop_instance_reference_item(
                item, f"Source Instance Sequence item {i}", result
            )

    @staticmethod
    def _validate_image_sop_instance_reference_sequence(
        dataset: Dataset, sequence_name: str, result: ValidationResult
    ) -> None:
        """Validate Image SOP Instance Reference Macro requirements (Table 10-3).

        Args:
            dataset: Dataset containing the sequence
            sequence_name: Name of the sequence attribute
            result: ValidationResult to update
        """
        sequence = getattr(dataset, sequence_name, [])
        for i, item in enumerate(sequence):
            item_name = f"{sequence_name} item {i}"

            # Required elements from Table 10-3 Image SOP Instance Reference Macro
            if not getattr(item, 'ReferencedSOPClassUID', None):
                result.add_error(
                    f"{item_name}: Referenced SOP Class UID (0008,1150) is required "
                    f"from Table 10-3 Image SOP Instance Reference Macro. "
                    f"See DICOM PS3.3 C.12.4."
                )
            if not getattr(item, 'ReferencedSOPInstanceUID', None):
                result.add_error(
                    f"{item_name}: Referenced SOP Instance UID (0008,1155) is required "
                    f"from Table 10-3 Image SOP Instance Reference Macro. "
                    f"See DICOM PS3.3 C.12.4."
                )

    @staticmethod
    def _validate_sop_instance_reference_item(
        item: Dataset, item_name: str, result: ValidationResult
    ) -> None:
        """Validate SOP Instance Reference Macro requirements (Table 10-11).

        Args:
            item: Dataset item to validate
            item_name: Name/description of the item for error messages
            result: ValidationResult to update
        """
        # Required elements from Table 10-11 SOP Instance Reference Macro
        if not getattr(item, 'ReferencedSOPClassUID', None):
            result.add_error(
                f"{item_name}: Referenced SOP Class UID (0008,1150) is required "
                f"from Table 10-11 SOP Instance Reference Macro. "
                f"See DICOM PS3.3 C.12.4."
            )
        if not getattr(item, 'ReferencedSOPInstanceUID', None):
            result.add_error(
                f"{item_name}: Referenced SOP Instance UID (0008,1155) is required "
                f"from Table 10-11 SOP Instance Reference Macro. "
                f"See DICOM PS3.3 C.12.4."
            )

    @staticmethod
    def _validate_semantic_constraints(dataset: Dataset, result: ValidationResult) -> None:
        """Validate semantic constraints and business logic per DICOM PS3.3 C.12.4.

        Validates:
        - Images shall not be referenced by Source Instance Sequence (only non-image instances)
        - Purpose of Reference Code Sequence uses appropriate CID codes
        - Sequence usage consistency with derivation information
        """

        # Semantic constraint: Images shall not be referenced by Source Instance Sequence
        source_instance_seq = getattr(dataset, 'SourceInstanceSequence', [])
        for i, item in enumerate(source_instance_seq):
            sop_class_uid = getattr(item, 'ReferencedSOPClassUID', '')
            if sop_class_uid:
                # Check if this is an image SOP class (basic check for common image SOP classes)
                image_sop_classes = [
                    '1.2.840.10008.5.1.4.1.1.1',    # CR Image Storage
                    '1.2.840.10008.5.1.4.1.1.2',    # CT Image Storage
                    '1.2.840.10008.5.1.4.1.1.4',    # MR Image Storage
                    '1.2.840.10008.5.1.4.1.1.6.1',  # US Image Storage
                    '1.2.840.10008.5.1.4.1.1.12.1', # X-Ray Angiographic Image Storage
                    '1.2.840.10008.5.1.4.1.1.20',   # Nuclear Medicine Image Storage
                    '1.2.840.10008.5.1.4.1.1.77.1.4', # VL Photographic Image Storage
                ]

                if sop_class_uid in image_sop_classes:
                    result.add_error(
                        f"Source Instance Sequence item {i}: Images shall NOT be referenced by "
                        f"Source Instance Sequence (0042,0013). Use Source Image Sequence (0008,2112) "
                        f"for image references. Referenced SOP Class UID {sop_class_uid} appears to be "
                        f"an image SOP class. See DICOM PS3.3 C.12.4.1.2."
                    )

        # Validate derivation consistency
        GeneralReferenceValidator._validate_derivation_consistency(dataset, result)

    @staticmethod
    def _validate_derivation_consistency(dataset: Dataset, result: ValidationResult) -> None:
        """Validate consistency between derivation information and source sequences.

        Args:
            dataset: Dataset to validate
            result: ValidationResult to update
        """
        has_derivation_desc = hasattr(dataset, 'DerivationDescription')
        has_derivation_code = hasattr(dataset, 'DerivationCodeSequence')
        has_source_images = hasattr(dataset, 'SourceImageSequence')
        has_source_instances = hasattr(dataset, 'SourceInstanceSequence')

        # If derivation information is present, it suggests this is a derived image
        if has_derivation_desc or has_derivation_code:
            if not (has_source_images or has_source_instances):
                result.add_warning(
                    "Derivation Description (0008,2111) or Derivation Code Sequence (0008,9215) "
                    "is present but no Source Image Sequence (0008,2112) or Source Instance Sequence "
                    "(0042,0013) is provided. Consider adding source information to document the "
                    "derivation process. See DICOM PS3.3 C.********."
                )

        # If source sequences are present, derivation information might be helpful
        if (has_source_images or has_source_instances) and not (has_derivation_desc or has_derivation_code):
            result.add_warning(
                "Source Image Sequence (0008,2112) or Source Instance Sequence (0042,0013) "
                "is present but no Derivation Description (0008,2111) or Derivation Code Sequence "
                "(0008,9215) is provided. Consider adding derivation information to document how "
                "the image was derived. See DICOM PS3.3 C.********."
            )
