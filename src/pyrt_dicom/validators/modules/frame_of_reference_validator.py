"""Frame of Reference Module DICOM validation - PS3.3 C.7.4.1

This validator ensures compliance with DICOM PS3.3 C.7.4.1 Frame of Reference Module
requirements, including Type 1 and Type 2 element validation, UID format validation,
and position reference indicator semantic validation.
"""

import re
from pydicom import Dataset
from .base_validator import BaseValidator, ValidationConfig
from ..validation_result import ValidationResult


class FrameOfReferenceValidator:
    """Validator for DICOM Frame of Reference Module (PS3.3 C.7.4.1).

    Validates Frame of Reference Module requirements including:
    - Type 1: Frame of Reference UID (0020,0052)
    - Type 2: Position Reference Indicator (0020,1040)
    - UID format compliance per DICOM PS3.5
    - Position reference indicator semantic validation
    - Context-specific warnings for special coordinate systems
    """
    
    @staticmethod
    def validate(dataset: Dataset, config: ValidationConfig | None = None) -> ValidationResult:
        """Validate Frame of Reference Module requirements on any pydicom Dataset.

        Performs comprehensive validation of DICOM PS3.3 C.7.4.1 Frame of Reference Module
        including required elements, UID format compliance, and semantic validation of
        position reference indicators.

        Args:
            dataset (Dataset): pydicom Dataset to validate
            config (ValidationConfig | None): Validation configuration options

        Returns:
            ValidationResult: Validation result with errors and warnings lists
        """
        config = config or ValidationConfig()
        result = BaseValidator.create_validation_result()

        # Validate Type 1 and Type 2 requirements
        FrameOfReferenceValidator._validate_required_elements(dataset, result)

        # Validate UID format compliance
        FrameOfReferenceValidator._validate_uid_format(dataset, result)

        # Validate position reference indicator values and context
        if config.check_enumerated_values:
            FrameOfReferenceValidator._validate_position_reference_indicator(dataset, result)

        return result
    
    @staticmethod
    def _validate_required_elements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate Type 1 and Type 2 required elements.

        Args:
            dataset (Dataset): pydicom Dataset to validate
            result (ValidationResult): ValidationResult to update with errors
        """

        # Frame of Reference UID (0020,0052) Type 1 - Required and must have value
        if not hasattr(dataset, 'FrameOfReferenceUID'):
            result.add_error(
                "Frame of Reference UID (0020,0052) is required (Type 1). "
                "This attribute uniquely identifies the Frame of Reference for a Series."
            )
        else:
            if not isinstance(dataset.FrameOfReferenceUID, str) or len(dataset.FrameOfReferenceUID.strip()) == 0:
                result.add_error(
                    "Frame of Reference UID (0020,0052) must be a non-empty string. "
                    "Provide a valid DICOM UID that uniquely identifies the Frame of Reference."
                )

        # Position Reference Indicator (0020,1040) Type 2 - Required but may be empty
        if not hasattr(dataset, 'PositionReferenceIndicator'):
            result.add_error(
                "Position Reference Indicator (0020,1040) is required (Type 2). "
                "This attribute specifies the part of the imaging target used as a reference point. "
                "It may be empty when no meaningful reference point exists (e.g., mammographic images)."
            )
    
    @staticmethod
    def _validate_uid_format(dataset: Dataset, result: ValidationResult) -> None:
        """Validate UID format according to DICOM PS3.5 standard.

        Args:
            dataset (Dataset): pydicom Dataset to validate
            result (ValidationResult): ValidationResult to update with errors
        """

        if not hasattr(dataset, 'FrameOfReferenceUID'):
            return

        uid = dataset.FrameOfReferenceUID

        # UID format validation according to DICOM PS3.5
        # UIDs are composed of numeric components separated by periods
        # Each component is a series of digits
        # Maximum length is 64 characters
        # No leading zeros except for "0" itself

        if len(uid) > 64:
            result.add_error(
                f"Frame of Reference UID (0020,0052) exceeds maximum length of 64 characters (length: {len(uid)}). "
                f"DICOM UIDs must be 64 characters or less per PS3.5."
            )

        # Check basic format: digits and periods only
        if not re.match(r'^[0-9.]+$', uid):
            result.add_error(
                "Frame of Reference UID (0020,0052) contains invalid characters. "
                "DICOM UIDs may only contain digits (0-9) and periods (.) per PS3.5."
            )
        else:
            # Check for valid structure
            if uid.startswith('.') or uid.endswith('.'):
                result.add_error(
                    "Frame of Reference UID (0020,0052) cannot start or end with a period. "
                    "Provide a valid UID format per DICOM PS3.5."
                )

            if '..' in uid:
                result.add_error(
                    "Frame of Reference UID (0020,0052) cannot contain consecutive periods. "
                    "Each UID component must be separated by a single period per DICOM PS3.5."
                )

            # Check each component for leading zeros
            components = uid.split('.')
            for i, component in enumerate(components):
                if len(component) == 0:
                    result.add_error(
                        f"Frame of Reference UID (0020,0052) has empty component at position {i+1}. "
                        f"Each UID component must contain at least one digit per DICOM PS3.5."
                    )
                elif len(component) > 1 and component.startswith('0'):
                    result.add_error(
                        f"Frame of Reference UID (0020,0052) component '{component}' has leading zero. "
                        f"UID components cannot have leading zeros except for '0' itself per DICOM PS3.5."
                    )
    
    @staticmethod
    def _validate_position_reference_indicator(dataset: Dataset, result: ValidationResult) -> None:
        """Validate position reference indicator values and provide context-specific guidance.

        Args:
            dataset (Dataset): pydicom Dataset to validate
            result (ValidationResult): ValidationResult to update with warnings
        """

        if not hasattr(dataset, 'PositionReferenceIndicator'):
            return

        indicator = dataset.PositionReferenceIndicator

        # Known valid anatomical reference points for patient-based coordinate systems
        # Per DICOM PS3.3 C.*******.2
        known_anatomical_indicators = {
            "ILIAC_CREST", "ORBITAL_MEDIAL", "STERNAL_NOTCH", "SYMPHYSIS_PUBIS",
            "XIPHOID", "LOWER_COSTAL_MARGIN", "EXTERNAL_AUDITORY_MEATUS"
        }

        # Known valid special indicators per DICOM PS3.3 C.*******.2
        known_special_indicators = {
            "SLIDE_CORNER",      # For slide-related frame of reference (Section C.********)
            "CORNEAL_VERTEX_R",  # For corneal coordinate system (right eye, Section C.********.4)
            "CORNEAL_VERTEX_L"   # For corneal coordinate system (left eye, Section C.********.4)
        }

        all_known_indicators = known_anatomical_indicators | known_special_indicators

        # Only validate if not empty (empty is explicitly allowed per DICOM standard)
        if len(indicator.strip()) > 0:
            if indicator not in all_known_indicators:
                result.add_warning(
                    f"Position Reference Indicator (0020,1040) value '{indicator}' is not a standard DICOM value. "
                    f"Standard values per PS3.3 C.*******.2 include: {', '.join(sorted(all_known_indicators))}. "
                    f"Custom values are allowed but should be documented."
                )

            # Provide context-specific guidance for special coordinate systems
            if indicator in ["CORNEAL_VERTEX_R", "CORNEAL_VERTEX_L"]:
                eye_side = "right" if indicator == "CORNEAL_VERTEX_R" else "left"
                result.add_warning(
                    f"Position Reference Indicator '{indicator}' indicates a corneal coordinate system for the {eye_side} eye. "
                    f"Ensure this is appropriate for the imaging context and that the corneal vertex is determined by the measuring instrument "
                    f"per DICOM PS3.3 Section C.********.4."
                )

            if indicator == "SLIDE_CORNER":
                result.add_warning(
                    "Position Reference Indicator 'SLIDE_CORNER' indicates a slide-based coordinate system. "
                    "Ensure this is appropriate for the imaging context and that the slide corner is specified "
                    "per DICOM PS3.3 Section C.********."
                )

            # Provide guidance for anatomical landmarks
            if indicator in known_anatomical_indicators:
                result.add_warning(
                    f"Position Reference Indicator '{indicator}' indicates a patient-based coordinate system. "
                    f"Ensure this anatomical landmark is appropriate for the imaging procedure and patient positioning."
                )
