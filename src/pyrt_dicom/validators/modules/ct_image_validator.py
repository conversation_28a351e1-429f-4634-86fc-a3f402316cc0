"""
CT Image Module Validator - DICOM PS3.3 C.8.2.1

Provides comprehensive validation for CT Image Module according to DICOM standard,
including Type 1/2/3/1C requirements, conditional logic, enumerated values,
and multi-energy CT constraints.
"""

from pydicom import Dataset
from .base_validator import BaseValidator, ValidationConfig
from ..validation_result import ValidationResult
from ...enums.contrast_ct_enums import (
    MultiEnergyCTAcquisition, RotationDirection, ExposureModulationType,
    CTImageTypeValue1, CTImageTypeValue2, CTImageTypeValue3, CTImageTypeValue4, CTSamplesPerPixel, CTBitsAllocated,
    CTBitsStored, RescaleType
)
from ...enums.image_enums import PhotometricInterpretation


class CTImageValidator(BaseValidator):
    """
    Validator for CT Image Module (DICOM PS3.3 C.8.2.1).

    Validates all aspects of CT Image Module including:
    - Type 1 (required) elements
    - Type 2 (required but can be empty) elements
    - Type 1C (conditionally required) elements
    - Enumerated value constraints
    - Multi-energy CT specific requirements
    - Pixel data consistency
    - Sequence structure validation

    Provides clear, actionable error messages with DICOM tag references
    and guidance for resolving validation issues.
    """
    
    @staticmethod
    def validate(dataset: Dataset, config: ValidationConfig | None = None) -> ValidationResult:
        """
        Validate CT Image Module requirements on any pydicom Dataset.

        Performs comprehensive validation according to DICOM PS3.3 C.8.2.1 including:
        - All Type 1 (required) elements presence
        - Type 1C conditional requirements based on multi-energy CT and other conditions
        - Enumerated value constraints for all applicable attributes
        - Multi-energy CT specific validation rules and exclusions
        - Pixel data consistency checks
        - Sequence structure validation

        Args:
            dataset: pydicom Dataset to validate against CT Image Module requirements
            config: Optional validation configuration to control validation behavior

        Returns:
            ValidationResult containing lists of errors and warnings with specific
            DICOM tag references and actionable guidance for resolution
        """
        config = config or ValidationConfig()
        result = BaseValidator.create_validation_result()

        # Always validate Type 1 required elements first
        CTImageValidator._validate_required_elements(dataset, result)

        # Validate Type 1C conditional requirements
        if config.validate_conditional_requirements:
            CTImageValidator._validate_conditional_requirements(dataset, result)

        # Validate multi-energy sequence exclusion rules
        if config.validate_conditional_requirements:
            CTImageValidator._validate_multi_energy_sequence_exclusions(dataset, result)

        # Validate enumerated values
        if config.check_enumerated_values:
            CTImageValidator._validate_enumerated_values(dataset, result)

        # Validate pixel data consistency
        CTImageValidator._validate_pixel_data_consistency(dataset, result)

        # Validate sequence structure
        if config.validate_sequences:
            CTImageValidator._validate_sequence_structure(dataset, result)

        return result
    
    @staticmethod
    def _validate_conditional_requirements(dataset: Dataset, result: ValidationResult) -> None:
        """
        Validate Type 1C conditional requirements according to DICOM PS3.3 C.8.2.1.

        Validates all conditional requirements including:
        - Rescale Type requirement based on multi-energy CT or non-HU rescale type
        - Energy Weighting Factor requirement for multi-energy proportional weighting
        - Water Equivalent Diameter Calculation Method requirement
        """

        # Type 1C: Rescale Type (0028,1054) requirement
        multi_energy_acquisition = getattr(dataset, 'MultienergyCTAcquisition', '')
        rescale_type = getattr(dataset, 'RescaleType', '')

        # Required if Multi-energy CT Acquisition is YES or if Rescale Type is not HU
        condition_met = (multi_energy_acquisition == "YES" or
                        (rescale_type and rescale_type != "HU"))

        if condition_met:
            if not hasattr(dataset, 'RescaleType') or not rescale_type:
                result.add_error(
                    "CT Image Module validation error: Rescale Type (0028,1054) is required "
                    "when Multi-energy CT Acquisition (0018,9361) is YES or when the rescale "
                    "type is not HU (Hounsfield Units). According to DICOM PS3.3 C.8.2.1, "
                    "this is a Type 1C requirement. To resolve: add RescaleType attribute "
                    "with appropriate value (e.g., 'HU', 'US', etc.)."
                )

        # Type 1C: Energy Weighting Factor (0018,9353) requirement
        derivation_code_seq = getattr(dataset, 'DerivationCodeSequence', [])
        has_multi_energy_weighting = False

        if derivation_code_seq:
            for item in derivation_code_seq:
                if (item.get('CodeValue') == '113097' and
                    item.get('CodingSchemeDesignator') == 'DCM'):
                    has_multi_energy_weighting = True
                    break

        if has_multi_energy_weighting:
            if not hasattr(dataset, 'EnergyWeightingFactor'):
                result.add_error(
                    "CT Image Module validation error: Energy Weighting Factor (0018,9353) "
                    "is required when Derivation Code Sequence (0008,9215) contains an item "
                    "with CodeValue '113097' (Multi-energy proportional weighting). According "
                    "to DICOM PS3.3 C.8.2.1, this is a Type 1C requirement. To resolve: "
                    "add EnergyWeightingFactor attribute with appropriate weighting value."
                )

        # Type 1C: Water Equivalent Diameter Calculation Method Code Sequence (0018,1272) requirement
        if hasattr(dataset, 'WaterEquivalentDiameter'):
            if not hasattr(dataset, 'WaterEquivalentDiameterCalculationMethodCodeSequence'):
                result.add_error(
                    "CT Image Module validation error: Water Equivalent Diameter Calculation "
                    "Method Code Sequence (0018,1272) is required when Water Equivalent "
                    "Diameter (0018,1271) is present. According to DICOM PS3.3 C.8.2.1, "
                    "this is a Type 1C requirement. To resolve: add "
                    "WaterEquivalentDiameterCalculationMethodCodeSequence with appropriate "
                    "code sequence item from CID 10024."
                )
    
    @staticmethod
    def _validate_enumerated_values(dataset: Dataset, result: ValidationResult) -> None:
        """Validate enumerated values against DICOM standard."""
        
        # Multi-energy CT Acquisition (0018,9361)
        multi_energy = getattr(dataset, 'MultienergyCTAcquisition', '')
        if multi_energy:
            valid_values = [acquisition.value for acquisition in MultiEnergyCTAcquisition]
            BaseValidator.validate_enumerated_value(
                multi_energy, valid_values,
                "Multi-energy CT Acquisition (0018,9361)", result
            )
        
        # Samples per Pixel (0028,0002) - must be 1 for CT
        samples_per_pixel = getattr(dataset, 'SamplesPerPixel', None)
        if samples_per_pixel is not None and samples_per_pixel != CTSamplesPerPixel.ONE.value:
            result.add_error(
                f"CT Image Module validation error: Samples per Pixel (0028,0002) must be "
                f"{CTSamplesPerPixel.ONE.value} for CT images according to DICOM PS3.3 C.8.2.1.1.2, "
                f"but got {samples_per_pixel}. To resolve: set SamplesPerPixel to 1."
            )

        # Photometric Interpretation (0028,0004)
        photometric = getattr(dataset, 'PhotometricInterpretation', '')
        if photometric:
            valid_values = [PhotometricInterpretation.MONOCHROME1.value, PhotometricInterpretation.MONOCHROME2.value]
            if photometric not in valid_values:
                result.add_error(
                    f"CT Image Module validation error: Photometric Interpretation (0028,0004) "
                    f"must be one of {valid_values} for CT images according to DICOM PS3.3 "
                    f"C.8.2.1.1.3, but got '{photometric}'. To resolve: set PhotometricInterpretation "
                    f"to either 'MONOCHROME1' or 'MONOCHROME2'."
                )

        # Bits Allocated (0028,0100) - must be 16 for CT
        bits_allocated = getattr(dataset, 'BitsAllocated', None)
        if bits_allocated is not None and bits_allocated != CTBitsAllocated.SIXTEEN.value:
            result.add_error(
                f"CT Image Module validation error: Bits Allocated (0028,0100) must be "
                f"{CTBitsAllocated.SIXTEEN.value} for CT images according to DICOM PS3.3 "
                f"C.8.2.1.1.4, but got {bits_allocated}. To resolve: set BitsAllocated to 16."
            )
        
        # Bits Stored (0028,0101) - valid values for CT
        bits_stored = getattr(dataset, 'BitsStored', None)
        if bits_stored is not None:
            valid_values = [bits.value for bits in CTBitsStored]
            if bits_stored not in valid_values:
                result.add_error(
                    f"CT Image Module validation error: Bits Stored (0028,0101) must be one of "
                    f"{valid_values} for CT images according to DICOM PS3.3 C.8.2.1.1.5, "
                    f"but got {bits_stored}. To resolve: set BitsStored to a valid value "
                    f"(12, 13, 14, 15, or 16)."
                )
        
        # Rotation Direction (0018,1140)
        rotation_direction = getattr(dataset, 'RotationDirection', '')
        if rotation_direction:
            valid_values = [direction.value for direction in RotationDirection]
            BaseValidator.validate_enumerated_value(
                rotation_direction, valid_values,
                "Rotation Direction (0018,1140)", result
            )
        
        # Exposure Modulation Type (0018,9323)
        exposure_modulation = getattr(dataset, 'ExposureModulationType', '')
        if exposure_modulation:
            valid_values = [modulation.value for modulation in ExposureModulationType]
            BaseValidator.validate_enumerated_value(
                exposure_modulation, valid_values,
                "Exposure Modulation Type (0018,9323)", result
            )
        
        # Rescale Type (0028,1054) validation
        rescale_type = getattr(dataset, 'RescaleType', '')
        if rescale_type:
            valid_values = [rescale.value for rescale in RescaleType]
            BaseValidator.validate_enumerated_value(
                rescale_type, valid_values,
                "Rescale Type (0028,1054)", result
            )
        
        # Image Type (0008,0008) validation
        image_type = getattr(dataset, 'ImageType', [])
        if image_type:
            # Map of value positions to their corresponding enums and descriptions
            value_validations = [
                (CTImageTypeValue1, "Value 1"),
                (CTImageTypeValue2, "Value 2"),
                (CTImageTypeValue3, "Value 3"),
            ]

            # Validate values 1-3
            for i, (enum_class, description) in enumerate(value_validations):
                if len(image_type) > i:
                    valid_values = [value.value for value in enum_class]
                    BaseValidator.validate_enumerated_value(
                        image_type[i], valid_values,
                        f"Image Type (0008,0008) {description}", result
                    )

            # Special handling for Value 4 (multi-energy CT)
            if multi_energy == "YES":
                if len(image_type) >= 4:
                    valid_values = [value.value for value in CTImageTypeValue4]
                    BaseValidator.validate_enumerated_value(
                        image_type[3], valid_values,
                        "Image Type (0008,0008) Value 4 for Multi-energy CT", result
                    )
                else:
                    result.add_error(
                        "Image Type (0008,0008) Value 4 shall be present if Multi-energy CT Acquisition (0018,9361) has a Value of YES"
                    )
            elif multi_energy == "NO":
                if len(image_type) >= 4:
                    result.add_warning(
                        "Image Type (0008,0008) Value 4 should not be present if Multi-energy CT Acquisition (0018,9361) has a Value of NO"
                    )
    
    @staticmethod
    def _validate_required_elements(dataset: Dataset, result: ValidationResult) -> None:
        """
        Validate presence of Type 1 required elements according to DICOM PS3.3 C.8.2.1.

        All Type 1 elements must be present and have valid values.
        """

        required_elements = [
            ('ImageType', 'Image Type (0008,0008)', 'Image identification characteristics'),
            ('SamplesPerPixel', 'Samples per Pixel (0028,0002)', 'Number of samples (planes) in this image'),
            ('PhotometricInterpretation', 'Photometric Interpretation (0028,0004)', 'Intended interpretation of pixel data'),
            ('BitsAllocated', 'Bits Allocated (0028,0100)', 'Number of bits allocated for each pixel sample'),
            ('BitsStored', 'Bits Stored (0028,0101)', 'Number of bits stored for each pixel sample'),
            ('HighBit', 'High Bit (0028,0102)', 'Most significant bit for pixel sample data'),
            ('RescaleIntercept', 'Rescale Intercept (0028,1052)', 'Value b in relationship between stored values and output units'),
            ('RescaleSlope', 'Rescale Slope (0028,1053)', 'Value m in the equation specified in Rescale Intercept')
        ]

        for attr_name, display_name, description in required_elements:
            if not hasattr(dataset, attr_name):
                result.add_error(
                    f"CT Image Module validation error: {display_name} is required (Type 1). "
                    f"This attribute represents {description.lower()}. According to DICOM PS3.3 "
                    f"C.8.2.1, this is a mandatory element for CT Image Module. To resolve: "
                    f"add {attr_name} attribute with appropriate value."
                )
    
    @staticmethod
    def _validate_pixel_data_consistency(dataset: Dataset, result: ValidationResult) -> None:
        """Validate consistency between pixel data attributes."""
        
        # High Bit should be one less than Bits Stored
        bits_stored = getattr(dataset, 'BitsStored', None)
        high_bit = getattr(dataset, 'HighBit', None)
        
        if bits_stored is not None and high_bit is not None:
            expected_high_bit = bits_stored - 1
            if high_bit != expected_high_bit:
                result.add_error(
                    f"High Bit (0028,0102) should be one less than Bits Stored (0028,0101). "
                    f"Expected {expected_high_bit}, got {high_bit}"
                )
        
        # Validate Image Type Value 4 presence for multi-energy
        multi_energy = getattr(dataset, 'MultienergyCTAcquisition', '')
        image_type = getattr(dataset, 'ImageType', [])
        
        if multi_energy == "YES" and len(image_type) < 4:
            result.add_error(
                "Image Type (0008,0008) Value 4 shall be present if "
                "Multi-energy CT Acquisition (0018,9361) has a Value of YES"
            )
        
        # Validate CT Additional X-Ray Source Sequence constraints
        multi_energy_seq = getattr(dataset, 'MultienergyCTAcquisitionSequence', [])
        additional_source_seq = getattr(dataset, 'CTAdditionalXRaySourceSequence', [])
        
        if multi_energy == "YES" and additional_source_seq:
            result.add_error(
                "CT Additional X-Ray Source Sequence (0018,9360) shall not be present "
                "if Multi-energy CT Acquisition (0018,9361) is YES. "
                "Use Multi-energy CT Acquisition Sequence (0018,9362) instead"
            )
    
    @staticmethod
    def _validate_multi_energy_sequence_exclusions(dataset: Dataset, result: ValidationResult) -> None:
        """Validate multi-energy CT acquisition sequence exclusion rules.
        
        Many attributes shall not be present if they exist in Multi-energy CT 
        Acquisition Sequence with different values across items.
        """
        multi_energy = getattr(dataset, 'MultienergyCTAcquisition', '')
        multi_energy_seq = getattr(dataset, 'MultienergyCTAcquisitionSequence', [])
        
        if multi_energy != "YES" or not multi_energy_seq:
            return
        
        # Attributes that should not be present at module level if in sequence with different values
        conflicting_attributes = [
            ('KVP', 'KVP (0018,0060)'),
            ('DataCollectionDiameter', 'Data Collection Diameter (0018,0090)'),
            ('DistanceSourceToDetector', 'Distance Source to Detector (0018,1110)'),
            ('ExposureTime', 'Exposure Time (0018,1150)'),
            ('XRayTubeCurrent', 'X-Ray Tube Current (0018,1151)'),
            ('Exposure', 'Exposure (0018,1152)'),
            ('ExposureInuAs', 'Exposure in μAs (0018,1153)'),
            ('FilterType', 'Filter Type (0018,1160)'),
            ('GeneratorPower', 'Generator Power (0018,1170)'),
            ('FocalSpots', 'Focal Spot(s) (0018,1190)'),
            ('SingleCollimationWidth', 'Single Collimation Width (0018,9306)'),
            ('TotalCollimationWidth', 'Total Collimation Width (0018,9307)')
        ]
        
        for attr_name, display_name in conflicting_attributes:
            if hasattr(dataset, attr_name):
                # Check if this attribute exists in sequence with different values
                sequence_values = []
                for item in multi_energy_seq:
                    if hasattr(item, attr_name):
                        sequence_values.append(getattr(item, attr_name))
                
                if sequence_values and len(set(str(v) for v in sequence_values)) > 1:
                    result.add_error(
                        f"{display_name} shall not be present at module level when it exists "
                        f"in Multi-energy CT Acquisition Sequence (0018,9362) with different values"
                    )
    
    @staticmethod
    def _validate_sequence_structure(dataset: Dataset, result: ValidationResult) -> None:
        """Validate structure of sequences within the module."""
        
        # Validate CTDI Phantom Type Code Sequence
        ctdi_phantom_seq = getattr(dataset, 'CTDIPhantomTypeCodeSequence', [])
        if ctdi_phantom_seq:
            if len(ctdi_phantom_seq) > 1:
                result.add_error(
                    "CTDI Phantom Type Code Sequence (0018,9346) should contain only one item"
                )
            
            for i, item in enumerate(ctdi_phantom_seq):
                if not hasattr(item, 'CodeValue'):
                    result.add_error(
                        f"CTDI Phantom Type Code Sequence (0018,9346) item {i+1} missing CodeValue"
                    )
                if not hasattr(item, 'CodingSchemeDesignator'):
                    result.add_error(
                        f"CTDI Phantom Type Code Sequence (0018,9346) item {i+1} missing CodingSchemeDesignator"
                    )
        
        # Validate Water Equivalent Diameter Calculation Method Code Sequence
        water_equiv_seq = getattr(dataset, 'WaterEquivalentDiameterCalculationMethodCodeSequence', [])
        if water_equiv_seq:
            if len(water_equiv_seq) > 1:
                result.add_error(
                    "Water Equivalent Diameter Calculation Method Code Sequence (0018,1272) should contain only one item"
                )
            
            for i, item in enumerate(water_equiv_seq):
                if not hasattr(item, 'CodeValue'):
                    result.add_error(
                        f"Water Equivalent Diameter Calculation Method Code Sequence (0018,1272) item {i+1} missing CodeValue"
                    )
                if not hasattr(item, 'CodingSchemeDesignator'):
                    result.add_error(
                        f"Water Equivalent Diameter Calculation Method Code Sequence (0018,1272) item {i+1} missing CodingSchemeDesignator"
                    )
        
        # Validate CT Additional X-Ray Source Sequence structure
        additional_source_seq = getattr(dataset, 'CTAdditionalXRaySourceSequence', [])
        for i, item in enumerate(additional_source_seq):
            required_attrs = ['KVP', 'XRayTubeCurrentInmA', 'DataCollectionDiameter', 
                            'FocalSpots', 'FilterType', 'FilterMaterial']
            for attr in required_attrs:
                if not hasattr(item, attr):
                    result.add_error(
                        f"CT Additional X-Ray Source Sequence (0018,9360) item {i+1} missing required attribute {attr}"
                    )
