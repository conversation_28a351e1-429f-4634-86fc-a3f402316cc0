"""Validation result class for DICOM module validation.

This module provides a class-based validation result object to replace
the dict-based validation results used throughout the validator classes.
"""

from typing import List, Dict


class ValidationResult:
    """Class-based validation result with protected lists for errors and warnings.
    
    This class provides a structured approach to validation results with
    protected attributes and convenience methods for managing validation
    errors and warnings. It replaces the dict-based validation pattern
    used throughout the validator classes.
    
    Attributes:
        _errors: Protected list of validation error messages
        _warnings: Protected list of validation warning messages
    
    Example:
        >>> result = ValidationResult()
        >>> result.add_error("Patient ID is required")
        >>> result.add_warning("Recommended field is missing")
        >>> print(f"Has errors: {result.has_errors}")
        >>> print(f"Total issues: {result.total_issues}")
        >>> dict_result = result.to_dict()
    """
    
    def __init__(self) -> None:
        """Initialize validation result with empty error and warning lists."""
        self._errors: List[str] = []
        self._warnings: List[str] = []
    
    def add_error(self, message: str) -> None:
        """Add an error message to the validation result.
        
        Args:
            message: Error message describing the validation failure
        """
        if message and isinstance(message, str):
            self._errors.append(message)
    
    def add_warning(self, message: str) -> None:
        """Add a warning message to the validation result.
        
        Args:
            message: Warning message describing the validation concern
        """
        if message and isinstance(message, str):
            self._warnings.append(message)
    
    def extend_errors(self, messages: List[str]) -> None:
        """Add multiple error messages to the validation result.
        
        Args:
            messages: List of error messages to add
        """
        if messages and isinstance(messages, list):
            self._errors.extend(msg for msg in messages if msg and isinstance(msg, str))
    
    def extend_warnings(self, messages: List[str]) -> None:
        """Add multiple warning messages to the validation result.
        
        Args:
            messages: List of warning messages to add
        """
        if messages and isinstance(messages, list):
            self._warnings.extend(msg for msg in messages if msg and isinstance(msg, str))
    
    def merge(self, other: 'ValidationResult') -> None:
        """Merge another validation result into this one.
        
        Args:
            other: ValidationResult to merge into this instance
        """
        if isinstance(other, ValidationResult):
            self._errors.extend(other._errors)
            self._warnings.extend(other._warnings)
    
    def clear(self) -> None:
        """Clear all errors and warnings from the validation result."""
        self._errors.clear()
        self._warnings.clear()
    
    @property
    def errors(self) -> List[str]:
        """Get a copy of the error messages list.
        
        Returns:
            Copy of the errors list to prevent external modification
        """
        return self._errors.copy()
    
    @property
    def warnings(self) -> List[str]:
        """Get a copy of the warning messages list.
        
        Returns:
            Copy of the warnings list to prevent external modification
        """
        return self._warnings.copy()
    
    @property
    def has_errors(self) -> bool:
        """Check if any validation errors are present.
        
        Returns:
            True if there are validation errors, False otherwise
        """
        return len(self._errors) > 0
    
    @property
    def has_warnings(self) -> bool:
        """Check if any validation warnings are present.
        
        Returns:
            True if there are validation warnings, False otherwise
        """
        return len(self._warnings) > 0
    
    @property
    def has_issues(self) -> bool:
        """Check if any validation issues (errors or warnings) are present.
        
        Returns:
            True if there are validation errors or warnings, False otherwise
        """
        return self.has_errors or self.has_warnings
    
    @property
    def is_valid(self) -> bool:
        """Check if validation passed (no errors).
        
        Returns:
            True if there are no validation errors, False otherwise
        """
        return not self.has_errors
    
    @property
    def error_count(self) -> int:
        """Get the number of validation errors.
        
        Returns:
            Number of validation errors
        """
        return len(self._errors)
    
    @property
    def warning_count(self) -> int:
        """Get the number of validation warnings.
        
        Returns:
            Number of validation warnings
        """
        return len(self._warnings)
    
    @property
    def total_issues(self) -> int:
        """Get the total number of validation issues (errors + warnings).
        
        Returns:
            Total number of validation errors and warnings
        """
        return self.error_count + self.warning_count
    
    def to_dict(self) -> Dict[str, List[str]]:
        """Convert validation result to dictionary format.
        
        This method provides backward compatibility with the existing
        dict-based validation result format used throughout the codebase.
        
        Returns:
            Dictionary with 'errors' and 'warnings' keys containing
            copies of the respective message lists
        """
        return {
            "errors": self.errors,
            "warnings": self.warnings
        }
    
    @classmethod
    def from_dict(cls, result_dict: Dict[str, List[str]]) -> 'ValidationResult':
        """Create ValidationResult from dictionary format.
        
        This method provides backward compatibility by allowing creation
        of ValidationResult instances from the existing dict-based format.
        
        Args:
            result_dict: Dictionary with 'errors' and 'warnings' keys
            
        Returns:
            ValidationResult instance populated with the dictionary data
        """
        instance = cls()
        if isinstance(result_dict, dict):
            if "errors" in result_dict:
                instance.extend_errors(result_dict["errors"])
            if "warnings" in result_dict:
                instance.extend_warnings(result_dict["warnings"])
        return instance
    
    def __str__(self) -> str:
        """String representation of validation result.
        
        Returns:
            String summarizing the validation result
        """
        if not self.has_issues:
            return "Validation passed: No errors or warnings"
        
        parts = []
        if self.has_errors:
            parts.append(f"{self.error_count} error{'s' if self.error_count != 1 else ''}")
        if self.has_warnings:
            parts.append(f"{self.warning_count} warning{'s' if self.warning_count != 1 else ''}")
        
        return f"Validation result: {', '.join(parts)}"
    
    def __repr__(self) -> str:
        """Detailed representation of validation result.
        
        Returns:
            Detailed string representation for debugging
        """
        return f"ValidationResult(errors={self.error_count}, warnings={self.warning_count})"
    
    def __bool__(self) -> bool:
        """Boolean evaluation of validation result.
        
        Returns:
            True if validation is valid (no errors), False otherwise
        """
        return self.is_valid
    
    def __eq__(self, other) -> bool:
        """Check equality with another ValidationResult.
        
        Args:
            other: Object to compare with
            
        Returns:
            True if both have the same errors and warnings, False otherwise
        """
        if not isinstance(other, ValidationResult):
            return False
        return self._errors == other._errors and self._warnings == other._warnings