"""
Contrast and CT Module Enums - Enumerated values for contrast and CT-related DICOM modules.

This module contains enumerated values used across contrast/bolus and CT image modules
including CT Image, Multi-energy CT Image, Contrast/Bolus, and General Acquisition modules.
"""
from enum import Enum


class ContrastBolusIngredient(Enum):
    """Contrast/Bolus Ingredient (0018,1048) - DICOM VR: CS
    
    Defined Terms per DICOM PS3.3 C.7.6.4:
    - IODINE = Iodine-based contrast agent
    - GADOLINIUM = Gadolinium-based contrast agent
    - CARBON_DIOXIDE = Carbon dioxide contrast agent
    - BARIUM = Barium-based contrast agent
    """
    IODINE = "IODINE"
    GADOLINIUM = "GADOLINIUM"
    CARBON_DIOXIDE = "CARBON DIOXIDE"
    BARIUM = "BARIUM"


class MultiEnergyCTAcquisition(Enum):
    """Multi-energy CT Acquisition (0018,9361) - DICOM VR: CS
    
    Defined Terms per DICOM PS3.3 C.8.2.1:
    - YES = Image is created by means of Multi-energy technique
    - NO = Image is not created by means of Multi-energy technique
    
    Note: PS3.3 reference to be added - see missing_dicom_references.md
    """
    YES = "YES"
    NO = "NO"


class RotationDirection(Enum):
    """Rotation Direction (0018,1140) - DICOM VR: CS
    
    Defined Terms per DICOM PS3.3 C.8.2.1:
    - CW = Clockwise rotation
    - CC = Counter clockwise rotation
    
    Note: PS3.3 reference to be added - see missing_dicom_references.md
    """
    CW = "CW"
    CC = "CC"


class ExposureModulationType(Enum):
    """Exposure Modulation Type (0018,9323) - DICOM VR: CS
    
    Defined Terms per DICOM PS3.3 C.8.2.1:
    - NONE = No exposure modulation used
    
    Note: PS3.3 reference to be added - see missing_dicom_references.md
    """
    NONE = "NONE"


class CTImageTypeValue1(Enum):
    """CT Image Type Value 1 (0008,0008) - DICOM VR: CS
    
    Defined Terms per DICOM PS3.3 C.8.2.1:
    - ORIGINAL = Original Image
    - DERIVED = Derived Image
    """
    ORIGINAL = "ORIGINAL"
    DERIVED = "DERIVED"


class CTImageTypeValue2(Enum):
    """CT Image Type Value 2 (0008,0008) - DICOM VR: CS
    
    Defined Terms per DICOM PS3.3 C.8.2.1:
    - PRIMARY = Primary Image
    - SECONDARY = Secondary Image
    """
    PRIMARY = "PRIMARY"
    SECONDARY = "SECONDARY"


class CTImageTypeValue3(Enum):
    """CT Image Type Value 3 (0008,0008) - DICOM VR: CS
    
    Defined Terms per DICOM PS3.3 C.8.2.1:
    - AXIAL = CT Cross-sectional Image
    - LOCALIZER = CT Localizer Image
    """
    AXIAL = "AXIAL"
    LOCALIZER = "LOCALIZER"


class CTImageTypeValue4(Enum):
    """CT Image Type Value 4 for Multi-energy CT (0008,0008) - DICOM VR: CS
    
    Defined Terms per DICOM PS3.3 C.8.2.1:
    - VMI = Virtual Monoenergetic Image
    - MAT_SPECIFIC = Material-Specific Image
    - MAT_REMOVED = Material-Removed Image
    - MAT_FRACTIONAL = Material-Fractional Image
    - EFF_ATOMIC_NUM = Effective Atomic Number Image
    - ELECTRON_DENSITY = Electron Density Image
    - MAT_MODIFIED = Material-Modified Image
    - MAT_VALUE_BASED = Value-Based Image
    
    Note: PS3.3 reference to be added - see missing_dicom_references.md
    """
    VMI = "VMI"
    MAT_SPECIFIC = "MAT_SPECIFIC"
    MAT_REMOVED = "MAT_REMOVED"
    MAT_FRACTIONAL = "MAT_FRACTIONAL"
    EFF_ATOMIC_NUM = "EFF_ATOMIC_NUM"
    ELECTRON_DENSITY = "ELECTRON_DENSITY"
    MAT_MODIFIED = "MAT_MODIFIED"
    MAT_VALUE_BASED = "MAT_VALUE_BASED"


class CTSamplesPerPixel(Enum):
    """CT Samples per Pixel (0028,0002) - DICOM VR: US
    
    Defined Terms per DICOM PS3.3 C.7.6.3:
    - 1 = Single sample per pixel for CT images
    """
    ONE = 1


class CTBitsAllocated(Enum):
    """CT Bits Allocated (0028,0100) - DICOM VR: US
    
    Defined Terms per DICOM PS3.3 C.7.6.3:
    - 16 = 16 bits allocated for CT images
    """
    SIXTEEN = 16


class CTBitsStored(Enum):
    """CT Bits Stored (0028,0101) - DICOM VR: US
    
    Defined Terms per DICOM PS3.3 C.7.6.3:
    - 12 = 12 bits stored
    - 13 = 13 bits stored
    - 14 = 14 bits stored
    - 15 = 15 bits stored
    - 16 = 16 bits stored
    """
    TWELVE = 12
    THIRTEEN = 13
    FOURTEEN = 14
    FIFTEEN = 15
    SIXTEEN = 16


class MultiEnergySourceTechnique(Enum):
    """Multi-energy Source Technique (0018,9368) - DICOM VR: CS
    
    Defined Terms per DICOM PS3.3 C.8.2.1:
    - SWITCHING_SOURCE = Physical X-Ray source uses beam mode switching
    - CONSTANT_SOURCE = Physical X-Ray source uses beam with constant characteristics
    
    Note: PS3.3 reference to be added - see missing_dicom_references.md
    """
    SWITCHING_SOURCE = "SWITCHING_SOURCE"
    CONSTANT_SOURCE = "CONSTANT_SOURCE"


class MultiEnergyDetectorType(Enum):
    """Multi-energy Detector Type (0018,9372) - DICOM VR: CS
    
    Defined Terms per DICOM PS3.3 C.8.2.1:
    - INTEGRATING = Physical detector integrates the full X-Ray spectrum
    - MULTILAYER = Physical detector layers absorb different parts of X-Ray spectrum
    - PHOTON_COUNTING = Physical detector counts photons with energy discrimination
    
    Note: PS3.3 reference to be added - see missing_dicom_references.md
    """
    INTEGRATING = "INTEGRATING"
    MULTILAYER = "MULTILAYER"
    PHOTON_COUNTING = "PHOTON_COUNTING"


class RescaleType(Enum):
    """Rescale Type (0028,1054) - DICOM VR: LO
    
    Defined Terms per DICOM PS3.3 C.7.6.1:
    - HU = Hounsfield Units
    - US = Unspecified
    - MGML = Milligrams per milliliter
    - PCNT = Percent
    - CPS = Counts per second
    - NONE = No rescaling function
    - CM = Centimeters
    - MM = Millimeters
    - PIXVAL = Pixel value
    - COUNTS = Counts
    - PROPCNT = Proportional to counts
    - DISP = Display units
    - UMOL = Micromoles
    - CONC = Concentration
    - RWU = Relative water units
    - DENS = Density
    - TEMP = Temperature
    - FLOW = Flow
    - PERF = Perfusion
    - DIFF = Diffusion
    - RELAX = Relaxation
    - METAB = Metabolic
    - RATIO = Ratio
    - OTHER = Other units
    - DVPX = Device-specific units
    - COEF = Coefficient
    - GRAD = Gradient
    - FRAC = Fraction
    - MASS = Mass
    - MLMIN = Milliliters per minute
    - MLMINM2 = Milliliters per minute per square meter
    - ML100GM = Milliliters per 100 grams
    - MLMIN100G = Milliliters per minute per 100 grams
    - DEGC = Degrees Celsius
    - SEC = Seconds
    - MSEC = Milliseconds
    - USEC = Microseconds
    - HZ = Hertz
    - PPM = Parts per million
    - RAD = Radians
    - DEG = Degrees
    
    Note: PS3.3 reference to be added - see missing_dicom_references.md
    """
    HU = "HU"
    US = "US"
    MGML = "MGML"
    PCNT = "PCNT"
    CPS = "CPS"
    NONE = "NONE"
    CM = "CM"
    MM = "MM"
    PIXVAL = "PIXVAL"
    COUNTS = "COUNTS"
    PROPCNT = "PROPCNT"
    DISP = "DISP"
    UMOL = "UMOL"
    CONC = "CONC"
    RWU = "RWU"
    DENS = "DENS"
    TEMP = "TEMP"
    FLOW = "FLOW"
    PERF = "PERF"
    DIFF = "DIFF"
    RELAX = "RELAX"
    METAB = "METAB"
    RATIO = "RATIO"
    OTHER = "OTHER"
    DVPX = "DVPX"
    COEF = "COEF"
    GRAD = "GRAD"
    FRAC = "FRAC"
    MASS = "MASS"
    MLMIN = "MLMIN"
    MLMINM2 = "MLMINM2"
    ML100GM = "ML100GM"
    MLMIN100G = "MLMIN100G"
    DEGC = "DEGC"
    SEC = "SEC"
    MSEC = "MSEC"
    USEC = "USEC"
    HZ = "HZ"
    PPM = "PPM"
    RAD = "RAD"
    DEG = "DEG"


class FilterMaterial(Enum):
    """Filter Material (0018,7050) - DICOM VR: CS
    
    Defined Terms per DICOM PS3.3 C.8.2.1:
    - AL = Aluminum
    - CU = Copper
    - MO = Molybdenum
    - RH = Rhodium
    - AG = Silver
    - SN = Tin
    - GD = Gadolinium
    - HO = Holmium
    - ER = Erbium
    - YB = Ytterbium
    - W = Tungsten
    - PB = Lead
    - TI = Titanium
    - NI = Nickel
    - FE = Iron
    - BE = Beryllium
    - C = Carbon
    - POLYIMIDE = Polyimide
    - LEXAN = Lexan
    - KAPTON = Kapton
    - MYLAR = Mylar
    
    Note: PS3.3 reference to be added - see missing_dicom_references.md
    """
    ALUMINUM = "AL"
    COPPER = "CU"
    MOLYBDENUM = "MO"
    RHODIUM = "RH"
    SILVER = "AG"
    TIN = "SN"
    GADOLINIUM = "GD"
    HOLMIUM = "HO"
    ERBIUM = "ER"
    YTTERBIUM = "YB"
    TUNGSTEN = "W"
    LEAD = "PB"
    TITANIUM = "TI"
    NICKEL = "NI"
    IRON = "FE"
    BERYLLIUM = "BE"
    CARBON = "C"
    POLYIMIDE = "POLYIMIDE"
    LEXAN = "LEXAN"
    KAPTON = "KAPTON"
    MYLAR = "MYLAR"


class ScanOptions(Enum):
    """Scan Options (0018,0022) - DICOM VR: CS
    
    Defined Terms per DICOM PS3.3 C.8.2.1:
    - HELICAL_CT = Helical/spiral CT scan
    - AXIAL_CT = Axial/sequential CT scan
    - CINE_CT = Cine CT scan
    - CARDIAC_GATING = Cardiac gated scan
    - RESPIRATORY_GATING = Respiratory gated scan
    - CONTRAST_ENHANCED = Contrast enhanced scan
    - NON_CONTRAST = Non-contrast scan
    - PERFUSION = Perfusion scan
    - ANGIOGRAPHY = CT angiography
    - CALCIUM_SCORING = Calcium scoring scan
    
    Note: This is not a DICOM-defined enumeration but provides common values
    for convenience. Custom values are allowed per DICOM standard.
    """
    HELICAL_CT = "HELICAL_CT"
    AXIAL_CT = "AXIAL_CT"
    CINE_CT = "CINE_CT"
    CARDIAC_GATING = "CARDIAC_GATING"
    RESPIRATORY_GATING = "RESPIRATORY_GATING"
    CONTRAST_ENHANCED = "CONTRAST_ENHANCED"
    NON_CONTRAST = "NON_CONTRAST"
    PERFUSION = "PERFUSION"
    ANGIOGRAPHY = "ANGIOGRAPHY"
    CALCIUM_SCORING = "CALCIUM_SCORING"


class FilterType(Enum):
    """Filter Type (0018,1160) - DICOM VR: CS
    
    Defined Terms per DICOM PS3.3 C.8.2.1:
    - NONE = No filter
    - ALUMINUM = Aluminum filter
    - COPPER = Copper filter
    - MOLYBDENUM = Molybdenum filter
    - RHODIUM = Rhodium filter
    - SILVER = Silver filter
    - TIN = Tin filter
    - GADOLINIUM = Gadolinium filter
    - TUNGSTEN = Tungsten filter
    - LEAD = Lead filter
    - TITANIUM = Titanium filter
    - BERYLLIUM = Beryllium filter
    - CARBON = Carbon filter
    - COMPOSITE = Composite filter
    - WEDGE = Wedge filter
    - BOW_TIE = Bow-tie filter
    - FLAT = Flat filter
    
    Note: This is not a DICOM-defined enumeration but provides common values
    for convenience. Custom values are allowed per DICOM standard.
    """
    NONE = "NONE"
    ALUMINUM = "ALUMINUM"
    COPPER = "COPPER"
    MOLYBDENUM = "MOLYBDENUM"
    RHODIUM = "RHODIUM"
    SILVER = "SILVER"
    TIN = "TIN"
    GADOLINIUM = "GADOLINIUM"
    TUNGSTEN = "TUNGSTEN"
    LEAD = "LEAD"
    TITANIUM = "TITANIUM"
    BERYLLIUM = "BERYLLIUM"
    CARBON = "CARBON"
    COMPOSITE = "COMPOSITE"
    WEDGE = "WEDGE"
    BOW_TIE = "BOW_TIE"
    FLAT = "FLAT"
