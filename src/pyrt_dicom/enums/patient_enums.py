"""Patient-related DICOM enumerations."""

from enum import Enum


class PatientSex(Enum):
    """Patient Sex (0010,0040) - DICOM VR: CS
    
    Defined Terms per DICOM PS3.3 C.7.1.1:
    - M = Male
    - F = Female  
    - O = Other
    """
    MALE = "M"
    FEMALE = "F" 
    OTHER = "O"


class ResponsiblePersonRole(Enum):
    """Responsible Person Role (0010,2298) - DICOM VR: CS
    
    Defined Terms per DICOM PS3.3 C.7.1.1.1.2:
    - OWNER = Owner of the patient
    - PARENT = Parent of the patient
    - CHILD = Child of the patient
    - SPOUSE = Spouse of the patient
    - SIBLING = Sibling of the patient
    - RELATIVE = Other relative of the patient
    - G<PERSON><PERSON><PERSON><PERSON> = Legal guardian of the patient
    - CUSTO<PERSON>AN = Legal custodian of the patient
    - AGENT = Agent acting on behalf of the patient
    - INVESTIGATOR = Investigator responsible for the patient
    - VETERINARIAN = Veterinarian responsible for the patient
    """
    OWNER = "OWNER"
    PARENT = "PARENT"
    CHILD = "CHILD"
    SPOUSE = "SPOUSE"
    SIBLING = "SIBLING"
    RELATIVE = "RELATIVE"
    GUARDIAN = "GUARDIAN"
    CUSTODIAN = "CUSTODIAN"
    AGENT = "AGENT"
    INVESTIGATOR = "INVESTIGATOR"
    VETERINARIAN = "VETERINARIAN"


class TypeOfPatientID(Enum):
    """Type of Patient ID (0010,0022) - DICOM VR: CS
    
    Defined Terms per DICOM PS3.3 C.7.1.1:
    - TEXT = Text-based patient identifier
    - RFID = Radio Frequency Identification based identifier
    - BARCODE = Barcode-based identifier
    """
    TEXT = "TEXT"
    RFID = "RFID"
    BARCODE = "BARCODE"