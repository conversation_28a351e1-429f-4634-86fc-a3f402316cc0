"""Radiotherapy-specific DICOM enumerations."""

from enum import Enum



class ConversionType(Enum):
    """Conversion Type (0008,0064) - DICOM VR: CS
    
    Defined Terms per DICOM PS3.3 C.7.6.1:
    - DV = Digitized Video
    - DI = Digital Interface
    - DF = Digitized Film
    - WSD = Workstation
    """
    DV = "DV"
    DI = "DI"
    DF = "DF"
    WSD = "WSD"


class BeamType(Enum):
    """Beam Type (300A,00C4) - DICOM VR: CS
    
    Defined Terms per DICOM PS3.3 C.8.8.14:
    - STATIC = All Control Point Sequence remain unchanged between pairs with changing Cumulative Meterset Weight
    - DYNAMIC = One or more Control Point Sequence change between pairs with changing Cumulative Meterset Weight
    """
    STATIC = "STATIC"
    DYNAMIC = "DYNAMIC"


class RadiationType(Enum):
    """Radiation Type (300A,00C6) - DICOM VR: CS
    
    Defined Terms per DICOM PS3.3 C.8.8.14:
    - PHOTON = Photon beam
    - ELECTRON = Electron beam
    - NEUTRON = Neutron beam
    - PROTON = Proton beam
    """
    PHOTON = "PHOTON"
    ELECTRON = "ELECTRON"
    NEUTRON = "NEUTRON"
    PROTON = "PROTON"


class ReportedValuesOrigin(Enum):
    """Reported Values Origin (3002,000A) - DICOM VR: CS
    
    Defined Terms per DICOM PS3.3 C.8.8.8:
    - OPERATOR = Manually entered by operator
    - PLAN = Planned parameter values
    - ACTUAL = Electronically recorded
    """
    OPERATOR = "OPERATOR"
    PLAN = "PLAN"
    ACTUAL = "ACTUAL"


class RTImagePlane(Enum):
    """RT Image Plane (3002,000C) - DICOM VR: CS
    
    Defined Terms per DICOM PS3.3 C.8.8.8:
    - NORMAL = Image plane normal to beam axis
    - NON_NORMAL = Image plane non-normal to beam axis
    """
    NORMAL = "NORMAL"
    NON_NORMAL = "NON_NORMAL"


class PrimaryDosimeterUnit(Enum):
    """Primary Dosimeter Unit (300A,00B3) - DICOM VR: CS
    
    Defined Terms per DICOM PS3.3 C.8.8.14:
    - MU = Monitor Unit
    - MINUTE = Minute
    """
    MU = "MU"
    MINUTE = "MINUTE"


class PixelIntensityRelationshipSign(Enum):
    """Pixel Intensity Relationship Sign (0028,1041) - DICOM VR: SS
    
    Defined Terms per DICOM PS3.3 C.7.6.3:
    - 1 = Lower pixel values correspond to less X-Ray beam intensity
    - -1 = Higher pixel values correspond to less X-Ray beam intensity
    """
    POSITIVE = 1
    NEGATIVE = -1


class FluenceMode(Enum):
    """Fluence Mode (3002,0051) - DICOM VR: CS
    
    Defined Terms per DICOM PS3.3 C.8.8.8:
    - STANDARD = Uses standard fluence-shaping
    - NON_STANDARD = Uses a non-standard fluence-shaping mode
    """
    STANDARD = "STANDARD"
    NON_STANDARD = "NON_STANDARD"


class RTBeamLimitingDeviceType(Enum):
    """RT Beam Limiting Device Type (300A,00B8) - DICOM VR: CS
    
    Defined Terms per DICOM PS3.3 C.8.8.14:
    - X = Symmetric jaw pair in IEC X direction
    - Y = Symmetric jaw pair in IEC Y direction
    - ASYMX = Asymmetric jaw pair in IEC X direction
    - ASYMY = Asymmetric jaw pair in IEC Y direction
    - MLCX = Single layer multileaf collimator in IEC X direction
    - MLCY = Single layer multileaf collimator in IEC Y direction
    """
    X = "X"
    Y = "Y"
    ASYMX = "ASYMX"
    ASYMY = "ASYMY"
    MLCX = "MLCX"
    MLCY = "MLCY"


class ApplicatorType(Enum):
    """Applicator Type (300A,0109) - DICOM VR: CS
    
    Defined Terms per DICOM PS3.3 C.8.8.14:
    - ELECTRON_SQUARE = Square electron applicator
    - ELECTRON_RECT = Rectangular electron applicator
    - ELECTRON_CIRC = Circular electron applicator
    - ELECTRON_SHORT = Short electron applicator
    - ELECTRON_OPEN = Open (dummy) electron applicator
    - PHOTON_SQUARE = Square photon applicator
    - PHOTON_RECT = Rectangular photon applicator
    - PHOTON_CIRC = Circular photon applicator
    - INTRAOPERATIVE = Intraoperative (custom) applicator
    - STEREOTACTIC = Stereotactic applicator (deprecated)
    """
    ELECTRON_SQUARE = "ELECTRON_SQUARE"
    ELECTRON_RECT = "ELECTRON_RECT"
    ELECTRON_CIRC = "ELECTRON_CIRC"
    ELECTRON_SHORT = "ELECTRON_SHORT"
    ELECTRON_OPEN = "ELECTRON_OPEN"
    PHOTON_SQUARE = "PHOTON_SQUARE"
    PHOTON_RECT = "PHOTON_RECT"
    PHOTON_CIRC = "PHOTON_CIRC"
    INTRAOPERATIVE = "INTRAOPERATIVE"
    STEREOTACTIC = "STEREOTACTIC"


class ApplicatorApertureShape(Enum):
    """Applicator Aperture Shape (300A,0432) - DICOM VR: CS
    
    Defined Terms per DICOM PS3.3 C.8.8.14:
    - SYM_SQUARE = Square-shaped aperture symmetrical to central axis
    - SYM_RECTANGLE = Rectangular-shaped aperture symmetrical to central axis
    - SYM_CIRCULAR = Circular-shaped aperture symmetrical to central axis
    """
    SYM_SQUARE = "SYM_SQUARE"
    SYM_RECTANGLE = "SYM_RECTANGLE"
    SYM_CIRCULAR = "SYM_CIRCULAR"


class GeneralAccessoryType(Enum):
    """General Accessory Type (300A,0423) - DICOM VR: CS
    
    Defined Terms per DICOM PS3.3 C.8.8.14:
    - GRATICULE = Accessory tray with a radio-opaque grid
    - IMAGE_DETECTOR = Image acquisition device positioned in the beam line
    - RETICLE = Accessory tray with radio-transparent markers or grid
    """
    GRATICULE = "GRATICULE"
    IMAGE_DETECTOR = "IMAGE_DETECTOR"
    RETICLE = "RETICLE"


class BlockType(Enum):
    """Block Type (300A,00F8) - DICOM VR: CS
    
    Defined Terms per DICOM PS3.3 C.8.8.14:
    - SHIELDING = Blocking material is inside contour
    - APERTURE = Blocking material is outside contour
    """
    SHIELDING = "SHIELDING"
    APERTURE = "APERTURE"


class BlockDivergence(Enum):
    """Block Divergence (300A,00FA) - DICOM VR: CS
    
    Defined Terms per DICOM PS3.3 C.8.8.14:
    - PRESENT = Block edges are shaped for beam divergence
    - ABSENT = Block edges are not shaped for beam divergence
    """
    PRESENT = "PRESENT"
    ABSENT = "ABSENT"


class BlockMountingPosition(Enum):
    """Block Mounting Position (300A,00FB) - DICOM VR: CS
    
    Defined Terms per DICOM PS3.3 C.8.8.14:
    - PATIENT_SIDE = Block mounted on side towards patient
    - SOURCE_SIDE = Block mounted on side towards radiation source
    """
    PATIENT_SIDE = "PATIENT_SIDE"
    SOURCE_SIDE = "SOURCE_SIDE"


class FluenceDataSource(Enum):
    """Fluence Data Source (3002,0041) - DICOM VR: CS
    
    Defined Terms per DICOM PS3.3 C.8.8.8:
    - CALCULATED = Calculated by a workstation
    - MEASURED = Measured by exposure to a film or detector
    """
    CALCULATED = "CALCULATED"
    MEASURED = "MEASURED"


class SpatialTransformOfDose(Enum):
    """Spatial Transform of Dose (3004,0005) - DICOM VR: CS
    
    Defined Terms per DICOM PS3.3 C.8.8.26:
    - NONE = No transformation
    - RIGID = Only Rigid transform used
    - NON_RIGID = Any other transform used
    """
    NONE = "NONE"
    RIGID = "RIGID"
    NON_RIGID = "NON_RIGID"


class DoseSummationType(Enum):
    """Dose Summation Type (3004,000A) - DICOM VR: CS
    
    Defined Terms per DICOM PS3.3 C.8.8.25:
    - PLAN = Dose for entire delivery of all fraction groups
    - MULTI_PLAN = Dose for entire delivery of 2 or more RT Plans
    - PLAN_OVERVIEW = Dose with respect to plan overview parameters
    - FRACTION = Dose for entire delivery of single Fraction Group
    - BEAM = Dose for entire delivery of one or more Beams
    - BRACHY = Dose for entire delivery of Brachy Application Setups
    - FRACTION_SESSION = Dose for single session of single Fraction Group
    - BEAM_SESSION = Dose for single session of one or more Beams
    - BRACHY_SESSION = Dose for single session of Brachy Application Setups
    - CONTROL_POINT = Dose for one or more Control Points within Beam
    - RECORD = Dose for RT Beams Treatment Record
    """
    PLAN = "PLAN"
    MULTI_PLAN = "MULTI_PLAN"
    PLAN_OVERVIEW = "PLAN_OVERVIEW"
    FRACTION = "FRACTION"
    BEAM = "BEAM"
    BRACHY = "BRACHY"
    FRACTION_SESSION = "FRACTION_SESSION"
    BEAM_SESSION = "BEAM_SESSION"
    BRACHY_SESSION = "BRACHY_SESSION"
    CONTROL_POINT = "CONTROL_POINT"
    RECORD = "RECORD"


class TissueHeterogeneityCorrection(Enum):
    """Tissue Heterogeneity Correction (3004,0014) - DICOM VR: CS
    
    Defined Terms per DICOM PS3.3 C.8.8.25:
    - IMAGE = Image data
    - ROI_OVERRIDE = ROI densities override image or water values
    - WATER = Entire volume treated as water equivalent
    """
    IMAGE = "IMAGE"
    ROI_OVERRIDE = "ROI_OVERRIDE"
    WATER = "WATER"


class DVHType(Enum):
    """DVH Type (3004,0001) - DICOM VR: CS
    
    Defined Terms per DICOM PS3.3 C.8.8.26:
    - DIFFERENTIAL = Differential dose-volume histogram
    - CUMULATIVE = Cumulative dose-volume histogram
    - NATURAL = Natural dose volume histogram
    """
    DIFFERENTIAL = "DIFFERENTIAL"
    CUMULATIVE = "CUMULATIVE"
    NATURAL = "NATURAL"


class DVHROIContributionType(Enum):
    """DVH ROI Contribution Type (3004,0062) - DICOM VR: CS
    
    Defined Terms per DICOM PS3.3 C.8.8.26:
    - INCLUDED = Volume within ROI is included in DVH
    - EXCLUDED = Volume within ROI is excluded from DVH
    """
    INCLUDED = "INCLUDED"
    EXCLUDED = "EXCLUDED"


class DVHVolumeUnits(Enum):
    """DVH Volume Units (3004,0054) - DICOM VR: CS
    
    Defined Terms per DICOM PS3.3 C.8.8.26:
    - CM3 = Cubic centimeters
    - PERCENT = Percent
    - PER_U = Volume per u with u(dose)=dose^-3/2
    """
    CM3 = "CM3"
    PERCENT = "PERCENT"
    PER_U = "PER_U"


class ROIGenerationAlgorithm(Enum):
    """ROI Generation Algorithm (3006,0036) - DICOM VR: CS
    
    Defined Terms per DICOM PS3.3 C.8.8.33:
    - AUTOMATIC = Calculated ROI
    - SEMIAUTOMATIC = ROI calculated with user assistance
    - MANUAL = User-entered ROI
    """
    AUTOMATIC = "AUTOMATIC"
    SEMIAUTOMATIC = "SEMIAUTOMATIC"
    MANUAL = "MANUAL"


class PlanIntent(Enum):
    """Plan Intent (300A,000A) - DICOM VR: CS
    
    Defined Terms per DICOM PS3.3 C.8.8.16:
    - CURATIVE = Curative therapy on patient
    - PALLIATIVE = Palliative therapy on patient
    - PROPHYLACTIC = Preventative therapy on patient
    - VERIFICATION = Verification of patient plan using phantom
    - MACHINE_QA = Quality assurance of delivery machine
    - RESEARCH = Research project
    - SERVICE = Machine repair or maintenance operation
    """
    CURATIVE = "CURATIVE"
    PALLIATIVE = "PALLIATIVE"
    PROPHYLACTIC = "PROPHYLACTIC"
    VERIFICATION = "VERIFICATION"
    MACHINE_QA = "MACHINE_QA"
    RESEARCH = "RESEARCH"
    SERVICE = "SERVICE"


class RTplanGeometry(Enum):
    """RT Plan Geometry (300A,000C) - DICOM VR: CS
    
    Defined Terms per DICOM PS3.3 C.8.8.16:
    - PATIENT = RT Structure Set exists
    - TREATMENT_DEVICE = RT Structure Set does not exist
    """
    PATIENT = "PATIENT"
    TREATMENT_DEVICE = "TREATMENT_DEVICE"


class RTPlanRelationship(Enum):
    """RT Plan Relationship (300A,0055) - DICOM VR: CS
    
    Defined Terms per DICOM PS3.3 C.8.8.16:
    - PRIOR = Plan delivered prior to current treatment
    - ALTERNATIVE = Alternative plan prepared for current treatment
    - PREDECESSOR = Plan used in derivation of current plan
    - VERIFIED_PLAN = Plan that is verified using the current plan
    - CONCURRENT = Plan that forms part of set applied in parallel
    """
    PRIOR = "PRIOR"
    ALTERNATIVE = "ALTERNATIVE"
    PREDECESSOR = "PREDECESSOR"
    VERIFIED_PLAN = "VERIFIED_PLAN"
    CONCURRENT = "CONCURRENT"


class RTImageTypeValue3(Enum):
    """RT Image Type Value 3 (0008,0008) - DICOM VR: CS
    
    Defined Terms per DICOM PS3.3 C.8.8.8:
    - DRR = Digitally reconstructed radiograph
    - PORTAL = Digital portal image or portal film image
    - SIMULATOR = Conventional simulator image
    - RADIOGRAPH = Radiographic image
    - BLANK = Image pixels set to background value
    - FLUENCE = Fluence map
    """
    DRR = "DRR"
    PORTAL = "PORTAL"
    SIMULATOR = "SIMULATOR"
    RADIOGRAPH = "RADIOGRAPH"
    BLANK = "BLANK"
    FLUENCE = "FLUENCE"


class EnhancedRTBeamLimitingDeviceDefinitionFlag(Enum):
    """Enhanced RT Beam Limiting Device Definition Flag (3008,00A3) - DICOM VR: CS
    
    Defined Terms per DICOM PS3.3 C.8.8.23:
    - YES = RT Beam Limiting Devices specified by Enhanced sequence
    - NO = RT Beam Limiting Devices not specified by Enhanced sequence
    """
    YES = "YES"
    NO = "NO"


class ContourGeometricType(Enum):
    """Contour Geometric Type (3006,0042) - DICOM VR: CS
    
    Defined Terms per DICOM PS3.3 C.8.8.33:
    - POINT = Single point
    - OPEN_PLANAR = Open contour containing coplanar points
    - OPEN_NONPLANAR = Open contour containing non-coplanar points
    - CLOSED_PLANAR = Closed contour (polygon) containing coplanar points
    - CLOSEDPLANAR_XOR = Closed contour combined using XOR operator
    """
    POINT = "POINT"
    OPEN_PLANAR = "OPEN_PLANAR"
    OPEN_NONPLANAR = "OPEN_NONPLANAR"
    CLOSED_PLANAR = "CLOSED_PLANAR"
    CLOSEDPLANAR_XOR = "CLOSEDPLANAR_XOR"


class RTROIInterpretedType(Enum):
    """RT ROI Interpreted Type (3006,00A4) - DICOM VR: CS
    
    Defined Terms per DICOM PS3.3 C.8.8.33:
    - EXTERNAL = External patient contour
    - PTV = Planning Target Volume
    - CTV = Clinical Target Volume
    - GTV = Gross Tumor Volume
    - TREATED_VOLUME = Treated Volume
    - IRRAD_VOLUME = Irradiated Volume
    - OAR = Organ at Risk
    - BOLUS = Patient bolus for external beam therapy
    - AVOIDANCE = Region in which dose is to be minimized
    - ORGAN = Patient organ
    - MARKER = Patient marker or marker on localizer
    - REGISTRATION = Registration ROI
    - ISOCENTER = Treatment isocenter for external beam therapy
    - CONTRAST_AGENT = Volume with contrast agent injection
    - CAVITY = Patient anatomical cavity
    - BRACHY_CHANNEL = Brachytherapy channel
    - BRACHY_ACCESSORY = Brachytherapy accessory device
    - BRACHY_SRC_APP = Brachytherapy source applicator
    - BRACHY_CHNL_SHLD = Brachytherapy channel shield
    - SUPPORT = External patient support device
    - FIXATION = External patient fixation or immobilization device
    - DOSE_REGION = ROI to be used as dose reference
    - CONTROL = ROI for dose optimization and calculation control
    - DOSE_MEASUREMENT = ROI representing dose measurement device
    - DEVICE = Device not addressed by another defined term
    """
    EXTERNAL = "EXTERNAL"
    PTV = "PTV"
    CTV = "CTV"
    GTV = "GTV"
    TREATED_VOLUME = "TREATED_VOLUME"
    IRRAD_VOLUME = "IRRAD_VOLUME"
    OAR = "OAR"
    BOLUS = "BOLUS"
    AVOIDANCE = "AVOIDANCE"
    ORGAN = "ORGAN"
    MARKER = "MARKER"
    REGISTRATION = "REGISTRATION"
    ISOCENTER = "ISOCENTER"
    CONTRAST_AGENT = "CONTRAST_AGENT"
    CAVITY = "CAVITY"
    BRACHY_CHANNEL = "BRACHY_CHANNEL"
    BRACHY_ACCESSORY = "BRACHY_ACCESSORY"
    BRACHY_SRC_APP = "BRACHY_SRC_APP"
    BRACHY_CHNL_SHLD = "BRACHY_CHNL_SHLD"
    SUPPORT = "SUPPORT"
    FIXATION = "FIXATION"
    DOSE_REGION = "DOSE_REGION"
    CONTROL = "CONTROL"
    DOSE_MEASUREMENT = "DOSE_MEASUREMENT"
    DEVICE = "DEVICE"


class RTROIRelationship(Enum):
    """RT ROI Relationship (3006,0033) - DICOM VR: CS
    
    Defined Terms per DICOM PS3.3 C.8.8.33:
    - SAME = ROIs represent the same entity
    - ENCLOSED = Referenced ROI completely encloses referencing ROI
    - ENCLOSING = Referencing ROI completely encloses referenced ROI
    """
    SAME = "SAME"
    ENCLOSED = "ENCLOSED"
    ENCLOSING = "ENCLOSING"


class ROIPhysicalProperty(Enum):
    """ROI Physical Property (3006,00B2) - DICOM VR: CS
    
    Defined Terms per DICOM PS3.3 C.8.8.33:
    - REL_MASS_DENSITY = Mass density relative to water
    - REL_ELEC_DENSITY = Electron density relative to water
    - EFFECTIVE_Z = Effective atomic number
    - EFF_Z_PER_A = Ratio of effective atomic number to mass (AMU^-1)
    - REL_STOP_RATIO = Ratio of linear stopping power relative to water
    - ELEM_FRACTION = Elemental composition of the material
    - MEAN_EXCI_ENERGY = Mean Excitation Energy of the material (eV)
    """
    REL_MASS_DENSITY = "REL_MASS_DENSITY"
    REL_ELEC_DENSITY = "REL_ELEC_DENSITY"
    EFFECTIVE_Z = "EFFECTIVE_Z"
    EFF_Z_PER_A = "EFF_Z_PER_A"
    REL_STOP_RATIO = "REL_STOP_RATIO"
    ELEM_FRACTION = "ELEM_FRACTION"
    MEAN_EXCI_ENERGY = "MEAN_EXCI_ENERGY"


class DoseReferenceStructureType(Enum):
    """Dose Reference Structure Type (300A,0014) - DICOM VR: CS
    
    Defined Terms per DICOM PS3.3 C.8.8.16:
    - POINT = Dose reference point specified as ROI
    - VOLUME = Dose reference volume specified as ROI
    - COORDINATES = Point specified by coordinates
    - SITE = Dose reference clinical site
    """
    POINT = "POINT"
    VOLUME = "VOLUME"
    COORDINATES = "COORDINATES"
    SITE = "SITE"


class DoseReferenceType(Enum):
    """Dose Reference Type (300A,0020) - DICOM VR: CS
    
    Defined Terms per DICOM PS3.3 C.8.8.16:
    - TARGET = Treatment target (GTV, PTV, or CTV)
    - ORGAN_AT_RISK = Organ at Risk
    """
    TARGET = "TARGET"
    ORGAN_AT_RISK = "ORGAN_AT_RISK"


class DoseValuePurpose(Enum):
    """Dose Value Purpose (300A,061D) - DICOM VR: CS
    
    Defined Terms per DICOM PS3.3 C.8.8.24:
    - TRACKING = Dose values used for tracking
    - QA = Dose values used for quality assurance
    """
    TRACKING = "TRACKING"
    QA = "QA"


class DoseValueInterpretation(Enum):
    """Dose Value Interpretation (300A,068B) - DICOM VR: CS
    
    Defined Terms per DICOM PS3.3 C.8.8.24:
    - NOMINAL = Nominal dose values
    - ACTUAL = Actual dose values
    """
    NOMINAL = "NOMINAL"
    ACTUAL = "ACTUAL"


class FixationDeviceType(Enum):
    """Fixation Device Type (300A,0192) - DICOM VR: CS
    
    Defined Terms per DICOM PS3.3 C.8.8.21:
    - BITEBLOCK = Bite block fixation device
    - HEADFRAME = Head frame fixation device
    - MASK = Mask fixation device
    - MOLD = Mold fixation device
    - CAST = Cast fixation device
    - HEADREST = Head rest fixation device
    - BREAST_BOARD = Breast board fixation device
    - BODY_FRAME = Body frame fixation device
    - VACUUM_MOLD = Vacuum mold fixation device
    - WHOLE_BODY_POD = Whole body pod fixation device
    - RECTAL_BALLOON = Rectal balloon fixation device
    """
    BITEBLOCK = "BITEBLOCK"
    HEADFRAME = "HEADFRAME"
    MASK = "MASK"
    MOLD = "MOLD"
    CAST = "CAST"
    HEADREST = "HEADREST"
    BREAST_BOARD = "BREAST_BOARD"
    BODY_FRAME = "BODY_FRAME"
    VACUUM_MOLD = "VACUUM_MOLD"
    WHOLE_BODY_POD = "WHOLE_BODY_POD"
    RECTAL_BALLOON = "RECTAL_BALLOON"


class ShieldingDeviceType(Enum):
    """Shielding Device Type (300A,01A2) - DICOM VR: CS
    
    Defined Terms per DICOM PS3.3 C.8.8.21:
    - GUM = Gum shield
    - EYE = Eye shield
    - GONAD = Gonad shield
    """
    GUM = "GUM"
    EYE = "EYE"
    GONAD = "GONAD"


class SetupTechnique(Enum):
    """Setup Technique (300A,01B0) - DICOM VR: CS
    
    Defined Terms per DICOM PS3.3 C.8.8.21:
    - ISOCENTRIC = Isocentric setup technique
    - FIXED_SSD = Fixed source-to-surface distance
    - TBI = Total body irradiation
    - BREAST_BRIDGE = Breast bridge technique
    - SKIN_APPOSITION = Skin apposition technique
    """
    ISOCENTRIC = "ISOCENTRIC"
    FIXED_SSD = "FIXED_SSD"
    TBI = "TBI"
    BREAST_BRIDGE = "BREAST_BRIDGE"
    SKIN_APPOSITION = "SKIN_APPOSITION"


class SetupDeviceType(Enum):
    """Setup Device Type (300A,01B6) - DICOM VR: CS
    
    Defined Terms per DICOM PS3.3 C.8.8.21:
    - LASER_POINTER = Laser pointer alignment device
    - DISTANCE_METER = Distance measurement device
    - TABLE_HEIGHT = Table height adjustment device
    - MECHANICAL_PTR = Mechanical pointer device
    - ARC = Arc alignment device
    """
    LASER_POINTER = "LASER_POINTER"
    DISTANCE_METER = "DISTANCE_METER"
    TABLE_HEIGHT = "TABLE_HEIGHT"
    MECHANICAL_PTR = "MECHANICAL_PTR"
    ARC = "ARC"


class RespiratoryMotionCompensationTechnique(Enum):
    """Respiratory Motion Compensation Technique (0018,9170) - DICOM VR: CS
    
    Defined Terms per DICOM PS3.3 C.8.15.3.6:
    - NONE = No respiratory motion compensation
    - BREATH_HOLD = Breath hold technique
    - REALTIME = Real-time image acquisition
    - GATING = Prospective gating
    - TRACKING = Prospective motion tracking
    - PHASE_ORDERING = Prospective phase ordering
    - PHASE_RESCANNING = Prospective phase rescanning
    - RETROSPECTIVE = Retrospective gating
    - CORRECTION = Retrospective image correction
    - UNKNOWN = Technique not known
    """
    NONE = "NONE"
    BREATH_HOLD = "BREATH_HOLD"
    REALTIME = "REALTIME"
    GATING = "GATING"
    TRACKING = "TRACKING"
    PHASE_ORDERING = "PHASE_ORDERING"
    PHASE_RESCANNING = "PHASE_RESCANNING"
    RETROSPECTIVE = "RETROSPECTIVE"
    CORRECTION = "CORRECTION"
    UNKNOWN = "UNKNOWN"


class RespiratorySignalSource(Enum):
    """Respiratory Signal Source (0018,9171) - DICOM VR: CS
    
    Defined Terms per DICOM PS3.3 C.8.15.3.6:
    - NONE = No respiratory signal source
    - BELT = Belt-based signal
    - NASAL_PROBE = Nasal probe signal
    - CO2_SENSOR = CO2 sensor signal
    - NAVIGATOR = MR navigator signal
    - MR_PHASE = MR phase signal
    - ECG = ECG baseline signal
    - SPIROMETER = Spirometer signal
    - EXTERNAL_MARKER = External motion marker
    - INTERNAL_MARKER = Internal motion marker
    - IMAGE = Image-derived signal
    - UNKNOWN = Signal source not known
    """
    NONE = "NONE"
    BELT = "BELT"
    NASAL_PROBE = "NASAL_PROBE"
    CO2_SENSOR = "CO2_SENSOR"
    NAVIGATOR = "NAVIGATOR"
    MR_PHASE = "MR_PHASE"
    ECG = "ECG"
    SPIROMETER = "SPIROMETER"
    EXTERNAL_MARKER = "EXTERNAL_MARKER"
    INTERNAL_MARKER = "INTERNAL_MARKER"
    IMAGE = "IMAGE"
    UNKNOWN = "UNKNOWN"


class BeamDoseMeaning(Enum):
    """Beam Dose Meaning (300A,008B) - DICOM VR: CS
    
    Defined Terms per DICOM PS3.3 C.8.8.14:
    - BEAM_LEVEL = Beam Dose value is individually calculated for this Beam
    - FRACTION_LEVEL = Dose is calculated on the Fraction level
    """
    BEAM_LEVEL = "BEAM_LEVEL"
    FRACTION_LEVEL = "FRACTION_LEVEL"


class DoseCalibrationConditionsVerifiedFlag(Enum):
    """Dose Calibration Conditions Verified Flag (300C,0123) - DICOM VR: CS
    
    Defined Terms per DICOM PS3.3 C.8.8.30:
    - YES = Calibration conditions were verified
    - NO = Calibration conditions were not verified
    """
    YES = "YES"
    NO = "NO"
