"""Common DICOM enumerations for SOP and reference modules."""

from enum import Enum


class SyntheticData(Enum):
    """Synthetic Data (0008,001C) - DICOM VR: CS
    
    Defined Terms per DICOM PS3.3 C.12.1:
    - YES = Some or all content was made artificially rather than acquired
    - NO = Content is a faithful representation of acquired data
    """
    YES = "YES"
    NO = "NO"


class SOPInstanceStatus(Enum):
    """SOP Instance Status (0100,0410) - DICOM VR: CS
    
    Defined Terms per DICOM PS3.3 C.12.1:
    - NS = Not Specified; no special storage status
    - AO = Authorized Original; primary SOP Instance authorized for diagnostic use
    - AC = Authorized Copy; copy of an Authorized Original SOP Instance
    """
    NS = "NS"
    AO = "AO"
    AC = "AC"


class QueryRetrieveView(Enum):
    """Query/Retrieve View (0008,0053) - DICOM VR: CS
    
    Defined Terms per DICOM PS3.3 C.12.1:
    - CLASSIC = Classic view requested during C-MOVE operation
    - ENHANCED = Enhanced view requested during C-MOVE operation
    """
    CLASSIC = "CLASSIC"
    ENHANCED = "ENHANCED"


class ContentQualification(Enum):
    """Content Qualification (0018,9004) - DICOM VR: CS
    
    Defined Terms per DICOM PS3.3 C.12.1:
    - PRODUCT = Product content qualification
    - RESEARCH = Research content qualification
    - SERVICE = Service content qualification
    """
    PRODUCT = "PRODUCT"
    RESEARCH = "RESEARCH"
    SERVICE = "SERVICE"


class LongitudinalTemporalInformationModified(Enum):
    """Longitudinal Temporal Information Modified (0028,0303) - DICOM VR: CS
    
    Defined Terms per DICOM PS3.3 C.12.1:
    - UNMODIFIED = Date and time Attributes have not been modified during de-identification
    - MODIFIED = Date and time Attributes have been modified during de-identification
    - REMOVED = Date and time Attributes have been removed during de-identification
    """
    UNMODIFIED = "UNMODIFIED"
    MODIFIED = "MODIFIED"
    REMOVED = "REMOVED"


class SpatialLocationsPreserved(Enum):
    """Spatial Locations Preserved (0028,135A) - DICOM VR: CS
    
    Defined Terms per DICOM PS3.3 C.12.4:
    - YES = Spatial locations are preserved
    - NO = Spatial locations are not preserved
    - REORIENTED_ONLY = Image has been reoriented only
    """
    YES = "YES"
    NO = "NO"
    REORIENTED_ONLY = "REORIENTED_ONLY"
