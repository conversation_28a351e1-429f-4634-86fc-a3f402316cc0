"""
Device Module - DICOM PS3.3 C.7.6.12

The Device Module identifies and describes devices or calibration objects 
(e.g., catheters, markers, baskets) or other quality control materials 
that are associated with a Study and/or image.
"""
from datetime import datetime, date
from pydicom import Dataset
from .base_module import BaseModule
from ...enums.equipment_enums import DeviceDiameterUnits
from ...validators.modules.device_validator import DeviceValidator
from ...validators.modules.base_validator import ValidationConfig
from ...validators import ValidationResult
from ...utils.dicom_formatters import format_date_value, format_enum_value


class DeviceModule(BaseModule):
    """Device Module implementation for DICOM PS3.3 C.7.6.12.
    
    Uses composition with internal dataset management rather than inheriting
    from pydicom.Dataset for cleaner separation of concerns.
    Identifies and describes devices or calibration objects that are associated 
    with a Study and/or image.
    
    Usage:
        # Create with device sequence including optional details
        device = DeviceModule.from_required_elements(
            device_sequence=[
                DeviceModule.create_device_item(
                    code_value="A-04000",
                    coding_scheme_designator="SRT", 
                    code_meaning="Catheter",
                    manufacturer="ACME Medical",
                    device_diameter=2.5,
                    device_diameter_units=DeviceDiameterUnits.MM,
                    device_description="Diagnostic catheter"
                )
            ]
        )
        
        # Validate
        result = device.validate()
    """

    @classmethod
    def from_required_elements(
        cls,
        device_sequence: list[Dataset]
    ) -> 'DeviceModule':
        """Create DeviceModule from all required (Type 1) data elements.
        
        Args:
            device_sequence (list[Dataset]): Sequence of devices used (0050,0010) Type 1.
                Each item must include Code Sequence Macro attributes (CodeValue, 
                CodingSchemeDesignator, CodeMeaning). Use create_device_item() to
                create properly formatted device sequence items.
                
        Returns:
            DeviceModule: New module instance with required data elements set
        """
        instance = cls()
        instance._dataset.DeviceSequence = device_sequence
        return instance
    
    
    def with_optional_elements(self) -> 'DeviceModule':
        """No optional elements at module level - all device details are specified in sequence items.
        
        Use create_device_item() to create device sequence items with optional attributes
        like manufacturer, device dimensions, serial numbers, etc.
        
        Returns:
            DeviceModule: Self for method chaining
        """
        return self
    
    @staticmethod
    def create_device_item(
        code_value: str,
        coding_scheme_designator: str,
        code_meaning: str,
        manufacturer: str | None = None,
        manufacturers_model_name: str | None = None,
        device_serial_number: str | None = None,
        device_id: str | None = None,
        device_length: float | None = None,
        device_diameter: float | None = None,
        device_diameter_units: str | DeviceDiameterUnits | None = None,
        device_volume: float | None = None,
        inter_marker_distance: float | None = None,
        device_description: str | None = None,
        date_of_manufacture: str | datetime | date | None = None
    ) -> Dataset:
        """Create device sequence item with Code Sequence Macro and optional device details.
        
        This is the primary method for creating device items. All device details should
        be specified here rather than added later.

        Args:
            code_value (str): Code value for device type (0008,0100)
            coding_scheme_designator (str): Coding scheme designator (0008,0102) 
            code_meaning (str): Code meaning (0008,0104)
            manufacturer (str | None): Manufacturer of the device (0008,0070) Type 3
            manufacturers_model_name (str | None): Manufacturer's model name (0008,1090) Type 3
            device_serial_number (str | None): Device serial number (0018,1000) Type 3
            device_id (str | None): User-supplied identifier (0018,1003) Type 3
            device_length (float | None): Length in mm (0050,0014) Type 3
            device_diameter (float | None): Unit diameter (0050,0016) Type 3
            device_diameter_units (str | DeviceDiameterUnits | None): Units for diameter (0050,0017) Type 2C
            device_volume (float | None): Volume in ml (0050,0018) Type 3
            inter_marker_distance (float | None): Distance between markers in mm (0050,0019) Type 3
            device_description (str | None): Free form description (0050,0020) Type 3
            date_of_manufacture (str | datetime | date | None): Date of manufacture (0018,1204) Type 3

        Returns:
            Dataset: Device sequence item
            
        Note:
            Device Diameter Units (0050,0017) is Type 2C - required if Device Diameter 
            (0050,0016) is present. Valid units: FR (French), GA (Gauge), IN (Inch), MM (Millimeter)
        """
        # Validate conditional requirement for diameter units
        if device_diameter is not None and device_diameter_units is None:
            raise ValueError(
                "Device Diameter Units (0050,0017) is required when Device Diameter (0050,0016) is present. "
                "Valid units: FR, GA, IN, MM"
            )

        item = Dataset()
        
        # Code Sequence Macro attributes (required)
        item.CodeValue = code_value
        item.CodingSchemeDesignator = coding_scheme_designator
        item.CodeMeaning = code_meaning

        # Optional device attributes (Type 3)
        if manufacturer is not None:
            item.Manufacturer = manufacturer
        if manufacturers_model_name is not None:
            item.ManufacturerModelName = manufacturers_model_name
        if device_serial_number is not None:
            item.DeviceSerialNumber = device_serial_number
        if device_id is not None:
            item.DeviceID = device_id
        if device_length is not None:
            item.DeviceLength = str(device_length)
        if device_diameter is not None:
            item.DeviceDiameter = str(device_diameter)
            # Device Diameter Units is Type 2C (required when diameter present)
            item.DeviceDiameterUnits = format_enum_value(device_diameter_units)
        if device_volume is not None:
            item.DeviceVolume = str(device_volume)
        if inter_marker_distance is not None:
            item.InterMarkerDistance = str(inter_marker_distance)
        if device_description is not None:
            item.DeviceDescription = device_description
        if date_of_manufacture is not None:
            # Format date to DICOM DA format (YYYYMMDD)
            item.DateOfManufacture = format_date_value(date_of_manufacture)

        return item
    
    @property
    def device_count(self) -> int:
        """Get the number of devices in the sequence.
        
        Returns:
            int: Number of device items
        """
        return len(getattr(self._dataset, 'DeviceSequence', []))
    
    @property
    def has_device_measurements(self) -> bool:
        """Check if any device has measurement data.
        
        Returns:
            bool: True if any device has length, diameter, or volume data
        """
        if not hasattr(self._dataset, 'DeviceSequence'):
            return False
        
        for item in self._dataset.DeviceSequence:
            if any(key in item for key in ['DeviceLength', 'DeviceDiameter', 'DeviceVolume', 'InterMarkerDistance']):
                return True
        return False
    
    def validate(self, config: ValidationConfig | None = None) -> ValidationResult:
        """Validate this Device Module instance.
        
        Args:
            config (ValidationConfig | None): Optional validation configuration
            
        Returns:
            ValidationResult with errors and warnings
        """
        return DeviceValidator.validate(self._dataset, config)
