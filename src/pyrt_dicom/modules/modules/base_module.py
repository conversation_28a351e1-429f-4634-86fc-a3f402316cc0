"""
Base Module - Abstract base class for all DICOM modules.

This abstract base class provides the common interface and functionality
that all DICOM modules should implement, using composition with internal
dataset management rather than inheriting from pydicom.Dataset for
cleaner separation of concerns.

IMPORTANT: Modules are NOT intended to be saved! Do NOT include any file
metadata or save methods in module classes, as these are reserved for IOD classes.
"""
from abc import ABC, abstractmethod
import copy
from datetime import datetime, date
from typing import Any
import pydicom
from pyrt_dicom.validators.validation_result import ValidationResult
from pyrt_dicom.validators.modules.base_validator import ValidationConfig


class BaseModule(ABC):
    """Abstract base class for all DICOM modules.
    
    Uses composition with internal dataset management rather than
    inheriting from pydicom.Dataset for cleaner separation of concerns.
    
    Key Design Principles:
    - Clean IntelliSense: Only methods and properties visible, no raw DICOM attributes
    - DICOM Type Clarity: Type 1 (args), Type 2 (kwargs=""), Type 3 (kwargs=None)
    - Method-Based API: All data setting through explicit named parameter methods
    - Fluent Interface: Methods return self for chaining
    - Validation Integration: Built-in validation with structured error/warning reporting
    
    Note: Modules are NOT intended to be saved directly. File operations and
    metadata are handled by IOD classes that compose modules.
    """
    
    def __init__(self):
        """Initialize module with internal dataset."""
        self._dataset = pydicom.Dataset()
    
    @classmethod
    @abstractmethod
    def from_required_elements(cls, *args, **kwargs) -> 'BaseModule':
        """Create module instance from all required (Type 1 and Type 2) data elements.
        
        This method must be implemented by each module subclass to define their
        specific required elements according to the DICOM standard.
        
        Pattern:
        - Type 1 elements as positional args (user MUST provide value)
        - Type 2 elements as kwargs with empty string defaults (required but can be empty)
        
        Returns:
            BaseModule: New module instance with required data elements set
        """
        pass
    
    @abstractmethod
    def with_optional_elements(self, *args, **kwargs) -> 'BaseModule':
        """Add optional (Type 3) data elements to the module instance.
        
        This method must be implemented by each module subclass to define their
        specific optional elements according to the DICOM standard.
        
        Pattern:
        - Type 3 elements as kwargs with None defaults (optional, may be absent)
        - Method returns self for fluent chaining
        
        Args:
            **kwargs: Optional data elements as keyword arguments
            
        Returns:
            BaseModule: Self for method chaining
        """
        pass
    
    def to_dataset(self) -> pydicom.Dataset:
        """Generate DICOM dataset from current module state.

        Returns:
            Fresh DICOM dataset containing all module data
        """
        return copy.deepcopy(self._dataset)
    
    def validate(self, config: ValidationConfig | None = None) -> ValidationResult:
        """Validate this module instance against DICOM standard.
        
        This method should be overridden by subclasses to provide module-specific
        validation logic using their corresponding validator classes.
        
        Args:
            config: Optional validation configuration
            
        Returns:
            ValidationResult with 'errors' and 'warnings' lists
        """
        # Default implementation returns no errors/warnings
        # Subclasses should override this with their specific validator
        return ValidationResult()
    
    
    @property
    def module_name(self) -> str:
        """Get the name of this module class.
        
        Returns:
            str: Module class name
        """
        return self.__class__.__name__
    
    @property
    def has_data(self) -> bool:
        """Check if this module contains any DICOM data elements.
        
        Returns:
            bool: True if module has any data elements
        """
        return len(self._dataset) > 0
    
    def get_element_count(self) -> int:
        """Get the number of DICOM data elements in this module.
        
        Returns:
            int: Number of data elements
        """
        return len(self._dataset)
    
    def __repr__(self) -> str:
        """String representation of the module.
        
        Returns:
            str: Module representation with class name and element count
        """
        return f"{self.module_name}(elements={self.get_element_count()})"

    # Helper methods for DICOM data handling
    def _set_attribute(self, attr_name: str, value: Any) -> None:
        """Set DICOM attribute on internal dataset.

        Args:
            attr_name: DICOM attribute name
            value: Value to set
        """
        setattr(self._dataset, attr_name, value)

    def _set_attribute_if_not_none(self, attr_name: str, value: Any) -> None:
        """Set DICOM attribute only if value is not None.

        Args:
            attr_name: DICOM attribute name
            value: Value to set (only if not None)
        """
        if value is not None:
            setattr(self._dataset, attr_name, value)

    def _validate_conditional_requirement(
        self,
        condition: bool,
        required_values: list[Any],
        error_message: str
    ) -> None:
        """Validate Type 1C/2C conditional requirements.

        Args:
            condition: Whether the condition is met
            required_values: List of values that must be present when condition is true
            error_message: Error message to raise if validation fails

        Raises:
            ValueError: If condition is true but required values are missing
        """
        if condition and not any(value is not None for value in required_values):
            raise ValueError(error_message)

    def _format_date_value(self, date_value: Any) -> str:
        """Format date values to DICOM DA format (YYYYMMDD).

        Args:
            date_value: Date value (str, datetime, or date object)

        Returns:
            str: Date formatted as YYYYMMDD string
        """
        if isinstance(date_value, (datetime, date)):
            return date_value.strftime("%Y%m%d")
        return str(date_value)

    def _format_time_value(self, time_value: Any) -> str:
        """Format time values to DICOM TM format (HHMMSS).

        Args:
            time_value: Time value (str or datetime object)

        Returns:
            str: Time formatted as HHMMSS string
        """
        if isinstance(time_value, datetime):
            if time_value.microsecond:
                return f"{time_value.strftime('%H%M%S')}.{time_value.microsecond:06d}"
            return time_value.strftime("%H%M%S")
        return str(time_value)

    def _format_enum_value(self, enum_value: Any) -> Any:
        """Extract value from enum objects.

        Args:
            enum_value: Enum object or other value

        Returns:
            Any: Value from enum or original value
        """
        return enum_value.value if hasattr(enum_value, 'value') else enum_value
