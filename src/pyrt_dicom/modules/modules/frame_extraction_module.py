"""
Frame Extraction Module - DICOM PS3.3 C.12.3

The Frame Extraction Module describes the Frames extracted if the SOP Instance
was created in response to a Frame-Level retrieve request.
"""
from pydicom import Dataset
from .base_module import BaseModule
from ...validators.modules.frame_extraction_validator import FrameExtractionValidator
from ...validators.modules.base_validator import ValidationConfig
from ...validators import ValidationResult


class FrameExtractionModule(BaseModule):
    """Frame Extraction Module implementation for DICOM PS3.3 C.12.3.
    
    Uses composition with internal dataset management rather than inheriting from
    pydicom.Dataset for cleaner separation of concerns. Describes the Frames extracted 
    if the SOP Instance was created in response to a Frame-Level retrieve request.
    
    The Frame Extraction Module contains a sequence describing how frames were extracted
    from source multi-frame DICOM objects using one of three methods:
    - Simple Frame List: Direct list of frame numbers
    - Calculated Frame List: Triplet-based selection (start, end, increment)
    - Time Range: Time-based frame selection (start_time, end_time)
    
    Usage:
        # Create with Simple Frame List
        frame_extraction = FrameExtractionModule.from_required_elements(
            frame_extraction_sequence=[
                FrameExtractionModule.create_frame_extraction_item(
                    multi_frame_source_sop_instance_uid="*******.*******.9",
                    simple_frame_list=[1, 3, 5, 7]
                )
            ]
        )
        
        # Create with Calculated Frame List (triplets: start, end, increment)
        frame_extraction = FrameExtractionModule.from_required_elements(
            frame_extraction_sequence=[
                FrameExtractionModule.create_frame_extraction_item(
                    multi_frame_source_sop_instance_uid="*******.*******.9",
                    calculated_frame_list=[1, 10, 2]  # frames 1-10 with increment of 2
                )
            ]
        )
        
        # Create with Time Range (DICOM VR=FD format)
        frame_extraction = FrameExtractionModule.from_required_elements(
            frame_extraction_sequence=[
                FrameExtractionModule.create_frame_extraction_item(
                    multi_frame_source_sop_instance_uid="*******.*******.9",
                    time_range=[120000.0, 130000.0]  # start and end times as floating point
                )
            ]
        )
        
        # Generate dataset for IOD integration
        dataset = frame_extraction.to_dataset()
        
        # Validate against DICOM standard
        result = frame_extraction.validate()
    """
    
    @classmethod
    def from_required_elements(
        cls,
        frame_extraction_sequence: list[Dataset]
    ) -> 'FrameExtractionModule':
        """Create module with required elements.
        
        Args:
            frame_extraction_sequence: Frame Extraction Sequence (0008,1164) containing Dataset objects with frame extraction details
            
        Returns:
            FrameExtractionModule: New module instance with required data elements set
        """
        if not frame_extraction_sequence:
            raise ValueError("Frame Extraction Sequence must contain at least one item")
        
        instance = cls()
        instance._dataset.FrameExtractionSequence = frame_extraction_sequence
        return instance
    
    def with_optional_elements(self, **kwargs) -> 'FrameExtractionModule':
        """Add optional (Type 3) data elements to the module instance.
        
        The Frame Extraction Module has no Type 3 elements defined in DICOM PS3.3 C.12.3.
        This method is provided for API consistency but accepts no parameters.
        
        Args:
            **kwargs: No optional elements are supported
            
        Returns:
            FrameExtractionModule: Self for method chaining
            
        Raises:
            ValueError: If any keyword arguments are provided
        """
        if kwargs:
            raise ValueError(f"FrameExtractionModule has no optional elements. Unexpected arguments: {list(kwargs.keys())}")
        return self
    
    @staticmethod
    def create_frame_extraction_item(
        multi_frame_source_sop_instance_uid: str,
        simple_frame_list: list[int] | None = None,
        calculated_frame_list: list[int] | None = None,
        time_range: list[float] | None = None
    ) -> Dataset:
        """Create a Frame Extraction Sequence item.
        
        Exactly one of simple_frame_list, calculated_frame_list, or time_range must be provided.
        
        Args:
            multi_frame_source_sop_instance_uid: SOP Instance from which frames are extracted (0008,1167)
            simple_frame_list: List of frames extracted as simple list (0008,1161) - VM=1-n, VR=UL
            calculated_frame_list: List of frames as triplets [start, end, increment] (0008,1162) - VM=3-3n, VR=UL  
            time_range: Start and end times of extracted frames (0008,1163) - VM=2, VR=FD
            
        Returns:
            Dataset representing a Frame Extraction Sequence item
        """
        # Validate that exactly one frame specification is provided
        frame_specs = [simple_frame_list, calculated_frame_list, time_range]
        provided_specs = [spec for spec in frame_specs if spec is not None]
        
        if len(provided_specs) != 1:
            raise ValueError("Exactly one of simple_frame_list, calculated_frame_list, or time_range must be provided")
        
        # Create Dataset object
        item = Dataset()
        item.MultiFrameSourceSOPInstanceUID = multi_frame_source_sop_instance_uid
        
        if simple_frame_list is not None:
            item.SimpleFrameList = simple_frame_list
        elif calculated_frame_list is not None:
            if len(calculated_frame_list) % 3 != 0:
                raise ValueError("Calculated Frame List must contain triplets (start, end, increment)")
            item.CalculatedFrameList = calculated_frame_list
        elif time_range is not None:
            if len(time_range) != 2:
                raise ValueError("Time Range must contain exactly two values (start, end)")
            item.TimeRange = time_range
        
        return item
    
    def add_frame_extraction_item(
        self,
        multi_frame_source_sop_instance_uid: str,
        simple_frame_list: list[int] | None = None,
        calculated_frame_list: list[int] | None = None,
        time_range: list[float] | None = None
    ) -> 'FrameExtractionModule':
        """Add another Frame Extraction Sequence item.
        
        Args:
            multi_frame_source_sop_instance_uid: SOP Instance from which frames are extracted (0008,1167)
            simple_frame_list: List of frames extracted as simple list (0008,1161) - VM=1-n, VR=UL
            calculated_frame_list: List of frames as triplets [start, end, increment] (0008,1162) - VM=3-3n, VR=UL  
            time_range: Start and end times of extracted frames (0008,1163) - VM=2, VR=FD
            
        Returns:
            FrameExtractionModule: Self for method chaining
        """
        new_item = self.create_frame_extraction_item(
            multi_frame_source_sop_instance_uid,
            simple_frame_list,
            calculated_frame_list,
            time_range
        )
        
        if not hasattr(self._dataset, 'FrameExtractionSequence'):
            self._dataset.FrameExtractionSequence = []
        
        self._dataset.FrameExtractionSequence.append(new_item)
        return self
    
    @property
    def extraction_count(self) -> int:
        """Get the number of frame extraction items.
        
        Returns:
            int: Number of frame extraction sequence items
        """
        if hasattr(self._dataset, 'FrameExtractionSequence'):
            return len(self._dataset.FrameExtractionSequence)
        return 0
    
    @property
    def has_simple_frame_lists(self) -> bool:
        """Check if any items use Simple Frame List.
        
        Returns:
            bool: True if any item contains Simple Frame List
        """
        if not hasattr(self._dataset, 'FrameExtractionSequence'):
            return False
        
        return any(hasattr(item, 'SimpleFrameList') for item in self._dataset.FrameExtractionSequence)
    
    @property
    def has_calculated_frame_lists(self) -> bool:
        """Check if any items use Calculated Frame List.
        
        Returns:
            bool: True if any item contains Calculated Frame List
        """
        if not hasattr(self._dataset, 'FrameExtractionSequence'):
            return False
        
        return any(hasattr(item, 'CalculatedFrameList') for item in self._dataset.FrameExtractionSequence)
    
    @property
    def has_time_ranges(self) -> bool:
        """Check if any items use Time Range.
        
        Returns:
            bool: True if any item contains Time Range
        """
        if not hasattr(self._dataset, 'FrameExtractionSequence'):
            return False
        
        return any(hasattr(item, 'TimeRange') for item in self._dataset.FrameExtractionSequence)
    
    @property
    def is_configured(self) -> bool:
        """Check if module is properly configured.
        
        Returns:
            bool: True if Frame Extraction Sequence is present and not empty
        """
        return hasattr(self._dataset, 'FrameExtractionSequence') and len(self._dataset.FrameExtractionSequence) > 0
    
    def validate(self, config: ValidationConfig | None = None) -> ValidationResult:
        """Validate this Frame Extraction Module instance.
        
        Args:
            config: Optional validation configuration
            
        Returns:
            ValidationResult with 'errors' and 'warnings' lists
        """
        return FrameExtractionValidator.validate(self._dataset, config)
