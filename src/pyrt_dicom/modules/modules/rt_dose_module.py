"""
RT Dose Module - DICOM PS3.3 C.8.8.3

The RT Dose Module is used to convey 2D or 3D radiation dose data generated 
from treatment planning systems or similar devices.
"""
from datetime import datetime, date
from pydicom import Dataset
from .base_module import BaseModule
from ...enums.dose_enums import DoseUnits, DoseType
from ...enums.rt_enums import SpatialTransformOfDose, DoseSummationType, TissueHeterogeneityCorrection
from ...enums.image_enums import PhotometricInterpretation, PixelRepresentation
from ...validators.modules.rt_dose_validator import RTDoseValidator
from ...validators.modules.base_validator import ValidationConfig
from ...validators import ValidationResult


class RTDoseModule(BaseModule):
    """RT Dose Module implementation for DICOM PS3.3 C.8.8.3.
    
    Inherits from pydicom.Dataset to provide native DICOM data handling.
    Used to convey 2D or 3D radiation dose data from treatment planning systems.
    
    Usage:
        # Create with required elements
        dose = RTDoseModule.from_required_elements(
            dose_units=DoseUnits.GY,
            dose_type=DoseType.PHYSICAL,
            dose_summation_type=DoseSummationType.PLAN
        )
        
        # Add pixel data elements if present
        dose.with_pixel_data_elements(
            samples_per_pixel=1,
            photometric_interpretation=PhotometricInterpretation.MONOCHROME2,
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            pixel_representation=PixelRepresentation.UNSIGNED,
            dose_grid_scaling=0.001
        )
        
        # Add optional elements
        dose.with_optional_elements(
            content_date="20240101",
            content_time="120000",
            instance_number="1",
            entity_long_label="Primary Dose Distribution",
            dose_comment="Calculated dose for treatment plan"
        )
        
        # Add referenced RT plan
        dose.with_referenced_rt_plan(
            referenced_sop_class_uid="1.2.840.10008.5.1.4.1.1.481.5",
            referenced_sop_instance_uid="1.2.3.4.5.6.7.8.9"
        )
        
        # Validate
        result = dose.validate()
    """

    @classmethod
    def from_required_elements(
        cls,
        dose_units: str | DoseUnits,
        dose_type: str | DoseType,
        dose_summation_type: str | DoseSummationType
    ) -> 'RTDoseModule':
        """Create RTDoseModule from all required (Type 1) data elements.
        
        Args:
            dose_units (str | DoseUnits): Units used to describe dose (3004,0002) Type 1
            dose_type (str | DoseType): Type of dose (3004,0004) Type 1
            dose_summation_type (str | DoseSummationType): Type of dose summation (3004,000A) Type 1
                
        Returns:
            RTDoseModule: New module instance with required data elements set
        """
        instance = cls()
        instance.DoseUnits = instance._format_enum_value(dose_units)
        instance.DoseType = instance._format_enum_value(dose_type)
        instance.DoseSummationType = instance._format_enum_value(dose_summation_type)
        return instance
    
    def with_pixel_data_elements(
        self,
        samples_per_pixel: int,
        photometric_interpretation: str | PhotometricInterpretation,
        bits_allocated: int,
        bits_stored: int,
        high_bit: int,
        pixel_representation: int | PixelRepresentation,
        dose_grid_scaling: float
    ) -> 'RTDoseModule':
        """Add pixel data elements (Type 1C - required if Pixel Data is present).
        
        Args:
            samples_per_pixel (int): Number of samples (planes) in this image (0028,0002) Type 1C
            photometric_interpretation (str | PhotometricInterpretation): Interpretation of pixel data (0028,0004) Type 1C
            bits_allocated (int): Number of bits allocated for each pixel sample (0028,0100) Type 1C
            bits_stored (int): Number of bits stored for each pixel sample (0028,0101) Type 1C
            high_bit (int): Most significant bit for each pixel sample (0028,0102) Type 1C
            pixel_representation (int | PixelRepresentation): Data representation of pixel samples (0028,0103) Type 1C
            dose_grid_scaling (float): Scaling factor for dose grid data (3004,000E) Type 1C
            
        Returns:
            RTDoseModule: Self for method chaining
        """
        self.SamplesPerPixel = samples_per_pixel
        self.PhotometricInterpretation = self._format_enum_value(photometric_interpretation)
        self.BitsAllocated = bits_allocated
        self.BitsStored = bits_stored
        self.HighBit = high_bit
        self.PixelRepresentation = self._format_enum_value(pixel_representation)
        self.DoseGridScaling = dose_grid_scaling
        return self
    
    def with_optional_elements(
        self,
        content_date: str | datetime | date | None = None,
        content_time: str | datetime | None = None,
        spatial_transform_of_dose: str | SpatialTransformOfDose | None = None,
        instance_number: str | None = None,
        entity_long_label: str | None = None,
        dose_comment: str | None = None,
        normalization_point: list[float] | None = None,
        grid_frame_offset_vector: list[float] | None = None,
        tissue_heterogeneity_correction: str | TissueHeterogeneityCorrection | None = None,
        recommended_isodose_level_sequence: list[Dataset] | None = None,
        derivation_code_sequence: list[Dataset] | None = None,
        referenced_instance_sequence: list[Dataset] | None = None,
        plan_overview_sequence: list[Dataset] | None = None
    ) -> 'RTDoseModule':
        """Add optional (Type 3) elements.
        
        Args:
            content_date (str | datetime | date | None): Date content was created (0008,0023) Type 3
            content_time (str | datetime | None): Time content was created (0008,0033) Type 3
            spatial_transform_of_dose (str | SpatialTransformOfDose | None): Use of transformation (3004,0005) Type 3
            instance_number (str | None): Number identifying this object Instance (0020,0013) Type 3
            entity_long_label (str | None): User-defined label for dose data (3010,0038) Type 3
            dose_comment (str | None): User-defined comments for dose data (3004,0006) Type 3
            normalization_point (list[float] | None): Coordinates of normalization point (3004,0008) Type 3
            grid_frame_offset_vector (list[float] | None): Dose image plane offsets (3004,000C) Type 1C
            tissue_heterogeneity_correction (str | TissueHeterogeneityCorrection | None): Patient heterogeneity characteristics (3004,0014) Type 3
            recommended_isodose_level_sequence (list[Dataset] | None): Recommended isodose levels (3004,0016) Type 3
            derivation_code_sequence (list[Dataset] | None): How dose was derived (0008,9215) Type 3
            referenced_instance_sequence (list[Dataset] | None): SOP Instances used to derive dose (0008,114A) Type 3
            plan_overview_sequence (list[Dataset] | None): Plan overview parameters (300C,0116) Type 1C
            
        Returns:
            RTDoseModule: Self for method chaining
        """
        if content_date is not None:
            self.ContentDate = self._format_date_value(content_date)
        if content_time is not None:
            self.ContentTime = self._format_time_value(content_time)
        if spatial_transform_of_dose is not None:
            self.SpatialTransformOfDose = self._format_enum_value(spatial_transform_of_dose)
        self._set_attribute_if_not_none('InstanceNumber', instance_number)
        self._set_attribute_if_not_none('EntityLongLabel', entity_long_label)
        self._set_attribute_if_not_none('DoseComment', dose_comment)
        self._set_attribute_if_not_none('NormalizationPoint', normalization_point)
        self._set_attribute_if_not_none('GridFrameOffsetVector', grid_frame_offset_vector)
        if tissue_heterogeneity_correction is not None:
            self.TissueHeterogeneityCorrection = self._format_enum_value(tissue_heterogeneity_correction)
        self._set_attribute_if_not_none('RecommendedIsodoseLevelSequence', recommended_isodose_level_sequence)
        self._set_attribute_if_not_none('DerivationCodeSequence', derivation_code_sequence)
        self._set_attribute_if_not_none('ReferencedInstanceSequence', referenced_instance_sequence)
        self._set_attribute_if_not_none('PlanOverviewSequence', plan_overview_sequence)
        return self
    
    def with_referenced_rt_plan(
        self,
        referenced_sop_class_uid: str,
        referenced_sop_instance_uid: str,
        referenced_plan_overview_index: int | None = None,
        referenced_fraction_group_sequence: list[Dataset] | None = None
    ) -> 'RTDoseModule':
        """Add referenced RT plan (Type 1C - required for certain dose summation types).
        
        Args:
            referenced_sop_class_uid (str): Referenced SOP Class UID
            referenced_sop_instance_uid (str): Referenced SOP Instance UID
            referenced_plan_overview_index (int | None): Plan Overview Index (300C,0118) Type 1C
            referenced_fraction_group_sequence (list[Dataset] | None): Referenced Fraction Groups (300C,0020) Type 1C
            
        Returns:
            RTDoseModule: Self for method chaining
        """
        # Validate conditional requirement based on dose summation type
        dose_summation_type = getattr(self, 'DoseSummationType', '')
        requires_rt_plan = dose_summation_type in [
            'PLAN', 'MULTI_PLAN', 'FRACTION', 'BEAM', 'BRACHY',
            'FRACTION_SESSION', 'BEAM_SESSION', 'BRACHY_SESSION', 'CONTROL_POINT'
        ]
        
        if requires_rt_plan:
            rt_plan_item = Dataset()
            rt_plan_item.ReferencedSOPClassUID = referenced_sop_class_uid
            rt_plan_item.ReferencedSOPInstanceUID = referenced_sop_instance_uid
            if referenced_plan_overview_index is not None:
                rt_plan_item.ReferencedPlanOverviewIndex = referenced_plan_overview_index
            if referenced_fraction_group_sequence is not None:
                rt_plan_item.ReferencedFractionGroupSequence = referenced_fraction_group_sequence
            
            self.ReferencedRTPlanSequence = [rt_plan_item]
        
        return self
    
    def with_referenced_spatial_registration(
        self,
        referenced_sop_class_uid: str,
        referenced_sop_instance_uid: str
    ) -> 'RTDoseModule':
        """Add referenced spatial registration (Type 2C - required if spatial transform is RIGID or NON_RIGID).
        
        Args:
            referenced_sop_class_uid (str): Referenced SOP Class UID
            referenced_sop_instance_uid (str): Referenced SOP Instance UID
            
        Returns:
            RTDoseModule: Self for method chaining
        """
        spatial_transform = getattr(self, 'SpatialTransformOfDose', '')
        if spatial_transform in ['RIGID', 'NON_RIGID']:
            registration_item = {
                'ReferencedSOPClassUID': referenced_sop_class_uid,
                'ReferencedSOPInstanceUID': referenced_sop_instance_uid
            }
            self.ReferencedSpatialRegistrationSequence = [registration_item]
        
        return self
    
    @staticmethod
    def create_recommended_isodose_level_item(
        dose_value: float,
        recommended_display_cielab_value: list[float]
    ) -> Dataset:
        """Create recommended isodose level sequence item.

        Args:
            dose_value (float): Value for the isodose (3004,0012)
            recommended_display_cielab_value (list[float]): Default color triplet (0062,000D)

        Returns:
            Dataset: Recommended isodose level sequence item
        """
        item = Dataset()
        item.DoseValue = dose_value
        item.RecommendedDisplayCIELabValue = recommended_display_cielab_value
        return item
    
    @staticmethod
    def create_referenced_fraction_group_item(
        referenced_fraction_group_number: int,
        referenced_beam_sequence: list[Dataset] | None = None,
        referenced_brachy_application_setup_sequence: list[Dataset] | None = None
    ) -> Dataset:
        """Create referenced fraction group sequence item.

        Args:
            referenced_fraction_group_number (int): Fraction Group Number (300C,0022)
            referenced_beam_sequence (list[Dataset] | None): Referenced Beams (300C,0004)
            referenced_brachy_application_setup_sequence (list[Dataset] | None): Referenced Brachy Setups (300C,000A)

        Returns:
            Dataset: Referenced fraction group sequence item
        """
        item = Dataset()
        item.ReferencedFractionGroupNumber = referenced_fraction_group_number
        if referenced_beam_sequence is not None:
            item.ReferencedBeamSequence = referenced_beam_sequence
        if referenced_brachy_application_setup_sequence is not None:
            item.ReferencedBrachyApplicationSetupSequence = referenced_brachy_application_setup_sequence
        return item
    
    @property
    def has_pixel_data(self) -> bool:
        """Check if pixel data elements are present.
        
        Returns:
            bool: True if pixel data related elements are present
        """
        return (hasattr(self, 'SamplesPerPixel') and 
                hasattr(self, 'PhotometricInterpretation') and
                hasattr(self, 'BitsAllocated'))
    
    @property
    def has_spatial_transform(self) -> bool:
        """Check if spatial transformation is used.
        
        Returns:
            bool: True if spatial transform is not NONE
        """
        spatial_transform = getattr(self, 'SpatialTransformOfDose', 'NONE')
        return spatial_transform != 'NONE'
    
    @property
    def requires_rt_plan_reference(self) -> bool:
        """Check if RT plan reference is required based on dose summation type.
        
        Returns:
            bool: True if dose summation type requires RT plan reference
        """
        dose_summation_type = getattr(self, 'DoseSummationType', '')
        return dose_summation_type in [
            'PLAN', 'MULTI_PLAN', 'FRACTION', 'BEAM', 'BRACHY',
            'FRACTION_SESSION', 'BEAM_SESSION', 'BRACHY_SESSION', 'CONTROL_POINT'
        ]
    
    def validate(self, config: ValidationConfig | None = None) -> ValidationResult:
        """Validate this RT Dose Module instance.
        
        Args:
            config (ValidationConfig | None): Optional validation configuration
            
        Returns:
            ValidationResult with 'errors' and 'warnings' lists
        """
        return RTDoseValidator.validate(self, config)
