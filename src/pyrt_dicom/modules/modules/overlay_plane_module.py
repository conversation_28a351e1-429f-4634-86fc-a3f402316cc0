"""
Overlay Plane Module Implementation - DICOM PS3.3 C.9.2

This module implements the DICOM Overlay Plane Module as specified in PS3.3 Section C.9.2.
The Overlay Plane Module describes characteristics of graphics or bit-mapped text overlays
that are spatially associated with medical images for annotation, measurement, or region
of interest (ROI) definition purposes.

Overlay planes provide a mechanism to display graphical annotations, text labels, or 
ROI boundaries over medical images without modifying the underlying pixel data. This is
essential for clinical workflows involving image annotation, measurement marking, or
treatment planning visualization.

Common use cases include:
- ROI boundary marking for radiation therapy planning
- Anatomical structure annotations in diagnostic imaging  
- Measurement tool overlays for distance/area calculations
- Text labels for image identification and orientation
- Graphics overlays for surgical planning visualization

The module supports both Graphics (G) and ROI (R) overlay types, with optional statistical
parameters for ROI overlays including area, mean, and standard deviation calculations.
Multiple overlay planes can be defined using different group numbers (6000-601E).
"""
from .base_module import BaseModule
from ...enums.image_enums import OverlayType, OverlaySubtype
from ...validators.modules.overlay_plane_validator import OverlayPlaneValidator
from ...validators.modules.base_validator import ValidationConfig
from ...validators import ValidationResult
from pydicom.tag import Tag


class OverlayPlaneModule(BaseModule):
    """DICOM Overlay Plane Module implementation conforming to PS3.3 Section C.9.2.
    
    This class provides a user-friendly interface for creating and managing DICOM overlay
    planes, which are essential for displaying graphics, text annotations, and region of
    interest (ROI) boundaries over medical images without altering the underlying pixel data.
    
    Overlay planes are commonly used in:
    - Radiation therapy planning: ROI boundary definition and dose distribution visualization
    - Diagnostic imaging: Anatomical structure annotation and measurement overlays
    - Surgical planning: Critical structure marking and approach visualization
    - Image analysis: Automated segmentation result display and manual annotation
    
    The module supports both Graphics (G) and ROI (R) overlay types:
    - Graphics overlays: General purpose graphics, text, and annotation display
    - ROI overlays: Specific region definitions with optional statistical calculations
    
    Technical Implementation Notes:
    - Uses DICOM group 6000 by default (configurable to any even group 6000-601E)
    - Supports 1-bit overlay data with automatic size calculation and validation
    - Provides spatial coordinate validation against associated image dimensions
    - Implements proper DICOM tag assignment for repeating overlay groups
    
    DICOM Compliance:
    This implementation follows DICOM PS3.3 C.9.2 specifications exactly, ensuring
    compatibility with all DICOM-compliant viewers and radiation therapy systems.
    
    Args:
        None (use factory methods for creation)
    
    Example:
        Basic overlay creation for ROI marking:
        
        >>> # Create ROI overlay for tumor boundary
        >>> overlay = OverlayPlaneModule.from_required_elements(
        ...     overlay_rows=512,
        ...     overlay_columns=512, 
        ...     overlay_type=OverlayType.ROI,
        ...     overlay_origin=[100, 100],  # Start at image position (100,100)
        ...     overlay_data=roi_binary_data  # 1-bit ROI mask data
        ... )
        
        >>> # Add descriptive information
        >>> overlay.with_optional_elements(
        ...     overlay_description="Primary tumor boundary for treatment planning",
        ...     overlay_subtype=OverlaySubtype.USER,
        ...     overlay_label="GTV"  # Gross Tumor Volume
        ... )
        
        >>> # Add ROI statistics for treatment planning
        >>> overlay.with_roi_statistics(
        ...     roi_area=2048,  # ROI area in pixels
        ...     roi_mean=2850.5,  # Mean HU value within ROI
        ...     roi_standard_deviation=125.3  # HU standard deviation
        ... )
        
        >>> # Validate DICOM compliance
        >>> result = overlay.validate()
        >>> if result.has_errors:
        ...     print("Validation errors:", result.errors)
        
        Graphics overlay for measurement display:
        
        >>> # Create graphics overlay for distance measurements
        >>> measurement_overlay = OverlayPlaneModule.from_required_elements(
        ...     overlay_rows=512,
        ...     overlay_columns=512,
        ...     overlay_type=OverlayType.GRAPHICS,
        ...     overlay_origin=[1, 1],  # Cover entire image
        ...     overlay_data=measurement_graphics_data
        ... )
        
        >>> # Add measurement description
        >>> measurement_overlay.with_optional_elements(
        ...     overlay_description="Linear measurements for dose verification",
        ...     overlay_label="Measurements"
        ... )
        
        Multiple overlay groups for complex annotations:
        
        >>> # Create second overlay using different group
        >>> annotation_overlay = OverlayPlaneModule.from_required_elements(
        ...     overlay_rows=512,
        ...     overlay_columns=512,
        ...     overlay_type=OverlayType.GRAPHICS,
        ...     overlay_origin=[1, 1],
        ...     overlay_data=annotation_data,
        ...     overlay_group=0x6002  # Use group 6002 instead of default 6000
        ... )
    
    See Also:
        - OverlayType enum: Defines valid overlay type values (GRAPHICS, ROI)
        - OverlaySubtype enum: Defines overlay purpose classifications
        - OverlayPlaneValidator: Validates overlay data for DICOM compliance
        - DICOM PS3.3 Section C.9.2: Complete specification reference
    """

    @classmethod
    def from_required_elements(
        cls,
        overlay_rows: int,
        overlay_columns: int,
        overlay_type: str | OverlayType,
        overlay_origin: list[int],
        overlay_data: bytes,
        overlay_group: int = 0x6000
    ) -> 'OverlayPlaneModule':
        """Create OverlayPlaneModule with all required DICOM elements for overlay display.
        
        Creates a complete overlay plane module with all Type 1 (required) DICOM attributes.
        The overlay data represents a 1-bit binary mask that defines overlay graphics or ROI
        boundaries to be displayed over the associated medical image.
        
        Args:
            overlay_rows (int): Number of pixel rows in the overlay plane (60xx,0010) Type 1.
                Must match or be smaller than the associated image height.
                
            overlay_columns (int): Number of pixel columns in the overlay plane (60xx,0011) Type 1.
                Must match or be smaller than the associated image width.
                
            overlay_type (str | OverlayType): Defines overlay purpose and interpretation (60xx,0040) Type 1.
                OverlayType.GRAPHICS ("G") for general graphics, OverlayType.ROI ("R") for regions.
                
            overlay_origin (list[int]): Starting position [row, column] of overlay (60xx,0050) Type 1.
                Uses 1-based DICOM coordinates relative to the image pixel matrix.
                
            overlay_data (bytes): Binary overlay pixel data (60xx,3000) Type 1.
                1-bit per pixel data arranged left-to-right, top-to-bottom.
                
            overlay_group (int, optional): DICOM overlay group number (default: 0x6000).
                Must be even number between 0x6000 and 0x601E for multiple overlay planes.
            
        Returns:
            OverlayPlaneModule: Configured module ready for optional elements and validation.
                
        Raises:
            ValueError: If overlay_group is not an even number between 0x6000 and 0x601E.
        """
        instance = cls()
        
        # Validate overlay group
        if overlay_group < 0x6000 or overlay_group > 0x601E or overlay_group % 2 != 0:
            raise ValueError("Overlay group must be even number between 0x6000 and 0x601E")
        
        # Set attributes using the specified group and proper tag format
        # Overlay elements use group-specific tags: (60xx,yy)
        
        # Create group-specific tags
        rows_tag = Tag(overlay_group, 0x0010)
        cols_tag = Tag(overlay_group, 0x0011) 
        type_tag = Tag(overlay_group, 0x0040)
        origin_tag = Tag(overlay_group, 0x0050)
        bits_alloc_tag = Tag(overlay_group, 0x0100)
        bit_pos_tag = Tag(overlay_group, 0x0102)
        data_tag = Tag(overlay_group, 0x3000)
        
        # Set data elements using add_new for repeating groups
        instance.add_new(rows_tag, 'US', overlay_rows)
        instance.add_new(cols_tag, 'US', overlay_columns)
        instance.add_new(type_tag, 'CS', instance._format_enum_value(overlay_type))
        instance.add_new(origin_tag, 'SS', overlay_origin)
        instance.add_new(bits_alloc_tag, 'US', 1)  # Always 1 for overlays
        instance.add_new(bit_pos_tag, 'US', 0)     # Always 0 for overlays
        instance.add_new(data_tag, 'OW', overlay_data)
        
        # Store group for reference
        instance._overlay_group = overlay_group
        
        return instance
    
    def with_optional_elements(
        self,
        overlay_description: str | None = None,
        overlay_subtype: str | OverlaySubtype | None = None,
        overlay_label: str | None = None
    ) -> 'OverlayPlaneModule':
        """Add optional descriptive elements to enhance overlay identification and purpose.
        
        These Type 3 elements provide human-readable information about the overlay's content
        and purpose, improving clinical workflow by allowing users to quickly identify and
        understand different overlays within the same image dataset.
        
        Args:
            overlay_description (str | None): Detailed description of overlay content (60xx,0022) Type 3.
                Free-text field for comprehensive overlay documentation.
                
            overlay_subtype (str | OverlaySubtype | None): Standardized overlay purpose (60xx,0045) Type 3.
                OverlaySubtype.USER for user-defined annotations, OverlaySubtype.AUTOMATED 
                for computer-generated overlays.
                
            overlay_label (str | None): Short identifying text label (60xx,1500) Type 3.
                Concise label for quick overlay identification in viewer interfaces.
            
        Returns:
            OverlayPlaneModule: Self-reference enabling method chaining for fluent interface.
        """
        from pydicom.tag import Tag
        
        overlay_group = getattr(self, '_overlay_group', 0x6000)
        
        if overlay_description is not None:
            desc_tag = Tag(overlay_group, 0x0022)
            self.add_new(desc_tag, 'LO', overlay_description)
            
        if overlay_subtype is not None:
            subtype_tag = Tag(overlay_group, 0x0045)
            self.add_new(subtype_tag, 'LO', self._format_enum_value(overlay_subtype))
            
        if overlay_label is not None:
            label_tag = Tag(overlay_group, 0x1500)
            self.add_new(label_tag, 'LO', overlay_label)
        return self
    
    def with_roi_statistics(
        self,
        roi_area: int | None = None,
        roi_mean: float | None = None,
        roi_standard_deviation: float | None = None
    ) -> 'OverlayPlaneModule':
        """Add statistical analysis parameters for ROI overlays used in quantitative imaging.
        
        These Type 3 elements provide quantitative measurements within the region of interest
        defined by the overlay, essential for treatment planning dose calculations, diagnostic
        analysis, and research applications.
        
        Args:
            roi_area (int | None): Total number of pixels within ROI boundary (60xx,1301) Type 3.
                Represents the spatial extent of the region in pixels.
                
            roi_mean (float | None): Average pixel value within ROI region (60xx,1302) Type 3.
                For CT images, represents mean Hounsfield Unit (HU) value indicating tissue density.
                
            roi_standard_deviation (float | None): Pixel value standard deviation (60xx,1303) Type 3.
                Measures intensity variation within the ROI, indicating tissue homogeneity.
            
        Returns:
            OverlayPlaneModule: Self-reference enabling method chaining for fluent interface.
            
        Note:
            ROI statistics are only meaningful when overlay_type is OverlayType.ROI.
            Graphics overlays typically do not require statistical analysis.
        """
        from pydicom.tag import Tag
        
        overlay_group = getattr(self, '_overlay_group', 0x6000)
        
        if roi_area is not None:
            area_tag = Tag(overlay_group, 0x1301)
            self.add_new(area_tag, 'IS', str(roi_area))
            
        if roi_mean is not None:
            mean_tag = Tag(overlay_group, 0x1302)
            self.add_new(mean_tag, 'DS', str(roi_mean))
            
        if roi_standard_deviation is not None:
            std_tag = Tag(overlay_group, 0x1303)
            self.add_new(std_tag, 'DS', str(roi_standard_deviation))
        return self
    
    @staticmethod
    def calculate_overlay_data_size(rows: int, columns: int) -> int:
        """Calculate required overlay data size for 1-bit DICOM overlay pixel data.
        
        Computes the exact byte size needed for overlay data storage according to DICOM PS3.3
        specifications. Accounts for 1-bit per pixel encoding, byte boundary alignment, and
        DICOM even-length requirement.
        
        Args:
            rows (int): Number of pixel rows in the overlay plane.
                Must be positive integer representing vertical overlay extent.
                
            columns (int): Number of pixel columns in the overlay plane.
                Must be positive integer representing horizontal overlay extent.
            
        Returns:
            int: Required data size in bytes with proper DICOM padding.
        """
        total_bits = rows * columns
        total_bytes = (total_bits + 7) // 8  # Round up to nearest byte
        # DICOM requires even length
        return total_bytes + (total_bytes % 2)
    
    @property
    def is_graphics_overlay(self) -> bool:
        """Check if this is a graphics overlay.
        
        Returns:
            bool: True if overlay type is GRAPHICS
        """
        from pydicom.tag import Tag
        overlay_group = getattr(self, '_overlay_group', 0x6000)
        type_tag = Tag(overlay_group, 0x0040)
        return type_tag in self and self[type_tag].value == "G"
    
    @property
    def is_roi_overlay(self) -> bool:
        """Check if this is an ROI overlay.
        
        Returns:
            bool: True if overlay type is ROI
        """
        from pydicom.tag import Tag
        overlay_group = getattr(self, '_overlay_group', 0x6000)
        type_tag = Tag(overlay_group, 0x0040)
        return type_tag in self and self[type_tag].value == "R"
    
    @property
    def has_roi_statistics(self) -> bool:
        """Check if ROI statistics are present.
        
        Returns:
            bool: True if any ROI statistical elements are present
        """
        from pydicom.tag import Tag
        overlay_group = getattr(self, '_overlay_group', 0x6000)
        roi_tags = [
            Tag(overlay_group, 0x1301),  # ROIArea
            Tag(overlay_group, 0x1302),  # ROIMean
            Tag(overlay_group, 0x1303)   # ROIStandardDeviation
        ]
        return any(tag in self for tag in roi_tags)
    
    @property
    def overlay_pixel_count(self) -> int | None:
        """Calculate total number of overlay pixels.
        
        Returns:
            int | None: Total pixel count (rows × columns), or None if not determinable
        """
        from pydicom.tag import Tag
        overlay_group = getattr(self, '_overlay_group', 0x6000)
        rows_tag = Tag(overlay_group, 0x0010)
        cols_tag = Tag(overlay_group, 0x0011)
        
        if rows_tag not in self or cols_tag not in self:
            return None
        return self[rows_tag].value * self[cols_tag].value
    
    @property
    def expected_data_size(self) -> int | None:
        """Calculate expected overlay data size.
        
        Returns:
            int | None: Expected data size in bytes, or None if not determinable
        """
        from pydicom.tag import Tag
        overlay_group = getattr(self, '_overlay_group', 0x6000)
        rows_tag = Tag(overlay_group, 0x0010)
        cols_tag = Tag(overlay_group, 0x0011)
        
        if rows_tag not in self or cols_tag not in self:
            return None
        return self.calculate_overlay_data_size(self[rows_tag].value, self[cols_tag].value)
    
    @property
    def actual_data_size(self) -> int | None:
        """Get actual overlay data size.
        
        Returns:
            int | None: Actual data size in bytes, or None if not available
        """
        from pydicom.tag import Tag
        overlay_group = getattr(self, '_overlay_group', 0x6000)
        data_tag = Tag(overlay_group, 0x3000)
        
        if data_tag not in self:
            return None
        return len(self[data_tag].value)
    
    @property
    def overlay_group(self) -> int:
        """Get the overlay group number.
        
        Returns:
            int: Overlay group number (0x6000-0x601E)
        """
        return getattr(self, '_overlay_group', 0x6000)
    
    def get_overlay_origin_coordinates(self) -> tuple[int, int] | None:
        """Get overlay origin as (row, column) tuple.
        
        Returns:
            tuple[int, int] | None: (row, column) coordinates or None if not available
        """
        from pydicom.tag import Tag
        overlay_group = getattr(self, '_overlay_group', 0x6000)
        origin_tag = Tag(overlay_group, 0x0050)
        
        if origin_tag not in self:
            return None
        
        origin = self[origin_tag].value
        if len(origin) != 2:
            return None
        return (origin[0], origin[1])
    
    def is_overlay_within_image(self, image_rows: int, image_columns: int) -> bool:
        """Check if overlay fits within image boundaries.
        
        Args:
            image_rows (int): Number of rows in associated image
            image_columns (int): Number of columns in associated image
            
        Returns:
            bool: True if overlay fits within image boundaries
        """
        from pydicom.tag import Tag
        overlay_group = getattr(self, '_overlay_group', 0x6000)
        rows_tag = Tag(overlay_group, 0x0010)
        cols_tag = Tag(overlay_group, 0x0011)
        
        origin = self.get_overlay_origin_coordinates()
        if origin is None or rows_tag not in self or cols_tag not in self:
            return False
        
        origin_row, origin_col = origin
        overlay_rows = self[rows_tag].value
        overlay_cols = self[cols_tag].value
        
        # Calculate overlay boundaries
        overlay_end_row = origin_row + overlay_rows - 1
        overlay_end_col = origin_col + overlay_cols - 1
        
        # Check if overlay extends beyond image
        return (origin_row >= 1 and origin_col >= 1 and
                overlay_end_row <= image_rows and overlay_end_col <= image_columns)
    
    # Compatibility properties for tests and easier access
    @property
    def OverlayRows(self) -> int:
        """Get overlay rows.
        
        Returns:
            int: Number of rows in overlay plane
            
        Raises:
            AttributeError: If OverlayRows not set
        """
        from pydicom.tag import Tag
        overlay_group = getattr(self, '_overlay_group', 0x6000)
        rows_tag = Tag(overlay_group, 0x0010)
        if rows_tag in self:
            return self[rows_tag].value
        raise AttributeError("OverlayRows not set")
    
    @property 
    def OverlayColumns(self) -> int:
        """Get overlay columns.
        
        Returns:
            int: Number of columns in overlay plane
            
        Raises:
            AttributeError: If OverlayColumns not set
        """
        from pydicom.tag import Tag
        overlay_group = getattr(self, '_overlay_group', 0x6000)
        cols_tag = Tag(overlay_group, 0x0011)
        if cols_tag in self:
            return self[cols_tag].value
        raise AttributeError("OverlayColumns not set")
    
    @property
    def OverlayType(self) -> str:
        """Get overlay type.
        
        Returns:
            str: Overlay type ("G" for graphics, "R" for ROI)
            
        Raises:
            AttributeError: If OverlayType not set
        """
        from pydicom.tag import Tag
        overlay_group = getattr(self, '_overlay_group', 0x6000)
        type_tag = Tag(overlay_group, 0x0040)
        if type_tag in self:
            return self[type_tag].value
        raise AttributeError("OverlayType not set")
    
    @property
    def OverlayOrigin(self) -> list:
        """Get overlay origin.
        
        Returns:
            list: Origin coordinates [row, column] as 1-based DICOM coordinates
            
        Raises:
            AttributeError: If OverlayOrigin not set
        """
        from pydicom.tag import Tag
        overlay_group = getattr(self, '_overlay_group', 0x6000)
        origin_tag = Tag(overlay_group, 0x0050)
        if origin_tag in self:
            return self[origin_tag].value
        raise AttributeError("OverlayOrigin not set")
    
    @property
    def OverlayBitsAllocated(self) -> int:
        """Get overlay bits allocated.
        
        Returns:
            int: Number of bits allocated per overlay pixel (always 1)
            
        Raises:
            AttributeError: If OverlayBitsAllocated not set
        """
        from pydicom.tag import Tag
        overlay_group = getattr(self, '_overlay_group', 0x6000)
        bits_tag = Tag(overlay_group, 0x0100)
        if bits_tag in self:
            return self[bits_tag].value
        raise AttributeError("OverlayBitsAllocated not set")
    
    @property
    def OverlayBitPosition(self) -> int:
        """Get overlay bit position.
        
        Returns:
            int: Bit position within allocated bits (always 0)
            
        Raises:
            AttributeError: If OverlayBitPosition not set
        """
        from pydicom.tag import Tag
        overlay_group = getattr(self, '_overlay_group', 0x6000)
        pos_tag = Tag(overlay_group, 0x0102)
        if pos_tag in self:
            return self[pos_tag].value
        raise AttributeError("OverlayBitPosition not set")
    
    @property
    def OverlayData(self) -> bytes:
        """Get overlay data.
        
        Returns:
            bytes: Binary overlay pixel data (1-bit per pixel)
            
        Raises:
            AttributeError: If OverlayData not set
        """
        from pydicom.tag import Tag
        overlay_group = getattr(self, '_overlay_group', 0x6000)
        data_tag = Tag(overlay_group, 0x3000)
        if data_tag in self:
            return self[data_tag].value
        raise AttributeError("OverlayData not set")
    
    @property
    def OverlayDescription(self) -> str:
        """Get overlay description.
        
        Returns:
            str: Detailed description of overlay content
            
        Raises:
            AttributeError: If OverlayDescription not set
        """
        from pydicom.tag import Tag
        overlay_group = getattr(self, '_overlay_group', 0x6000)
        desc_tag = Tag(overlay_group, 0x0022)
        if desc_tag in self:
            return self[desc_tag].value
        raise AttributeError("OverlayDescription not set")
    
    @property
    def OverlayLabel(self) -> str:
        """Get overlay label.
        
        Returns:
            str: Short identifying text label
            
        Raises:
            AttributeError: If OverlayLabel not set
        """
        from pydicom.tag import Tag
        overlay_group = getattr(self, '_overlay_group', 0x6000)
        label_tag = Tag(overlay_group, 0x1500)
        if label_tag in self:
            return self[label_tag].value
        raise AttributeError("OverlayLabel not set")
    
    @property
    def OverlaySubtype(self) -> str:
        """Get overlay subtype.
        
        Returns:
            str: Standardized overlay purpose classification
            
        Raises:
            AttributeError: If OverlaySubtype not set
        """
        from pydicom.tag import Tag
        overlay_group = getattr(self, '_overlay_group', 0x6000)
        subtype_tag = Tag(overlay_group, 0x0045)
        if subtype_tag in self:
            return self[subtype_tag].value
        raise AttributeError("OverlaySubtype not set")
    
    @property
    def ROIArea(self) -> int:
        """Get ROI area.
        
        Returns:
            int: Number of pixels within ROI boundary
            
        Raises:
            AttributeError: If ROIArea not set
        """
        from pydicom.tag import Tag
        overlay_group = getattr(self, '_overlay_group', 0x6000)
        area_tag = Tag(overlay_group, 0x1301)
        if area_tag in self:
            return int(self[area_tag].value)
        raise AttributeError("ROIArea not set")

    def validate(self, config: ValidationConfig | None = None) -> ValidationResult:
        """Validate this Overlay Plane Module instance.
        
        Args:
            config: Optional validation configuration
            
        Returns:
            ValidationResult: ValidationResult with 'errors' and 'warnings' lists
        """
        return OverlayPlaneValidator.validate(self, config)
