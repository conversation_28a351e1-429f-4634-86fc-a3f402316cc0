"""
CT Image Module - DICOM PS3.3 C.8.2.1

The CT Image Module contains attributes that describe CT images including
pixel characteristics, acquisition parameters, and reconstruction details.
"""
from pydicom import Dataset
from .base_module import BaseModule
from ...enums.contrast_ct_enums import (
    MultiEnergyCTAcquisition, RotationDirection, ExposureModulationType,
    CTImageTypeValue3, CTImageTypeValue4, CTSamplesPerPixel, CTBitsAllocated,
    CTBitsStored, RescaleType, FilterMaterial, ScanOptions, FilterType
)
from ...enums.image_enums import PhotometricInterpretation, ImageType
from ...validators.modules.ct_image_validator import CTImageValidator
from ...validators.modules.base_validator import ValidationConfig
from ...validators import ValidationResult
from ...utils.dicom_formatters import format_enum_value


class CTImageModule(BaseModule):
    """CT Image Module implementation for DICOM PS3.3 C.8.2.1.

    Uses composition with internal dataset management for cleaner separation
    of concerns. Contains attributes that describe CT images including pixel
    characteristics, acquisition parameters, and reconstruction details.

    Usage:
        # Create with required elements
        ct_image = CTImageModule.from_required_elements(
            image_type=["ORIGINAL", "PRIMARY", "AXIAL"],
            samples_per_pixel=1,
            photometric_interpretation="MONOCHROME2",
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            rescale_intercept=-1024.0,
            rescale_slope=1.0,
            kvp=120.0,
            acquisition_number=1
        )

        # Add optional elements
        ct_image.with_optional_elements(
            multi_energy_ct_acquisition=MultiEnergyCTAcquisition.NO,
            scan_options=ScanOptions.HELICAL_CT,
            data_collection_diameter=500.0,
            convolution_kernel="STANDARD"
        )

        # Add conditional elements
        ct_image.with_rescale_type_conditional(
            rescale_type=RescaleType.HU
        )

        # Add water equivalent diameter if needed
        ct_image.with_water_equivalent_diameter_conditional(
            water_equivalent_diameter_calculation_method_code_sequence=[
                CTImageModule.create_water_equivalent_diameter_calculation_method_item(
                    code_value="113835", code_meaning="Water equivalent diameter"
                )
            ]
        )

        # Generate dataset for IOD integration
        dataset = ct_image.to_dataset()

        # Validate
        result = ct_image.validate()
    """

    @classmethod
    def from_required_elements(
        cls,
        # Type 1 elements as positional args (user MUST provide value)
        image_type: list[str | ImageType | CTImageTypeValue3 | CTImageTypeValue4],
        samples_per_pixel: int | CTSamplesPerPixel,
        photometric_interpretation: str | PhotometricInterpretation,
        bits_allocated: int | CTBitsAllocated,
        bits_stored: int | CTBitsStored,
        high_bit: int,
        rescale_intercept: float,
        rescale_slope: float,
        # Type 2 elements as kwargs with empty string defaults (required but can be empty)
        kvp: float | str = "",
        acquisition_number: int | str = ""
    ) -> 'CTImageModule':
        """Create CT Image Module with all required (Type 1 and Type 2) elements.

        Args:
            image_type: Image identification characteristics (0008,0008) Type 1
            samples_per_pixel: Number of samples (planes) in this image (0028,0002) Type 1
            photometric_interpretation: Intended interpretation of pixel data (0028,0004) Type 1
            bits_allocated: Number of bits allocated for each pixel sample (0028,0100) Type 1
            bits_stored: Number of bits stored for each pixel sample (0028,0101) Type 1
            high_bit: Most significant bit for pixel sample data (0028,0102) Type 1
            rescale_intercept: Value b in relationship between stored values and output units (0028,1052) Type 1
            rescale_slope: Value m in the equation specified in Rescale Intercept (0028,1053) Type 1
            kvp: Peak kilo voltage output of the X-Ray generator used (0018,0060) Type 2
            acquisition_number: Number identifying the single continuous gathering of data (0020,0012) Type 2
        """
        instance = cls()

        # Set Type 1 elements (format enums if needed)
        instance._dataset.ImageType = [format_enum_value(item) for item in image_type]
        instance._dataset.SamplesPerPixel = format_enum_value(samples_per_pixel)
        instance._dataset.PhotometricInterpretation = format_enum_value(photometric_interpretation)
        instance._dataset.BitsAllocated = format_enum_value(bits_allocated)
        instance._dataset.BitsStored = format_enum_value(bits_stored)
        instance._dataset.HighBit = high_bit
        instance._dataset.RescaleIntercept = rescale_intercept
        instance._dataset.RescaleSlope = rescale_slope

        # Set Type 2 elements
        instance._dataset.KVP = kvp
        instance._dataset.AcquisitionNumber = acquisition_number

        return instance
    
    def with_optional_elements(
        self,
        # Type 3 elements as kwargs with None defaults (truly optional)
        multi_energy_ct_acquisition: str | MultiEnergyCTAcquisition | None = None,
        scan_options: str | ScanOptions | None = None,
        data_collection_diameter: float | None = None,
        data_collection_center_patient: list[float] | None = None,
        reconstruction_diameter: float | None = None,
        reconstruction_target_center_patient: list[float] | None = None,
        distance_source_to_detector: float | None = None,
        distance_source_to_patient: float | None = None,
        gantry_detector_tilt: float | None = None,
        table_height: float | None = None,
        rotation_direction: str | RotationDirection | None = None,
        exposure_time: float | None = None,
        x_ray_tube_current: float | None = None,
        exposure: float | None = None,
        exposure_in_uas: float | None = None,
        filter_type: str | FilterType | None = None,
        filter_material: list[str | FilterMaterial] | None = None,
        generator_power: float | None = None,
        focal_spots: list[float] | None = None,
        convolution_kernel: str | None = None,
        revolution_time: float | None = None,
        single_collimation_width: float | None = None,
        total_collimation_width: float | None = None,
        table_speed: float | None = None,
        table_feed_per_rotation: float | None = None,
        spiral_pitch_factor: float | None = None,
        exposure_modulation_type: str | ExposureModulationType | None = None,
        ctdivol: float | None = None,
        ctdi_phantom_type_code_sequence: list[Dataset] | None = None,
        water_equivalent_diameter: float | None = None,
        image_and_fluoroscopy_area_dose_product: float | None = None,
        calcium_scoring_mass_factor_patient: float | None = None,
        calcium_scoring_mass_factor_device: list[float] | None = None,
        ct_additional_x_ray_source_sequence: list[Dataset] | None = None,
        multi_energy_ct_acquisition_sequence: list[Dataset] | None = None
    ) -> 'CTImageModule':
        """Add optional (Type 3) elements."""
        # Set attributes directly on internal dataset (format enums if needed)
        if multi_energy_ct_acquisition is not None:
            self._dataset.MultienergyCTAcquisition = format_enum_value(multi_energy_ct_acquisition)
        if scan_options is not None:
            self._dataset.ScanOptions = format_enum_value(scan_options)
        if data_collection_diameter is not None:
            self._dataset.DataCollectionDiameter = data_collection_diameter
        if data_collection_center_patient is not None:
            self._dataset.DataCollectionCenterPatient = data_collection_center_patient
        if reconstruction_diameter is not None:
            self._dataset.ReconstructionDiameter = reconstruction_diameter
        if reconstruction_target_center_patient is not None:
            self._dataset.ReconstructionTargetCenterPatient = reconstruction_target_center_patient
        if distance_source_to_detector is not None:
            self._dataset.DistanceSourceToDetector = distance_source_to_detector
        if distance_source_to_patient is not None:
            self._dataset.DistanceSourceToPatient = distance_source_to_patient
        if gantry_detector_tilt is not None:
            self._dataset.GantryDetectorTilt = gantry_detector_tilt
        if table_height is not None:
            self._dataset.TableHeight = table_height
        if rotation_direction is not None:
            self._dataset.RotationDirection = format_enum_value(rotation_direction)
        if exposure_time is not None:
            self._dataset.ExposureTime = exposure_time
        if x_ray_tube_current is not None:
            self._dataset.XRayTubeCurrent = x_ray_tube_current
        if exposure is not None:
            self._dataset.Exposure = exposure
        if exposure_in_uas is not None:
            self._dataset.ExposureInuAs = exposure_in_uas
        if filter_type is not None:
            self._dataset.FilterType = format_enum_value(filter_type)
        if filter_material is not None:
            self._dataset.FilterMaterial = [format_enum_value(material) for material in filter_material]
        if generator_power is not None:
            self._dataset.GeneratorPower = generator_power
        if focal_spots is not None:
            self._dataset.FocalSpots = focal_spots
        if convolution_kernel is not None:
            self._dataset.ConvolutionKernel = convolution_kernel
        if revolution_time is not None:
            self._dataset.RevolutionTime = revolution_time
        if single_collimation_width is not None:
            self._dataset.SingleCollimationWidth = single_collimation_width
        if total_collimation_width is not None:
            self._dataset.TotalCollimationWidth = total_collimation_width
        if table_speed is not None:
            self._dataset.TableSpeed = table_speed
        if table_feed_per_rotation is not None:
            self._dataset.TableFeedPerRotation = table_feed_per_rotation
        if spiral_pitch_factor is not None:
            self._dataset.SpiralPitchFactor = spiral_pitch_factor
        if exposure_modulation_type is not None:
            self._dataset.ExposureModulationType = format_enum_value(exposure_modulation_type)
        if ctdivol is not None:
            self._dataset.CTDIvol = ctdivol
        if ctdi_phantom_type_code_sequence is not None:
            self._dataset.CTDIPhantomTypeCodeSequence = ctdi_phantom_type_code_sequence
        if water_equivalent_diameter is not None:
            self._dataset.WaterEquivalentDiameter = water_equivalent_diameter
        if image_and_fluoroscopy_area_dose_product is not None:
            self._dataset.ImageAndFluoroscopyAreaDoseProduct = image_and_fluoroscopy_area_dose_product
        if calcium_scoring_mass_factor_patient is not None:
            self._dataset.CalciumScoringMassFactorPatient = calcium_scoring_mass_factor_patient
        if calcium_scoring_mass_factor_device is not None:
            self._dataset.CalciumScoringMassFactorDevice = calcium_scoring_mass_factor_device

        # Handle multi-energy exclusion logic
        multi_energy_value = format_enum_value(multi_energy_ct_acquisition) if multi_energy_ct_acquisition is not None else None
        if multi_energy_value == "YES":
            # CT Additional X-Ray Source Sequence shall not be present if multi-energy is YES
            if ct_additional_x_ray_source_sequence is not None:
                # This will be caught by validation as an error
                pass
        else:
            if ct_additional_x_ray_source_sequence is not None:
                self._dataset.CTAdditionalXRaySourceSequence = ct_additional_x_ray_source_sequence

        if multi_energy_ct_acquisition_sequence is not None:
            self._dataset.MultienergyCTAcquisitionSequence = multi_energy_ct_acquisition_sequence

        return self
    
    def with_rescale_type_conditional(
        self,
        rescale_type: str | RescaleType | None = None
    ) -> 'CTImageModule':
        """Add rescale type with conditional validation.

        Type 1C: Required if the Rescale Type is not HU (Hounsfield Units),
        or Multi-energy CT Acquisition (0018,9361) is YES.

        Args:
            rescale_type: Specifies the output units of Rescale Slope and Rescale Intercept (0028,1054) Type 1C
        """
        # Check conditions for Type 1C requirement
        multi_energy_acquisition = getattr(self._dataset, 'MultienergyCTAcquisition', '')
        condition = (multi_energy_acquisition == "YES" or
                    (rescale_type and format_enum_value(rescale_type) != "HU"))

        if condition and rescale_type:
            self._dataset.RescaleType = format_enum_value(rescale_type)
        elif condition and not rescale_type:
            # This will be caught by validation
            pass
        elif rescale_type:
            # May be present even when not required
            self._dataset.RescaleType = format_enum_value(rescale_type)

        return self
    
    def with_energy_weighting_conditional(
        self,
        energy_weighting_factor: float | None = None,
        derivation_code_sequence: list[Dataset] | None = None
    ) -> 'CTImageModule':
        """Add energy weighting factor with conditional validation.

        Type 1C: Required if one Derivation Code Sequence (0008,9215) Item value
        is "Multi-energy proportional weighting".

        Args:
            energy_weighting_factor: Weighting factor of data from primary source (0018,9353) Type 1C
            derivation_code_sequence: Derivation code sequence for validation (0008,9215)
        """
        # Check for multi-energy proportional weighting condition
        has_multi_energy_weighting = False
        if derivation_code_sequence:
            for item in derivation_code_sequence:
                if (item.get('CodeValue') == '113097' and
                    item.get('CodingSchemeDesignator') == 'DCM'):
                    has_multi_energy_weighting = True
                    break

        if has_multi_energy_weighting and energy_weighting_factor is not None:
            self._dataset.EnergyWeightingFactor = energy_weighting_factor
        elif energy_weighting_factor is not None:
            # May be present even when not required
            self._dataset.EnergyWeightingFactor = energy_weighting_factor

        return self
    
    def with_water_equivalent_diameter_conditional(
        self,
        water_equivalent_diameter_calculation_method_code_sequence: list[Dataset] | None = None
    ) -> 'CTImageModule':
        """Add water equivalent diameter calculation method with conditional validation.

        Type 1C: Required if Water Equivalent Diameter (0018,1271) is present.

        Args:
            water_equivalent_diameter_calculation_method_code_sequence: Method of calculation (0018,1272) Type 1C
        """
        has_water_diameter = hasattr(self._dataset, 'WaterEquivalentDiameter')

        if has_water_diameter and water_equivalent_diameter_calculation_method_code_sequence:
            self._dataset.WaterEquivalentDiameterCalculationMethodCodeSequence = water_equivalent_diameter_calculation_method_code_sequence
        elif has_water_diameter and not water_equivalent_diameter_calculation_method_code_sequence:
            # This will be caught by validation
            pass

        return self
    
    @staticmethod
    def create_ctdi_phantom_type_code_item(
        code_value: str,
        coding_scheme_designator: str = "DCM",
        code_meaning: str | None = None
    ) -> Dataset:
        """Create CTDI phantom type code sequence item.

        Args:
            code_value: Code value for phantom type
            coding_scheme_designator: Coding scheme designator (default: DCM)
            code_meaning: Human readable meaning of the code

        Returns:
            Dataset representing code sequence item
        """
        item = Dataset()
        item.CodeValue = code_value
        item.CodingSchemeDesignator = coding_scheme_designator
        if code_meaning:
            item.CodeMeaning = code_meaning
        return item
    
    @staticmethod
    def create_water_equivalent_diameter_calculation_method_item(
        code_value: str,
        coding_scheme_designator: str = "DCM",
        code_meaning: str | None = None
    ) -> Dataset:
        """Create Water Equivalent Diameter Calculation Method Code Sequence item.

        Args:
            code_value: Code value for calculation method
            coding_scheme_designator: Coding scheme designator (default: DCM)
            code_meaning: Human readable meaning of the code

        Returns:
            Dataset representing code sequence item
        """
        item = Dataset()
        item.CodeValue = code_value
        item.CodingSchemeDesignator = coding_scheme_designator
        if code_meaning:
            item.CodeMeaning = code_meaning
        return item
    
    @staticmethod
    def create_ct_additional_x_ray_source_item(
        kvp: float,
        x_ray_tube_current_in_ma: float,
        data_collection_diameter: float,
        focal_spots: list[float],
        filter_type: str,
        filter_material: list[str],
        exposure_in_mas: float | None = None,
        energy_weighting_factor: float | None = None
    ) -> Dataset:
        """Create CT additional X-Ray source sequence item.

        Args:
            kvp: Peak kilo voltage output (0018,0060) Type 1
            x_ray_tube_current_in_ma: Nominal X-Ray tube current (0018,9330) Type 1
            data_collection_diameter: Diameter of data collection region (0018,0090) Type 1
            focal_spots: Used nominal size of focal spot (0018,1190) Type 1
            filter_type: Type of filter(s) inserted (0018,1160) Type 1
            filter_material: X-Ray absorbing material used (0018,7050) Type 1
            exposure_in_mas: Exposure in milliampere seconds (0018,9332) Type 3
            energy_weighting_factor: Weighting factor for multi-energy (0018,9353) Type 1C

        Returns:
            Dataset representing additional X-Ray source item
        """
        item = Dataset()
        item.KVP = kvp
        item.XRayTubeCurrentInmA = x_ray_tube_current_in_ma
        item.DataCollectionDiameter = data_collection_diameter
        item.FocalSpots = focal_spots
        item.FilterType = filter_type
        item.FilterMaterial = filter_material
        if exposure_in_mas is not None:
            item.ExposureInmAs = exposure_in_mas
        if energy_weighting_factor is not None:
            item.EnergyWeightingFactor = energy_weighting_factor
        return item
    
    @property
    def is_multi_energy(self) -> bool:
        """Check if this is a multi-energy CT acquisition.

        Returns:
            bool: True if Multi-energy CT Acquisition is YES
        """
        return getattr(self._dataset, 'MultienergyCTAcquisition', '') == "YES"

    @property
    def is_axial_image(self) -> bool:
        """Check if this is an axial CT image.

        Returns:
            bool: True if Image Type Value 3 is AXIAL
        """
        image_type = getattr(self._dataset, 'ImageType', [])
        return len(image_type) >= 3 and image_type[2] == "AXIAL"

    @property
    def is_localizer_image(self) -> bool:
        """Check if this is a localizer CT image.

        Returns:
            bool: True if Image Type Value 3 is LOCALIZER
        """
        image_type = getattr(self._dataset, 'ImageType', [])
        return len(image_type) >= 3 and image_type[2] == "LOCALIZER"

    @property
    def has_rescale_type(self) -> bool:
        """Check if rescale type is present.

        Returns:
            bool: True if Rescale Type is present
        """
        return hasattr(self._dataset, 'RescaleType')

    @property
    def has_water_equivalent_diameter(self) -> bool:
        """Check if water equivalent diameter is present.

        Returns:
            bool: True if Water Equivalent Diameter is present
        """
        return hasattr(self._dataset, 'WaterEquivalentDiameter')
    
    def validate(self, config: ValidationConfig | None = None) -> ValidationResult:
        """Validate this CT Image Module instance.

        Args:
            config (ValidationConfig | None): Optional validation configuration

        Returns:
            ValidationResult instance
        """
        return CTImageValidator.validate(self._dataset, config)
