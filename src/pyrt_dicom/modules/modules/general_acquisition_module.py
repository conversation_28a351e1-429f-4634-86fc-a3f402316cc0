"""General Acquisition Module - DICOM PS3.3 C.7.10.1"""

from datetime import datetime, date
from .base_module import BaseModule
from ...validators import ValidationResult
from ...validators.modules.general_acquisition_validator import GeneralAcquisitionValidator
from ...validators.modules.base_validator import ValidationConfig
from ...utils.dicom_formatters import format_date_value, format_time_value, format_enum_value


class GeneralAcquisitionModule(BaseModule):
    """General Acquisition Module implementation for DICOM PS3.3 C.7.10.1.
    
    Contains attributes that identify and describe general information about 
    an Acquisition. All elements in this module are Type 3 (optional).
    
    Usage:
        # Create with all required elements (all Type 3, so empty instance is valid)
        acquisition = GeneralAcquisitionModule.from_required_elements()
        
        # Add optional elements
        acquisition = acquisition.with_optional_elements(
            acquisition_uid="*******.*******.**********",
            acquisition_number=1,
            acquisition_date="20240101",
            acquisition_time="120000",
            acquisition_datetime="20240101120000.000000",
            acquisition_duration=30.5,
            images_in_acquisition=100,
            irradiation_event_uid="*******.*******.**********"  # Single UID
        )
        
        # Or with multiple irradiation event UIDs (VM 1-n)  
        acquisition = acquisition.with_optional_elements(
            irradiation_event_uid=[
                "*******.*******.**********",
                "*******.*******.**********"
            ]
        )
        
        # Generate dataset for IOD integration
        dataset = acquisition.to_dataset()
        
        # Validate
        result = acquisition.validate()
    """

    @classmethod
    def from_required_elements(cls) -> 'GeneralAcquisitionModule':
        """Create General Acquisition Module with all required elements.
        
        Note: All elements in this module are Type 3 (optional), so no required elements.
        """
        instance = cls()
        return instance
    
    def with_optional_elements(
        self,
        # Type 3 elements as kwargs with None defaults (truly optional)
        acquisition_uid: str | None = None,
        acquisition_number: int | None = None,
        acquisition_date: str | datetime | date | None = None,
        acquisition_time: str | datetime | None = None,
        acquisition_datetime: str | datetime | None = None,
        acquisition_duration: float | None = None,
        images_in_acquisition: int | None = None,
        irradiation_event_uid: str | list[str] | None = None
    ) -> 'GeneralAcquisitionModule':
        """Add optional (Type 3) elements.
        
        Args:
            acquisition_uid: Unique identifier for the acquisition (0008,0017)
            acquisition_number: Number identifying the acquisition (0020,0012)
            acquisition_date: Date the acquisition started (0008,0022)
            acquisition_time: Time the acquisition started (0008,0032)
            acquisition_datetime: Date and time the acquisition started (0008,002A)
            acquisition_duration: Duration of acquisition in seconds (0018,9073)
            images_in_acquisition: Number of images from this acquisition (0020,1002)
            irradiation_event_uid: UID(s) of irradiation event(s) (0008,3010) - VM 1-n
            
        Returns:
            GeneralAcquisitionModule: Self for method chaining
        """
        # Use direct dataset assignment instead of helper methods
        if acquisition_uid is not None:
            self._dataset.AcquisitionUID = acquisition_uid
        if acquisition_number is not None:
            self._dataset.AcquisitionNumber = acquisition_number
        
        # Format date value using utility function
        if acquisition_date is not None:
            self._dataset.AcquisitionDate = format_date_value(acquisition_date)
        
        # Format time value using utility function
        if acquisition_time is not None:
            self._dataset.AcquisitionTime = format_time_value(acquisition_time)
        
        # Format datetime value (inline implementation)
        if acquisition_datetime is not None:
            if isinstance(acquisition_datetime, datetime):
                formatted_datetime = acquisition_datetime.strftime("%Y%m%d%H%M%S.%f")
            else:
                formatted_datetime = str(acquisition_datetime)
            self._dataset.AcquisitionDateTime = formatted_datetime
        
        if acquisition_duration is not None:
            self._dataset.AcquisitionDuration = acquisition_duration
        if images_in_acquisition is not None:
            self._dataset.ImagesInAcquisition = images_in_acquisition
        
        # Handle VM 1-n for IrradiationEventUID
        if irradiation_event_uid is not None:
            self._dataset.IrradiationEventUID = irradiation_event_uid
        
        return self
    
    @property
    def has_acquisition_identification(self) -> bool:
        """Check if acquisition identification is present.
        
        Returns:
            bool: True if UID or number is present
        """
        return (hasattr(self._dataset, 'AcquisitionUID') or 
                hasattr(self._dataset, 'AcquisitionNumber'))
    
    @property
    def has_timing_information(self) -> bool:
        """Check if timing information is present.
        
        Returns:
            bool: True if date, time, datetime, or duration is present
        """
        return (hasattr(self._dataset, 'AcquisitionDate') or 
                hasattr(self._dataset, 'AcquisitionTime') or
                hasattr(self._dataset, 'AcquisitionDateTime') or
                hasattr(self._dataset, 'AcquisitionDuration'))
    
    @property
    def has_irradiation_event(self) -> bool:
        """Check if irradiation event information is present.
        
        Returns:
            bool: True if Irradiation Event UID is present
        """
        return hasattr(self._dataset, 'IrradiationEventUID')
    
    @property
    def has_image_count(self) -> bool:
        """Check if image count information is present.
        
        Returns:
            bool: True if Images in Acquisition is present
        """
        return hasattr(self._dataset, 'ImagesInAcquisition')
    
    @property
    def acquisition_uid_value(self) -> str | None:
        """Get the acquisition UID value.
        
        Returns:
            str | None: Acquisition UID value or None if not present
        """
        return getattr(self._dataset, 'AcquisitionUID', None)
    
    @property
    def acquisition_number_value(self) -> int | None:
        """Get the acquisition number value.
        
        Returns:
            int | None: Acquisition number value or None if not present
        """
        return getattr(self._dataset, 'AcquisitionNumber', None)
    
    @property
    def acquisition_date_value(self) -> str | None:
        """Get the acquisition date value.
        
        Returns:
            str | None: Acquisition date value or None if not present
        """
        return getattr(self._dataset, 'AcquisitionDate', None)
    
    @property
    def acquisition_time_value(self) -> str | None:
        """Get the acquisition time value.
        
        Returns:
            str | None: Acquisition time value or None if not present
        """
        return getattr(self._dataset, 'AcquisitionTime', None)
    
    @property
    def acquisition_datetime_value(self) -> str | None:
        """Get the acquisition datetime value.
        
        Returns:
            str | None: Acquisition datetime value or None if not present
        """
        return getattr(self._dataset, 'AcquisitionDateTime', None)
    
    @property
    def acquisition_duration_value(self) -> float | None:
        """Get the acquisition duration value in seconds.
        
        Returns:
            float | None: Acquisition duration in seconds or None if not present
        """
        return getattr(self._dataset, 'AcquisitionDuration', None)
    
    @property
    def images_in_acquisition_value(self) -> int | None:
        """Get the number of images in acquisition.
        
        Returns:
            int | None: Number of images or None if not present
        """
        return getattr(self._dataset, 'ImagesInAcquisition', None)
    
    @property
    def irradiation_event_uid_value(self) -> str | list[str] | None:
        """Get the irradiation event UID value(s).
        
        Returns:
            str | list[str] | None: Irradiation event UID value(s) or None if not present.
                                   Can be single string or list of strings (VM 1-n)
        """
        return getattr(self._dataset, 'IrradiationEventUID', None)
    
    def is_synchronized_with_external_clock(self) -> bool:
        """Check if acquisition time is synchronized with external clock.
        
        Note: This requires checking the Synchronization Module's 
        Acquisition Time Synchronized (0018,1800) attribute, which is 
        outside this module's scope.
        
        Returns:
            bool: False (cannot determine from this module alone)
        """
        # This would require access to the Synchronization Module
        # which is outside the scope of this module
        return False
    
    def get_acquisition_summary(self) -> 'pydicom.Dataset':
        """Get a summary of acquisition information.

        Returns:
            pydicom.Dataset: Summary of acquisition attributes as a pydicom Dataset
        """
        import pydicom
        summary = pydicom.Dataset()

        if self.has_acquisition_identification:
            identification_ds = pydicom.Dataset()
            if self.acquisition_uid_value is not None:
                identification_ds.AcquisitionUID = self.acquisition_uid_value
            if self.acquisition_number_value is not None:
                identification_ds.AcquisitionNumber = self.acquisition_number_value
            summary.Identification = identification_ds

        if self.has_timing_information:
            timing_ds = pydicom.Dataset()
            if self.acquisition_date_value is not None:
                timing_ds.AcquisitionDate = self.acquisition_date_value
            if self.acquisition_time_value is not None:
                timing_ds.AcquisitionTime = self.acquisition_time_value
            if self.acquisition_datetime_value is not None:
                timing_ds.AcquisitionDateTime = self.acquisition_datetime_value
            if self.acquisition_duration_value is not None:
                timing_ds.AcquisitionDuration = self.acquisition_duration_value
            summary.Timing = timing_ds

        if self.has_image_count and self.images_in_acquisition_value is not None:
            summary.ImagesInAcquisition = self.images_in_acquisition_value

        if self.has_irradiation_event and self.irradiation_event_uid_value is not None:
            # Handle VM 1-n: can be single string or list of strings
            summary.IrradiationEventUID = self.irradiation_event_uid_value

        return summary
    
    def validate(self, config: ValidationConfig | None = None) -> ValidationResult:
        """Validate this General Acquisition Module instance.
        
        Args:
            config: Optional validation configuration
            
        Returns:
            ValidationResult with 'errors' and 'warnings' lists
        """
        return GeneralAcquisitionValidator.validate(self._dataset, config)
