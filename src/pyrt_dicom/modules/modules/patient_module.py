"""
Patient Module - DICOM PS3.3 C.7.1.1

The Patient Module contains attributes of the Patient that are needed for
interpretation of the Composite Instances and are common for all Studies
performed on the Patient.
"""
from datetime import datetime, date
from pydicom import Dataset
from pydicom.valuerep import PersonName
from .base_module import BaseModule
from ...enums.patient_enums import PatientSex, ResponsiblePersonRole, TypeOfPatientID
from ...validators.modules.patient_validator import PatientValidator
from ...validators.modules.base_validator import ValidationConfig
from ...validators import ValidationResult


class PatientModule(BaseModule):
    """Patient Module implementation for DICOM PS3.3 C.7.1.1.
    
    Inherits from pydicom.Dataset to provide native DICOM data handling.
    Contains attributes of the Patient that are needed for interpretation of
    Composite Instances and are common for all Studies performed on the Patient.
    
    Usage:
        # Create with required elements
        patient = PatientModule.from_required_elements(
            patient_name="<PERSON><PERSON><PERSON><PERSON>",
            patient_id="12345",
            patient_birth_date="19900101",
            patient_sex=PatientSex.MALE
        )
        
        # Add optional elements
        patient.with_optional_elements(
            type_of_patient_id=TypeOfPatientID.TEXT,
            quality_control_subject="NO"
        )
        
        # Validate and save
        result = patient.validate()
        patient.save_as("patient.dcm", validate=True)
    """

    @classmethod
    def from_required_elements(
        cls,
        patient_name: str | PersonName = "",
        patient_id: str = "",
        patient_birth_date: str | datetime | date = "",
        patient_sex: str | PatientSex = ""
    ) -> 'PatientModule':
        """Create PatientModule from all required (Type 1 and Type 2) data elements.
        
        Args:
            patient_name (str | PersonName): Patient's full name (0010,0010) Type 2.
                Can be a string formatted as "Family^Given^Middle^Prefix^Suffix" 
                or a pydicom.valuerep.PersonName object.
            patient_id (str): Primary identifier for the Patient (0010,0020) Type 2
            patient_birth_date (str | datetime | date): Birth date of the Patient (0010,0030) Type 2.
                Can be a string in YYYYMMDD format, datetime object, or date object.
            patient_sex (str | PatientSex): Sex of the named Patient - M/F/O or PatientSex enum (0010,0040) Type 2
            
        Returns:
            PatientModule: New dataset instance with required data elements set
        """
        instance = cls()
        instance._set_attribute('PatientName', patient_name)
        instance._set_attribute('PatientID', patient_id)
        instance._set_attribute('PatientBirthDate', instance._format_date_value(patient_birth_date))
        instance._set_attribute('PatientSex', instance._format_enum_value(patient_sex))
        return instance
    
    def with_optional_elements(
        self,
        type_of_patient_id: str | TypeOfPatientID | None = None,
        referenced_patient_photo_sequence: list[Dataset] | None = None,
        quality_control_subject: str | None = None,
        referenced_patient_sequence: list[Dataset] | None = None,
        patient_birth_time: str | datetime | None = None,
        other_patient_ids_sequence: list[Dataset] | None = None,
        other_patient_names: str | PersonName | None = None,
        ethnic_group_code_sequence: list[Dataset] | None = None,
        ethnic_groups: str | None = None,
        patient_comments: str | None = None,
        strain_description: str | None = None,
        strain_nomenclature: str | None = None,
        strain_code_sequence: list[Dataset] | None = None,
        strain_additional_information: str | None = None,
        strain_stock_sequence: list[Dataset] | None = None,
        genetic_modifications_sequence: list[Dataset] | None = None,
        patient_identity_removed: str | None = None
    ) -> 'PatientModule':
        """Add optional (Type 3) data elements without conditional requirements.
        
        Args:
            type_of_patient_id (str | TypeOfPatientID | None): Type of identifier - TEXT/RFID/BARCODE or TypeOfPatientID enum (0010,0022)
            referenced_patient_photo_sequence (list[Dataset] | None): Photo to confirm identity (0010,1100)
            quality_control_subject (str | None): Quality control phantom - YES/NO (0010,0200)
            referenced_patient_sequence (list[Dataset] | None): Reference to a Patient (0008,1120)
            patient_birth_time (str | datetime | None): Birth time of Patient (0010,0032).
                Can be a string in HHMMSS format or datetime object.
                If datetime is provided, only the time portion is used.
            other_patient_ids_sequence (list[Dataset] | None): Additional patient identifiers (0010,1002)
            other_patient_names (str | PersonName | None): Other names for patient (0010,1001).
                Can be a string formatted as "Family^Given^Middle^Prefix^Suffix" 
                or a pydicom.valuerep.PersonName object.
            ethnic_group_code_sequence (list[Dataset] | None): Ethnic group codes (0010,2161)
            ethnic_groups (str | None): Ethnic group values (0010,2162)
            patient_comments (str | None): Additional patient information (0010,4000)
            strain_description (str | None): Strain of the Patient (0010,0212)
            strain_nomenclature (str | None): Nomenclature for strain (0010,0213)
            strain_code_sequence (list[Dataset] | None): Coded strain identification (0010,0219)
            strain_additional_information (str | None): Additional strain info (0010,0218)
            strain_stock_sequence (list[Dataset] | None): Strain stock information (0010,0216)
            genetic_modifications_sequence (list[Dataset] | None): Genetic modifications (0010,0221)
            patient_identity_removed (str | None): Identity removal flag - YES/NO (0012,0062)
            
        Returns:
            PatientModule: Self with optional elements added
        """
        if type_of_patient_id is not None:
            self._set_attribute('TypeOfPatientID', self._format_enum_value(type_of_patient_id))
        self._set_attribute_if_not_none('ReferencedPatientPhotoSequence', referenced_patient_photo_sequence)
        self._set_attribute_if_not_none('QualityControlSubject', quality_control_subject)
        self._set_attribute_if_not_none('ReferencedPatientSequence', referenced_patient_sequence)
        if patient_birth_time is not None:
            self._set_attribute('PatientBirthTime', self._format_time_value(patient_birth_time))
        self._set_attribute_if_not_none('OtherPatientIDsSequence', other_patient_ids_sequence)
        self._set_attribute_if_not_none('OtherPatientNames', other_patient_names)
        self._set_attribute_if_not_none('EthnicGroupCodeSequence', ethnic_group_code_sequence)
        self._set_attribute_if_not_none('EthnicGroups', ethnic_groups)
        self._set_attribute_if_not_none('PatientComments', patient_comments)
        self._set_attribute_if_not_none('StrainDescription', strain_description)
        self._set_attribute_if_not_none('StrainNomenclature', strain_nomenclature)
        self._set_attribute_if_not_none('StrainCodeSequence', strain_code_sequence)
        self._set_attribute_if_not_none('StrainAdditionalInformation', strain_additional_information)
        self._set_attribute_if_not_none('StrainStockSequence', strain_stock_sequence)
        self._set_attribute_if_not_none('GeneticModificationsSequence', genetic_modifications_sequence)
        self._set_attribute_if_not_none('PatientIdentityRemoved', patient_identity_removed)
        return self
    
    def with_alternative_calendar(
        self,
        patient_alternative_calendar: str,
        patient_birth_date_in_alternative_calendar: str | datetime | date | None = None,
        patient_death_date_in_alternative_calendar: str | datetime | date | None = None
    ) -> 'PatientModule':
        """Add alternative calendar dates with required calendar specification.
        
        Note: Patient's Alternative Calendar (0010,0035) is Type 1C - required if either
        birth or death date in alternative calendar is present.
        
        Args:
            patient_alternative_calendar (str): Alternative calendar used (0010,0035) Type 1C
            patient_birth_date_in_alternative_calendar (str | datetime | date | None): Birth date in alt calendar (0010,0033) Type 3.
                Can be a string, datetime object, or date object.
            patient_death_date_in_alternative_calendar (str | datetime | date | None): Death date in alt calendar (0010,0034) Type 3.
                Can be a string, datetime object, or date object.
            
        Returns:
            PatientModule: Self with alternative calendar elements added
        """
        self._set_attribute('PatientAlternativeCalendar', patient_alternative_calendar)
        if patient_birth_date_in_alternative_calendar is not None:
            self._set_attribute('PatientBirthDateInAlternativeCalendar', self._format_date_value(patient_birth_date_in_alternative_calendar))
        if patient_death_date_in_alternative_calendar is not None:
            self._set_attribute('PatientDeathDateInAlternativeCalendar', self._format_date_value(patient_death_date_in_alternative_calendar))
        return self
    
    def with_non_human_organism(
        self,
        patient_species_description: str | None = None,
        patient_species_code_sequence: list[Dataset] | None = None,
        patient_breed_description: str | None = None,
        patient_breed_code_sequence: list[Dataset] | None = None,
        breed_registration_sequence: list[Dataset] | None = None,
        responsible_person: str | None = None,
        responsible_organization: str | None = None
    ) -> 'PatientModule':
        """Add non-human organism data with all required conditional elements.
        
        Note: For non-human organisms, the following are required:
        - Type 1C: Either Patient Species Description OR Patient Species Code Sequence
        - Type 2C: Patient Breed Code Sequence (may be empty)
        - Type 2C: Patient Breed Description (required if Breed Code Sequence is empty)
        - Type 2C: Breed Registration Sequence (may be empty)
        - Type 2C: Either Responsible Person OR Responsible Organization
        
        Args:
            patient_species_description (str | None): Taxonomic rank value (0010,2201) Type 1C
            patient_species_code_sequence (list[Dataset] | None): Coded taxonomic rank (0010,2202) Type 1C
            patient_breed_description (str | None): Breed description (0010,2292) Type 2C
            patient_breed_code_sequence (list[Dataset] | None): Coded breed (0010,2293) Type 2C
            breed_registration_sequence (list[Dataset] | None): Breed registry info (0010,2294) Type 2C
            responsible_person (str | None): Person with authority (0010,2297) Type 2C
            responsible_organization (str | None): Organization with authority (0010,2299) Type 2C
            
        Returns:
            PatientModule: Self with non-human organism elements added
            
        Raises:
            ValueError: If required conditional elements are missing
        """
        # Type 1C requirement: Either species description OR species code sequence
        self._validate_conditional_requirement(
            True,  # Always required for non-human organisms
            [patient_species_description, patient_species_code_sequence],
            "Either patient_species_description or patient_species_code_sequence is required for non-human organisms"
        )

        # Type 2C requirement: Either responsible person OR responsible organization
        self._validate_conditional_requirement(
            True,  # Always required for non-human organisms
            [responsible_person, responsible_organization],
            "Either responsible_person or responsible_organization is required for non-human organisms"
        )
        
        # Set species information (Type 1C)
        self._set_attribute_if_not_none('PatientSpeciesDescription', patient_species_description)
        self._set_attribute_if_not_none('PatientSpeciesCodeSequence', patient_species_code_sequence)

        # Set breed information (Type 2C)
        self._set_attribute('PatientBreedCodeSequence', patient_breed_code_sequence or [])

        # Type 2C: Breed description required if breed code sequence is empty
        breed_code_seq = patient_breed_code_sequence or []
        if not breed_code_seq and patient_breed_description is None:
            raise ValueError("patient_breed_description is required when patient_breed_code_sequence is empty")
        self._set_attribute('PatientBreedDescription', patient_breed_description or "")

        # Set registration and responsibility (Type 2C)
        self._set_attribute('BreedRegistrationSequence', breed_registration_sequence or [])
        self._set_attribute_if_not_none('ResponsiblePerson', responsible_person)
        self._set_attribute_if_not_none('ResponsibleOrganization', responsible_organization)
        
        return self
    
    def with_responsible_person(
        self,
        responsible_person: str | PersonName,
        responsible_person_role: str | ResponsiblePersonRole
    ) -> 'PatientModule':
        """Add responsible person with required role specification.
        
        Note: Responsible Person Role (0010,2298) is Type 1C - required if 
        Responsible Person is present and has a value.
        
        Args:
            responsible_person (str | PersonName): Name of person with authority (0010,2297).
                Can be a string formatted as "Family^Given^Middle^Prefix^Suffix" 
                or a pydicom.valuerep.PersonName object.
            responsible_person_role (str | ResponsiblePersonRole): Role or ResponsiblePersonRole enum (0010,2298) Type 1C
            
        Returns:
            PatientModule: Self with responsible person elements added
        """
        self._set_attribute('ResponsiblePerson', responsible_person)
        self._set_attribute('ResponsiblePersonRole', self._format_enum_value(responsible_person_role))
        return self
    
    def with_deidentification(
        self,
        patient_identity_removed: str | None = "YES",
        de_identification_method: str | None = None,
        de_identification_method_code_sequence: list[Dataset] | None = None
    ) -> 'PatientModule':
        """Add patient identity removal with required method specification.
        
        Note: When Patient Identity Removed (0012,0062) is "YES", either 
        De-identification Method OR De-identification Method Code Sequence is Type 1C required.
        
        Args:
            patient_identity_removed (str | None): Identity removal flag - YES/NO (0012,0062) Type 3
            de_identification_method (str | None): Description of removal method (0012,0063) Type 1C
            de_identification_method_code_sequence (list[Dataset] | None): Coded removal method (0012,0064) Type 1C
            
        Returns:
            PatientModule: Self with deidentification elements added
            
        Raises:
            ValueError: If required conditional elements are missing
        """
        self._set_attribute('PatientIdentityRemoved', patient_identity_removed)
        
        # Type 1C requirement when identity removed is "YES"
        if patient_identity_removed == "YES":
            self._validate_conditional_requirement(
                True,  # Condition is met (identity removed is "YES")
                [de_identification_method, de_identification_method_code_sequence],
                "Either de_identification_method or de_identification_method_code_sequence is required when patient_identity_removed is 'YES'"
            )

            self._set_attribute_if_not_none('DeIdentificationMethod', de_identification_method)
            self._set_attribute_if_not_none('DeIdentificationMethodCodeSequence', de_identification_method_code_sequence)
        
        return self
    
    @staticmethod
    def create_other_patient_id_item(
        patient_id: str,
        type_of_patient_id: str | TypeOfPatientID
    ) -> Dataset:
        """Create an item for Other Patient IDs Sequence (0010,1002).
        
        Args:
            patient_id (str): An identifier for the Patient (0010,0020) Type 1
            type_of_patient_id (str | TypeOfPatientID): Type of identifier - TEXT/RFID/BARCODE or TypeOfPatientID enum (0010,0022) Type 1
            
        Returns:
            Dataset: Sequence item with Patient ID and Type
        """
        item = Dataset()
        item.PatientID = patient_id
        if isinstance(type_of_patient_id, TypeOfPatientID):
            item.TypeOfPatientID = type_of_patient_id.value
        else:
            item.TypeOfPatientID = type_of_patient_id
        return item
    
    @staticmethod
    def create_strain_stock_item(
        strain_stock_number: str,
        strain_source: str,
        strain_source_registry_code: Dataset
    ) -> Dataset:
        """Create an item for Strain Stock Sequence (0010,0216).
        
        Args:
            strain_stock_number (str): Stock number of the strain (0010,0214) Type 1
            strain_source (str): Organization source of the organism (0010,0217) Type 1
            strain_source_registry_code (Dataset): Registry code sequence item (0010,0215) Type 1
            
        Returns:
            Dataset: Sequence item with strain stock information
        """
        item = Dataset()
        item.StrainStockNumber = strain_stock_number
        item.StrainSource = strain_source
        item.StrainSourceRegistryCodeSequence = [strain_source_registry_code]
        return item
    
    @staticmethod
    def create_genetic_modification_item(
        genetic_modifications_description: str,
        genetic_modifications_nomenclature: str,
        genetic_modifications_code_sequence: list[Dataset] | None = None
    ) -> Dataset:
        """Create an item for Genetic Modifications Sequence (0010,0221).
        
        Args:
            genetic_modifications_description (str): Genetic modifications description (0010,0222) Type 1
            genetic_modifications_nomenclature (str): Nomenclature used (0010,0223) Type 1
            genetic_modifications_code_sequence (list[Dataset] | None): Coded identification (0010,0229) Type 3
            
        Returns:
            Dataset: Sequence item with genetic modification information
        """
        item = Dataset()
        item.GeneticModificationsDescription = genetic_modifications_description
        item.GeneticModificationsNomenclature = genetic_modifications_nomenclature
        
        if genetic_modifications_code_sequence is not None:
            item.GeneticModificationsCodeSequence = genetic_modifications_code_sequence
        
        return item
    
    @property
    def is_human(self) -> bool:
        """Check if this patient represents a human organism.
        
        Returns:
            bool: True if no species information indicates human organism
        """
        return not self.is_non_human
    
    @property
    def is_non_human(self) -> bool:
        """Check if this patient represents a non-human organism.
        
        Returns:
            bool: True if species information indicates non-human organism
        """
        return (hasattr(self, 'PatientSpeciesDescription') or 
                hasattr(self, 'PatientSpeciesCodeSequence'))
    
    @property
    def is_deidentified(self) -> bool:
        """Check if patient identity has been removed.
        
        Returns:
            bool: True if PatientIdentityRemoved is "YES"
        """
        return getattr(self, 'PatientIdentityRemoved', '') == "YES"
    
    @property
    def has_alternative_calendar_dates(self) -> bool:
        """Check if alternative calendar dates are present.
        
        Returns:
            bool: True if birth or death date in alternative calendar is present
        """
        return (hasattr(self, 'PatientBirthDateInAlternativeCalendar') or
                hasattr(self, 'PatientDeathDateInAlternativeCalendar'))
    
    def validate(self, config: ValidationConfig | None = None) -> ValidationResult:
        """Validate this Patient Module instance.
        
        Args:
            config (ValidationConfig | None): Optional validation configuration
            
        Returns:
            ValidationResult with 'errors' and 'warnings' lists
        """
        return PatientValidator.validate(self, config)