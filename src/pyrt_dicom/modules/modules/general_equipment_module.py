"""
General Equipment Module - DICOM PS3.3 C.7.5.1

The General Equipment Module identifies and describes the piece of equipment 
that produced Composite Instances.
"""
from datetime import datetime, date
from pydicom import Dataset
from .base_module import BaseModule
from ...validators.modules.general_equipment_validator import GeneralEquipmentValidator
from ...validators.modules.base_validator import ValidationConfig
from ...validators import ValidationResult


class GeneralEquipmentModule(BaseModule):
    """General Equipment Module implementation for DICOM PS3.3 C.7.5.1.
    
    Inherits from pydicom.Dataset to provide native DICOM data handling.
    Identifies and describes the piece of equipment that produced Composite Instances.
    
    Usage:
        # Create with required elements
        equipment = GeneralEquipmentModule.from_required_elements(
            manufacturer="ACME Medical Systems"
        )
        
        # Add optional elements
        equipment.with_optional_elements(
            institution_name="General Hospital",
            station_name="CT01",
            manufacturers_model_name="SuperScan 3000",
            device_serial_number="12345",
            software_versions=["v2.1.0", "kernel-1.5.2"]
        )
        
        # Add conditional pixel padding if needed
        equipment.with_pixel_padding(
            pixel_padding_value=0
        )
        
        # Validate
        result = equipment.validate()
    """

    @classmethod
    def from_required_elements(
        cls,
        manufacturer: str = ""
    ) -> 'GeneralEquipmentModule':
        """Create GeneralEquipmentModule from all required (Type 2) data elements.
        
        Args:
            manufacturer (str): Manufacturer of the equipment (0008,0070) Type 2.
                Required but may be empty.
                
        Returns:
            GeneralEquipmentModule: New module instance with required data elements set
        """
        instance = cls()
        instance.Manufacturer = manufacturer
        return instance
    
    def with_optional_elements(
        self,
        institution_name: str | None = None,
        institution_address: str | None = None,
        station_name: str | None = None,
        institutional_department_name: str | None = None,
        institutional_department_type_code_sequence: list[Dataset] | None = None,
        manufacturers_model_name: str | None = None,
        manufacturers_device_class_uid: list[str] | None = None,
        device_serial_number: str | None = None,
        software_versions: list[str] | None = None,
        gantry_id: str | None = None,
        udi_sequence: list[Dataset] | None = None,
        device_uid: str | None = None,
        spatial_resolution: float | None = None,
        date_of_manufacture: str | datetime | date | None = None,
        date_of_installation: str | datetime | date | None = None,
        date_of_last_calibration: list[str] | None = None,
        time_of_last_calibration: list[str] | None = None
    ) -> 'GeneralEquipmentModule':
        """Add optional (Type 3) elements.
        
        Args:
            institution_name (str | None): Institution where equipment is located (0008,0080) Type 3
            institution_address (str | None): Mailing address of institution (0008,0081) Type 3
            station_name (str | None): User defined name identifying machine (0008,1010) Type 3
            institutional_department_name (str | None): Department in institution (0008,1040) Type 3
            institutional_department_type_code_sequence (list[dict] | None): Coded department type (0008,1041) Type 3
            manufacturers_model_name (str | None): Manufacturer's model name (0008,1090) Type 3
            manufacturers_device_class_uid (list[str] | None): Manufacturer's device class UID (0018,100B) Type 3
            device_serial_number (str | None): Manufacturer's serial number (0018,1000) Type 3
            software_versions (list[str] | None): Software version designation (0018,1020) Type 3
            gantry_id (str | None): Identifier of gantry or positioner (0018,1008) Type 3
            udi_sequence (list[dict] | None): Unique Device Identifier sequence (0018,100A) Type 3
            device_uid (str | None): Unique identifier of equipment (0018,1002) Type 3
            spatial_resolution (float | None): Inherent limiting resolution in mm (0018,1050) Type 3
            date_of_manufacture (str | datetime | date | None): Date equipment manufactured (0018,1204) Type 3
            date_of_installation (str | datetime | date | None): Date equipment installed (0018,1205) Type 3
            date_of_last_calibration (list[str] | None): Date of last calibration (0018,1200) Type 3
            time_of_last_calibration (list[str] | None): Time of last calibration (0018,1201) Type 3
        """
        self._set_attribute_if_not_none('InstitutionName', institution_name)
        self._set_attribute_if_not_none('InstitutionAddress', institution_address)
        self._set_attribute_if_not_none('StationName', station_name)
        self._set_attribute_if_not_none('InstitutionalDepartmentName', institutional_department_name)
        self._set_attribute_if_not_none('InstitutionalDepartmentTypeCodeSequence', institutional_department_type_code_sequence)
        self._set_attribute_if_not_none('ManufacturerModelName', manufacturers_model_name)
        self._set_attribute_if_not_none('ManufacturersDeviceClassUID', manufacturers_device_class_uid)
        self._set_attribute_if_not_none('DeviceSerialNumber', device_serial_number)
        self._set_attribute_if_not_none('SoftwareVersions', software_versions)
        self._set_attribute_if_not_none('GantryID', gantry_id)
        self._set_attribute_if_not_none('UDISequence', udi_sequence)
        self._set_attribute_if_not_none('DeviceUID', device_uid)
        self._set_attribute_if_not_none('SpatialResolution', spatial_resolution)
        
        if date_of_manufacture is not None:
            self.DateOfManufacture = self._format_date_value(date_of_manufacture)
        if date_of_installation is not None:
            self.DateOfInstallation = self._format_date_value(date_of_installation)
        
        self._set_attribute_if_not_none('DateOfLastCalibration', date_of_last_calibration)
        self._set_attribute_if_not_none('TimeOfLastCalibration', time_of_last_calibration)
        
        return self
    
    def with_pixel_padding(
        self,
        pixel_padding_value: int | float
    ) -> 'GeneralEquipmentModule':
        """Add pixel padding value.
        
        Note: Pixel Padding Value (0028,0120) is Type 1C - required if Pixel Padding Range Limit 
        is present and either Pixel Data or Pixel Data Provider URL is present.
        
        Args:
            pixel_padding_value (int | float): Single pixel value used for padding (0028,0120) Type 1C
            
        Returns:
            GeneralEquipmentModule: Self for method chaining
        """
        self.PixelPaddingValue = pixel_padding_value
        return self
    
    @staticmethod
    def create_institutional_department_type_code_item(
        code_value: str,
        coding_scheme_designator: str,
        code_meaning: str,
        coding_scheme_version: str | None = None
    ) -> Dataset:
        """Create institutional department type code sequence item.

        Args:
            code_value (str): Code value
            coding_scheme_designator (str): Coding scheme designator
            code_meaning (str): Code meaning
            coding_scheme_version (str | None): Coding scheme version

        Returns:
            Dataset: Institutional department type code sequence item
        """
        item = Dataset()
        item.CodeValue = code_value
        item.CodingSchemeDesignator = coding_scheme_designator
        item.CodeMeaning = code_meaning
        if coding_scheme_version is not None:
            item.CodingSchemeVersion = coding_scheme_version
        return item
    
    @staticmethod
    def create_udi_item(
        unique_device_identifier: str,
        device_identifier: str | None = None,
        issuer_of_udi: str | None = None,
        udi_issuer_country_code: str | None = None
    ) -> Dataset:
        """Create UDI sequence item.

        Args:
            unique_device_identifier (str): Unique Device Identifier
            device_identifier (str | None): Device Identifier
            issuer_of_udi (str | None): Issuer of UDI
            udi_issuer_country_code (str | None): UDI Issuer Country Code

        Returns:
            Dataset: UDI sequence item
        """
        item = Dataset()
        item.UniqueDeviceIdentifier = unique_device_identifier
        if device_identifier is not None:
            item.DeviceIdentifier = device_identifier
        if issuer_of_udi is not None:
            item.IssuerOfUDI = issuer_of_udi
        if udi_issuer_country_code is not None:
            item.UDIIssuerCountryCode = udi_issuer_country_code
        return item
    
    @property
    def has_institution_info(self) -> bool:
        """Check if institution information is present.
        
        Returns:
            bool: True if institution name or address is present
        """
        return (hasattr(self, 'InstitutionName') or 
                hasattr(self, 'InstitutionAddress'))
    
    @property
    def has_device_identification(self) -> bool:
        """Check if device identification is present.
        
        Returns:
            bool: True if model name, serial number, or device UID is present
        """
        return (hasattr(self, 'ManufacturerModelName') or
                hasattr(self, 'DeviceSerialNumber') or
                hasattr(self, 'DeviceUID'))
    
    @property
    def has_calibration_info(self) -> bool:
        """Check if calibration information is present.
        
        Returns:
            bool: True if calibration date or time is present
        """
        return (hasattr(self, 'DateOfLastCalibration') or
                hasattr(self, 'TimeOfLastCalibration'))
    
    def validate(self, config: ValidationConfig | None = None) -> ValidationResult:
        """Validate this General Equipment Module instance.
        
        Args:
            config (ValidationConfig | None): Optional validation configuration
            
        Returns:
            ValidationResult with 'errors' and 'warnings' lists
        """
        return GeneralEquipmentValidator.validate(self, config)
