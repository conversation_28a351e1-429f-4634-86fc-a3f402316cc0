"""DICOM Modules - Concrete implementations of DICOM specification modules.

Each module represents a specific section of the DICOM standard, implemented as
concrete Python classes with all data elements as attributes for IntelliSense support.

All modules inherit from BaseModule for consistent API patterns and validation.
"""

# Patient Information Modules
from .modules.patient_module import PatientModule
from .modules.clinical_trial_subject_module import ClinicalTrialSubjectModule
from .modules.patient_study_module import PatientStudyModule

# Study & Series Modules
from .modules.general_study_module import GeneralStudyModule
from .modules.clinical_trial_study_module import ClinicalTrialStudyModule
from .modules.general_series_module import GeneralSeriesModule
from .modules.rt_series_module import RTSeriesModule
from .modules.clinical_trial_series_module import ClinicalTrialSeriesModule

# Image & Spatial Modules
from .modules.general_image_module import GeneralImageModule
from .modules.image_plane_module import ImagePlaneModule
from .modules.image_pixel_module import ImagePixelModule
from .modules.multi_frame_module import MultiFrameModule
from .modules.frame_of_reference_module import FrameOfReferenceModule
from .modules.cine_module import CineModule
from .modules.overlay_plane_module import OverlayPlaneModule

# Equipment & Common Modules
from .modules.general_equipment_module import GeneralEquipmentModule
from .modules.sop_common_module import SOPCommonModule
from .modules.common_instance_reference_module import CommonInstanceReferenceModule
from .modules.device_module import DeviceModule
from .modules.general_reference_module import GeneralReferenceModule

# Radiotherapy Modules
from .modules.rt_general_plan_module import RTGeneralPlanModule
from .modules.structure_set_module import StructureSetModule
from .modules.roi_contour_module import ROIContourModule
from .modules.rt_roi_observations_module import RTROIObservationsModule
from .modules.rt_prescription_module import RTPrescriptionModule
from .modules.rt_tolerance_tables_module import RTToleranceTablesModule
from .modules.rt_patient_setup_module import RTPatientSetupModule
from .modules.rt_fraction_scheme_module import RTFractionSchemeModule
from .modules.rt_beams_module import RTBeamsModule
from .modules.rt_brachy_application_setups_module import RTBrachyApplicationSetupsModule
from .modules.rt_dvh_module import RTDVHModule
from .modules.rt_dose_module import RTDoseModule
from .modules.rt_image_module import RTImageModule

# Specialized Modules
from .modules.ct_image_module import CTImageModule
from .modules.multi_energy_ct_image_module import MultiEnergyCTImageModule
from .modules.contrast_bolus_module import ContrastBolusModule
from .modules.general_acquisition_module import GeneralAcquisitionModule
from .modules.modality_lut_module import ModalityLutModule
from .modules.voi_lut_module import VoiLutModule
from .modules.approval_module import ApprovalModule
from .modules.frame_extraction_module import FrameExtractionModule
from .modules.enhanced_patient_orientation_module import EnhancedPatientOrientationModule
from .modules.synchronization_module import SynchronizationModule
from .modules.specimen_module import SpecimenModule

__all__ = [
    # Patient Information Modules
    "PatientModule",
    "ClinicalTrialSubjectModule",
    "PatientStudyModule",

    # Study & Series Modules
    "GeneralStudyModule",
    "ClinicalTrialStudyModule",
    "GeneralSeriesModule",
    "RTSeriesModule",
    "ClinicalTrialSeriesModule",

    # Image & Spatial Modules
    "GeneralImageModule",
    "ImagePlaneModule",
    "ImagePixelModule",
    "MultiFrameModule",
    "FrameOfReferenceModule",
    "CineModule",
    "OverlayPlaneModule",

    # Equipment & Common Modules
    "GeneralEquipmentModule",
    "SOPCommonModule",
    "CommonInstanceReferenceModule",
    "DeviceModule",
    "GeneralReferenceModule",

    # Radiotherapy Modules
    "RTGeneralPlanModule",
    "StructureSetModule",
    "ROIContourModule",
    "RTROIObservationsModule",
    "RTPrescriptionModule",
    "RTToleranceTablesModule",
    "RTPatientSetupModule",
    "RTFractionSchemeModule",
    "RTBeamsModule",
    "RTBrachyApplicationSetupsModule",
    "RTDVHModule",
    "RTDoseModule",
    "RTImageModule",

    # Specialized Modules
    "CTImageModule",
    "MultiEnergyCTImageModule",
    "ContrastBolusModule",
    "GeneralAcquisitionModule",
    "ModalityLutModule",
    "VoiLutModule",
    "ApprovalModule",
    "FrameExtractionModule",
    "EnhancedPatientOrientationModule",
    "SynchronizationModule",
    "SpecimenModule",
]
