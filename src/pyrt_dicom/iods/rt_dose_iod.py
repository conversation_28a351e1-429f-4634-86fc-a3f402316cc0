"""RT Dose IOD implementation for DICOM PS3.3 A.18.3.

The RT Dose IOD combines radiotherapy dose distribution modules into a complete
DICOM RT Dose object containing 3D dose matrices, scaling factors, and dose
calculation parameters. This implementation follows the new IOD design pattern
with explicit module constructors and on-demand dataset generation.
"""

from typing import Optional, Dict, Any

from .base_iod import BaseIOD, IODValidationError
from ..validators import ValidationResult
from ..validators.iods.rt_dose_iod_validator import RTDoseIODValidator
from ..validators.modules.base_validator import ValidationConfig
from ..modules import (
    PatientModule,
    GeneralStudyModule,
    RTSeriesModule,
    FrameOfReferenceModule,
    GeneralEquipmentModule,
    RTDoseModule,
    GeneralImageModule,
    ImagePlaneModule,
    ImagePixelModule,
    MultiFrameModule,
    RTDVHModule,
    SOPCommonModule,
    OverlayPlaneModule,
    ApprovalModule,
    FrameExtractionModule
)


class RTDoseIOD(BaseIOD):
    """RT Dose Information Object Definition for DICOM PS3.3 A.18.3.

    Combines radiotherapy dose distribution modules into a complete DICOM RT Dose object
    containing 3D dose matrices, scaling factors, and dose calculation parameters.

    The RT Dose IOD is designed to address the requirements for transfer of dose
    distributions calculated by radiotherapy treatment planning systems. These
    distributions may be represented as 2D or 3D grids and may also contain
    dose-volume histogram data.

    Required Modules (per DICOM PS3.3 A.18.3):
    - Patient Module (C.7.1.1) - M
    - General Study Module (C.7.2.1) - M
    - RT Series Module (C.8.8.1) - M
    - Frame of Reference Module (C.7.4.1) - M
    - General Equipment Module (C.7.5.1) - M
    - RT Dose Module (C.8.8.3) - M
    - SOP Common Module (C.12.1) - M

    Conditional Modules:
    - General Image Module (C.7.6.1) - C (Required if dose data contains grid-based doses)
    - Image Plane Module (C.7.6.2) - C (Required if dose data contains grid-based doses)
    - Image Pixel Module (C.7.6.3) - C (Required if dose data contains grid-based doses)
    - Multi-frame Module (C.7.6.6) - C (Required if dose data contains grid-based doses and pixel data is multi-frame)

    Optional Modules:
    - RT DVH Module (C.8.8.4) - U
    - Frame Extraction Module (C.12.3) - C (Required if SOP Instance created in response to Frame-Level retrieve)
    - Overlay Plane Module - U (Previously included, now optional)
    - Approval Module - U

    Usage:
        # Create RT Dose IOD with 3D dose distribution
        rt_dose = RTDoseIOD(
            patient_module=PatientModule.from_required_elements(
                patients_name="Doe^John",
                patient_id="12345"
            ),
            general_study_module=GeneralStudyModule.from_required_elements(
                study_instance_uid="*******.5"
            ),
            rt_series_module=RTSeriesModule.from_required_elements(
                modality="RTDOSE",
                series_instance_uid="*******.6"
            ),
            frame_of_reference_module=FrameOfReferenceModule.from_required_elements(
                frame_of_reference_uid="*******.7"  # Must match planning CT
            ),
            general_equipment_module=GeneralEquipmentModule.from_required_elements(
                manufacturer="Eclipse Treatment Planning System"
            ),
            rt_dose_module=RTDoseModule.from_required_elements(
                dose_units="GY",
                dose_type="PHYSICAL",
                dose_grid_scaling=0.001,  # Scale factor for dose values
                pixel_data=dose_3d_array  # 3D numpy array with dose distribution
            ),
            sop_common_module=SOPCommonModule.from_required_elements(
                sop_class_uid="1.2.840.10008.*******.1.481.2",  # RT Dose Storage
                sop_instance_uid="*******.8"
            ),
            # Optional: Add DVH data for analysis
            rt_dvh_module=RTDVHModule.from_required_elements(
                dvh_sequence=structure_dvhs
            ),
            # Optional: Add image plane for spatial registration
            image_plane_module=ImagePlaneModule.from_required_elements(
                pixel_spacing=[2.5, 2.5],  # Dose grid resolution
                image_orientation_patient=[1,0,0,0,1,0],
                image_position_patient=[-200, -200, -100]  # Dose grid origin
            )
        )

        # Access dose statistics
        max_dose = np.max(rt_dose.get_module('rt_dose').pixel_data) * rt_dose.get_module('rt_dose').dose_grid_scaling
        print(f"Maximum dose: {max_dose} Gy")

        # Generate dataset for export
        dataset = rt_dose.to_dataset()
        dataset.save_as("dose.dcm")
    """

    SOP_CLASS_UID = "1.2.840.10008.*******.1.481.2"  #: RT Dose Storage SOP Class UID per DICOM PS3.6

    def __init__(self,
                 patient_module: PatientModule,
                 general_study_module: GeneralStudyModule,
                 rt_series_module: RTSeriesModule,
                 frame_of_reference_module: FrameOfReferenceModule,
                 general_equipment_module: GeneralEquipmentModule,
                 rt_dose_module: RTDoseModule,
                 sop_common_module: SOPCommonModule,
                 general_image_module: Optional[GeneralImageModule] = None,
                 image_plane_module: Optional[ImagePlaneModule] = None,
                 image_pixel_module: Optional[ImagePixelModule] = None,
                 multi_frame_module: Optional[MultiFrameModule] = None,
                 rt_dvh_module: Optional[RTDVHModule] = None,
                 overlay_plane_module: Optional[OverlayPlaneModule] = None,
                 approval_module: Optional[ApprovalModule] = None,
                 frame_extraction_module: Optional[FrameExtractionModule] = None):
        """Create RT Dose IOD from constituent modules.

        Module Requirements:
            M - Required
            C - Conditional
            U - Optional

        Args:
            patient_module (PatientModule): Patient demographic and identification information (M, C.7.1.1)
            general_study_module (GeneralStudyModule): Study-level metadata including study UID and dates (M, C.7.2.1)
            rt_series_module (RTSeriesModule): RT series metadata (must have modality='RTDOSE') (M, C.8.8.1)
            frame_of_reference_module (FrameOfReferenceModule): Spatial coordinate system reference matching planning CT (M, C.7.4.1)
            general_equipment_module (GeneralEquipmentModule): Dose calculation system equipment information (M, C.7.5.1)
            rt_dose_module (RTDoseModule): Dose distribution data, scaling factors, and calculation parameters (M, C.8.8.3)
            sop_common_module (SOPCommonModule): SOP Class and Instance UIDs and creation metadata (M, C.12.1)
            
            general_image_module (GeneralImageModule, optional): Image attributes for dose visualization and display (C, C.7.6.1). 
                Required if dose data contains grid-based doses.
            image_plane_module (ImagePlaneModule, optional): Spatial characteristics for dose grid (C, C.7.6.2). 
                Required if dose data contains grid-based doses. Requires general_image_module.
            image_pixel_module (ImagePixelModule, optional): Pixel data attributes for dose grid (C, C.7.6.3). 
                Required if dose data contains grid-based doses. Requires general_image_module.
            multi_frame_module (MultiFrameModule, optional): Multi-frame attributes for dose slices (C, C.7.6.6). 
                Required if dose data contains multi-frame pixel data. Requires general_image_module.
            
            rt_dvh_module (RTDVHModule, optional): Dose volume histogram data for structures (U, C.8.8.4)
            overlay_plane_module (OverlayPlaneModule, optional): Overlay graphics for isodose lines (U, C.7.6.5)
            approval_module (ApprovalModule, optional): Dose calculation approval information (U, C.12.2)
            frame_extraction_module (FrameExtractionModule, optional): Frame extraction metadata (C, C.12.3). 
                Required if SOP Instance created from frame-level retrieve.

        Raises:
            IODValidationError: If module dependencies are not satisfied or modality is incorrect

        Example:
            # Create RT Dose IOD with 3D dose distribution
            rt_dose = RTDoseIOD(
                patient_module=PatientModule.from_required_elements(
                    patients_name="Doe^John",
                    patient_id="12345"
                ),
                general_study_module=GeneralStudyModule.from_required_elements(
                    study_instance_uid="*******.5"
                ),
                rt_series_module=RTSeriesModule.from_required_elements(
                    modality="RTDOSE",
                    series_instance_uid="*******.6"
                ),
                frame_of_reference_module=FrameOfReferenceModule.from_required_elements(
                    frame_of_reference_uid="*******.7"
                ),
                general_equipment_module=GeneralEquipmentModule.from_required_elements(
                    manufacturer="Eclipse Treatment Planning System"
                ),
                rt_dose_module=RTDoseModule.from_required_elements(
                    dose_units="GY",
                    dose_type="PHYSICAL",
                    dose_grid_scaling=0.001
                ),
                sop_common_module=SOPCommonModule.from_required_elements(
                    sop_class_uid="1.2.840.10008.*******.1.481.2",
                    sop_instance_uid="*******.8"
                )
            )
        """
        super().__init__()

        # Validate RT Dose-specific requirements
        if rt_series_module.Modality != 'RTDOSE':
            raise IODValidationError("rt_series_module must have modality='RTDOSE'")

        # Validate conditional module dependencies
        self._validate_conditional_dependencies(image_plane_module, image_pixel_module,
                                              multi_frame_module, general_image_module)

        # Store required modules as live references
        self._modules = {
            'patient': patient_module,
            'general_study': general_study_module,
            'rt_series': rt_series_module,
            'frame_of_reference': frame_of_reference_module,
            'general_equipment': general_equipment_module,
            'rt_dose': rt_dose_module,
            'sop_common': sop_common_module
        }

        # Add optional and conditional modules if provided
        optional_modules = {
            'general_image': general_image_module,
            'image_plane': image_plane_module,
            'image_pixel': image_pixel_module,
            'multi_frame': multi_frame_module,
            'rt_dvh': rt_dvh_module,
            'overlay_plane': overlay_plane_module,
            'approval': approval_module,
            'frame_extraction': frame_extraction_module
        }

        for name, module in optional_modules.items():
            if module is not None:
                self._modules[name] = module

    def _validate_dependencies(self, *args, **kwargs) -> None:
        """Validate IOD-specific module dependencies.

        This method implements the abstract method from BaseIOD.
        RT Dose IOD dependencies are validated during construction.
        
        Note:
            Dependencies are validated during __init__, so this is a no-op.
        """
        pass

    def _validate_conditional_dependencies(self, image_plane_module, image_pixel_module,
                                         multi_frame_module, general_image_module) -> None:
        """Validate RT Dose IOD conditional module dependencies.

        Per DICOM PS3.3 A.18.3, certain modules are conditionally required
        based on the presence of grid-based dose data.
        
        Args:
            image_plane_module: Image Plane Module instance or None
            image_pixel_module: Image Pixel Module instance or None  
            multi_frame_module: Multi-frame Module instance or None
            general_image_module: General Image Module instance or None
            
        Raises:
            IODValidationError: If conditional dependencies are not satisfied
        """
        # Image-related modules require general_image_module
        if image_plane_module and not general_image_module:
            raise IODValidationError("image_plane_module requires general_image_module")
        if image_pixel_module and not general_image_module:
            raise IODValidationError("image_pixel_module requires general_image_module")
        if multi_frame_module and not general_image_module:
            raise IODValidationError("multi_frame_module requires general_image_module")

        # Multi-frame module requires pixel data to be multi-frame
        if multi_frame_module and image_pixel_module:
            # NOTE: This validation would need to check the actual pixel data structure
            # For now, we assume the caller has provided appropriate data
            pass

    @property
    def has_dvh_data(self) -> bool:
        """Check if dose volume histogram data is included.

        Returns:
            bool: True if RT DVH module is present
        """
        return 'rt_dvh' in self._modules

    @property
    def has_image_representation(self) -> bool:
        """Check if dose can be displayed as images.

        Returns:
            bool: True if general image module is present for dose visualization
        """
        return 'general_image' in self._modules

    @property
    def is_3d_dose(self) -> bool:
        """Check if this contains 3D dose distribution data.

        Returns:
            bool: True if pixel data represents 3D dose array
        """
        dose_module = self._modules.get('rt_dose')
        if not dose_module:
            return False

        pixel_data = getattr(dose_module, 'PixelData', None)
        if pixel_data is None:
            return False

        # Check if we have image pixel module with dimensions
        image_pixel_module = self._modules.get('image_pixel')
        if image_pixel_module:
            rows = getattr(image_pixel_module, 'Rows', 0)
            columns = getattr(image_pixel_module, 'Columns', 0)
            frames = getattr(image_pixel_module, 'NumberOfFrames', 1)
            return frames > 1 or (rows > 0 and columns > 0)

        return False

    @property
    def is_multi_frame(self) -> bool:
        """Check if this is a multi-frame dose distribution.

        Returns:
            bool: True if multi-frame module is present
        """
        return 'multi_frame' in self._modules

    @property
    def has_spatial_information(self) -> bool:
        """Check if spatial positioning data is available.

        Returns:
            bool: True if image plane module is present for spatial calculations
        """
        return 'image_plane' in self._modules

    @property
    def has_approval_data(self) -> bool:
        """Check if dose calculation approval information is included.

        Returns:
            bool: True if approval module is present
        """
        return 'approval' in self._modules

    def get_dose_summary(self) -> Dict[str, Any]:
        """Get dose distribution summary information.

        Returns:
            Dict[str, Any]: Dictionary with key dose parameters and statistics
        """
        dose_module = self._modules.get('rt_dose')
        if not dose_module:
            return {}

        summary = {
            'dose_units': getattr(dose_module, 'DoseUnits', None),
            'dose_type': getattr(dose_module, 'DoseType', None),
            'dose_summation_type': getattr(dose_module, 'DoseSummationType', None),
            'dose_grid_scaling': getattr(dose_module, 'DoseGridScaling', None),
            'dose_comment': getattr(dose_module, 'DoseComment', None)
        }

        # Add pixel data statistics if available
        pixel_data = getattr(dose_module, 'PixelData', None)
        scaling = getattr(dose_module, 'DoseGridScaling', 1.0)

        if pixel_data is not None and scaling is not None:
            try:
                # Convert pixel data to numpy array for statistics
                import numpy as np
                if isinstance(pixel_data, bytes):
                    # Determine data type and shape from image pixel module
                    image_pixel_module = self._modules.get('image_pixel')
                    if image_pixel_module:
                        rows = getattr(image_pixel_module, 'Rows', 0)
                        columns = getattr(image_pixel_module, 'Columns', 0)
                        frames = getattr(image_pixel_module, 'NumberOfFrames', 1)
                        bits_allocated = getattr(image_pixel_module, 'BitsAllocated', 16)

                        # Determine numpy dtype based on bits allocated
                        if bits_allocated == 8:
                            dtype = np.uint8
                        elif bits_allocated == 16:
                            dtype = np.uint16
                        elif bits_allocated == 32:
                            dtype = np.uint32
                        else:
                            dtype = np.uint16  # Default fallback

                        # Reshape pixel data into appropriate array structure
                        pixel_array = np.frombuffer(pixel_data, dtype=dtype)
                        if frames > 1:
                            pixel_array = pixel_array.reshape((frames, rows, columns))
                        else:
                            pixel_array = pixel_array.reshape((rows, columns))

                        # Calculate dose statistics using scaling factor
                        dose_array = pixel_array.astype(np.float64) * scaling
                        summary.update({
                            'grid_dimensions': pixel_array.shape,
                            'max_dose': float(np.max(dose_array)) if dose_array.size > 0 else 0.0,
                            'mean_dose': float(np.mean(dose_array)) if dose_array.size > 0 else 0.0,
                            'min_dose': float(np.min(dose_array)) if dose_array.size > 0 else 0.0,
                            'dose_std': float(np.std(dose_array)) if dose_array.size > 0 else 0.0
                        })
                elif isinstance(pixel_data, np.ndarray):
                    # Handle direct numpy array input
                    dose_array = pixel_data.astype(np.float64) * scaling
                    summary.update({
                        'grid_dimensions': pixel_data.shape,
                        'max_dose': float(np.max(dose_array)) if dose_array.size > 0 else 0.0,
                        'mean_dose': float(np.mean(dose_array)) if dose_array.size > 0 else 0.0,
                        'min_dose': float(np.min(dose_array)) if dose_array.size > 0 else 0.0,
                        'dose_std': float(np.std(dose_array)) if dose_array.size > 0 else 0.0
                    })
            except Exception:
                # If pixel data processing fails, return basic info only
                pass

        return summary

    def get_spatial_information(self) -> Dict[str, Any]:
        """Get spatial positioning and orientation information.

        Returns:
            Dict[str, Any]: Dictionary with spatial parameters for dose grid positioning
        """
        spatial_info = {}

        # Frame of reference information
        frame_ref_module = self._modules.get('frame_of_reference')
        if frame_ref_module:
            spatial_info['frame_of_reference_uid'] = getattr(frame_ref_module, 'FrameOfReferenceUID', None)
            spatial_info['position_reference_indicator'] = getattr(frame_ref_module, 'PositionReferenceIndicator', None)

        # Image plane information  
        image_plane_module = self._modules.get('image_plane')
        if image_plane_module:
            spatial_info.update({
                'pixel_spacing': getattr(image_plane_module, 'PixelSpacing', None),
                'image_orientation_patient': getattr(image_plane_module, 'ImageOrientationPatient', None),
                'image_position_patient': getattr(image_plane_module, 'ImagePositionPatient', None),
                'slice_thickness': getattr(image_plane_module, 'SliceThickness', None),
                'slice_location': getattr(image_plane_module, 'SliceLocation', None)
            })

        # Image pixel information
        image_pixel_module = self._modules.get('image_pixel')
        if image_pixel_module:
            spatial_info.update({
                'rows': getattr(image_pixel_module, 'Rows', None),
                'columns': getattr(image_pixel_module, 'Columns', None),
                'number_of_frames': getattr(image_pixel_module, 'NumberOfFrames', None)
            })

        return spatial_info

    def validate(self, config: ValidationConfig | None = None) -> ValidationResult:
        """Validate this RT Dose IOD instance.
        
        Args:
            config (ValidationConfig | None): Optional validation configuration
            
        Returns:
            ValidationResult with errors and warnings
        """
        return RTDoseIODValidator.validate(self, config)
