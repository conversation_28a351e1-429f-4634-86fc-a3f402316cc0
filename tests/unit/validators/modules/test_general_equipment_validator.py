"""Tests for General Equipment Module Validator - DICOM PS3.3 C.7.5.1"""

import pytest
from pydicom import Dataset
from pyrt_dicom.validators.modules.general_equipment_validator import GeneralEquipmentValidator
from pyrt_dicom.validators.modules.base_validator import ValidationConfig
from pyrt_dicom.validators.validation_result import ValidationResult


class TestGeneralEquipmentValidator:
    """Test class for GeneralEquipmentValidator following pytest framework requirements."""
    
    def test_validate_valid_minimal_dataset_passes(self):
        """Test that dataset with minimal required elements passes validation."""
        dataset = Dataset()
        dataset.Manufacturer = "Test Manufacturer"
        
        result = GeneralEquipmentValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        assert len(result.warnings) == 0
    
    def test_validate_empty_manufacturer_passes(self):
        """Test that empty manufacturer (Type 2) passes validation."""
        dataset = Dataset()
        dataset.Manufacturer = ""  # Type 2 can be empty
        
        result = GeneralEquipmentValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        assert len(result.warnings) == 0
    
    def test_validate_missing_manufacturer_fails(self):
        """Test that missing manufacturer (Type 2) fails validation."""
        dataset = Dataset()
        # No Manufacturer attribute
        
        result = GeneralEquipmentValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 1
        assert "Manufacturer (0008,0070) is required (Type 2)" in result.errors[0]
        assert len(result.warnings) == 0
    
    def test_validate_complete_valid_dataset_passes(self):
        """Test that dataset with all valid optional elements passes validation."""
        dataset = Dataset()
        dataset.Manufacturer = "Varian Medical Systems"
        dataset.InstitutionName = "Test Hospital"
        dataset.InstitutionAddress = "123 Main St, City, State"
        dataset.StationName = "TrueBeam STx"
        dataset.InstitutionalDepartmentName = "Radiation Oncology"
        dataset.ManufacturerModelName = "TrueBeam"
        dataset.ManufacturersDeviceClassUID = ["*******.5"]
        dataset.DeviceSerialNumber = "12345"
        dataset.SoftwareVersions = ["v2.7.1", "kernel-1.5.2"]
        dataset.GantryID = "Gantry01"
        dataset.DeviceUID = "*******.*******.9"
        dataset.SpatialResolution = 0.5
        dataset.DateOfManufacture = "20200101"
        dataset.DateOfInstallation = "20210101"
        dataset.DateOfLastCalibration = "20240101"
        dataset.TimeOfLastCalibration = "120000"
        
        result = GeneralEquipmentValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        assert len(result.warnings) == 0
    
    # Pixel Padding Conditional Validation Tests
    
    def test_validate_pixel_padding_conditional_requirement_missing_fails(self):
        """Test that missing pixel padding value when required fails validation."""
        dataset = Dataset()
        dataset.Manufacturer = "Test Manufacturer"
        dataset.PixelPaddingRangeLimit = 100
        dataset.PixelData = b"test_pixel_data"
        # Missing PixelPaddingValue - should fail
        
        config = ValidationConfig(validate_conditional_requirements=True)
        result = GeneralEquipmentValidator.validate(dataset, config)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 1
        assert "Pixel Padding Value (0028,0120) is required" in result.errors[0]
        assert "Pixel Padding Range Limit (0028,0121)" in result.errors[0]
    
    def test_validate_pixel_padding_conditional_requirement_present_passes(self):
        """Test that pixel padding value when required passes validation."""
        dataset = Dataset()
        dataset.Manufacturer = "Test Manufacturer"
        dataset.PixelPaddingRangeLimit = 100
        dataset.PixelData = b"test_pixel_data"
        dataset.PixelPaddingValue = 0  # Required and present
        
        config = ValidationConfig(validate_conditional_requirements=True)
        result = GeneralEquipmentValidator.validate(dataset, config)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
    
    def test_validate_pixel_padding_not_required_without_range_limit(self):
        """Test that pixel padding value is not required without range limit."""
        dataset = Dataset()
        dataset.Manufacturer = "Test Manufacturer"
        dataset.PixelData = b"test_pixel_data"
        # No PixelPaddingRangeLimit, so PixelPaddingValue not required
        
        config = ValidationConfig(validate_conditional_requirements=True)
        result = GeneralEquipmentValidator.validate(dataset, config)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
    
    def test_validate_pixel_padding_not_required_without_pixel_data(self):
        """Test that pixel padding value is not required without pixel data."""
        dataset = Dataset()
        dataset.Manufacturer = "Test Manufacturer"
        dataset.PixelPaddingRangeLimit = 100
        # No PixelData or PixelDataProviderURL, so PixelPaddingValue not required
        
        config = ValidationConfig(validate_conditional_requirements=True)
        result = GeneralEquipmentValidator.validate(dataset, config)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
    
    # Calibration Date/Time Pairing Tests
    
    def test_validate_calibration_date_only_passes(self):
        """Test that calibration date without time passes validation."""
        dataset = Dataset()
        dataset.Manufacturer = "Test Manufacturer"
        dataset.DateOfLastCalibration = "20240101"
        # No TimeOfLastCalibration - this is valid
        
        result = GeneralEquipmentValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        assert len(result.warnings) == 0
    
    def test_validate_calibration_time_without_date_warns(self):
        """Test that calibration time without date generates warning."""
        dataset = Dataset()
        dataset.Manufacturer = "Test Manufacturer"
        dataset.TimeOfLastCalibration = "120000"
        # No DateOfLastCalibration - should warn
        
        result = GeneralEquipmentValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        assert len(result.warnings) == 1
        assert "Time of Last Calibration (0018,1201) has no meaning unless" in result.warnings[0]
        assert "Date of Last Calibration (0018,1200)" in result.warnings[0]
    
    def test_validate_calibration_paired_single_values_passes(self):
        """Test that paired single calibration date/time values pass validation."""
        dataset = Dataset()
        dataset.Manufacturer = "Test Manufacturer"
        dataset.DateOfLastCalibration = "20240101"
        dataset.TimeOfLastCalibration = "120000"
        
        result = GeneralEquipmentValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        assert len(result.warnings) == 0
    
    def test_validate_calibration_paired_multiple_values_passes(self):
        """Test that paired multiple calibration date/time values pass validation."""
        dataset = Dataset()
        dataset.Manufacturer = "Test Manufacturer"
        dataset.DateOfLastCalibration = ["20240101", "20240201"]
        dataset.TimeOfLastCalibration = ["120000", "130000"]
        
        result = GeneralEquipmentValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        assert len(result.warnings) == 0
    
    def test_validate_calibration_mismatched_count_warns(self):
        """Test that mismatched calibration date/time counts generate warning."""
        dataset = Dataset()
        dataset.Manufacturer = "Test Manufacturer"
        dataset.DateOfLastCalibration = ["20240101", "20240201"]
        dataset.TimeOfLastCalibration = ["120000", "130000", "140000"]  # Mismatched count (3 vs 2)

        result = GeneralEquipmentValidator.validate(dataset)

        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        assert len(result.warnings) == 1
        assert "Date of Last Calibration (0018,1200) has 2 values" in result.warnings[0]
        assert "Time of Last Calibration (0018,1201) has 3 values" in result.warnings[0]
        assert "should have the same number of values" in result.warnings[0]
    
    # Sequence Validation Tests
    
    def test_validate_institutional_department_sequence_valid_passes(self):
        """Test that valid institutional department type code sequence passes validation."""
        dept_item = Dataset()
        dept_item.CodeValue = "394802001"
        dept_item.CodingSchemeDesignator = "SCT"
        dept_item.CodeMeaning = "General medicine"
        
        dataset = Dataset()
        dataset.Manufacturer = "Test Manufacturer"
        dataset.InstitutionalDepartmentTypeCodeSequence = [dept_item]
        
        config = ValidationConfig(validate_sequences=True)
        result = GeneralEquipmentValidator.validate(dataset, config)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        # Should warn about single item requirement
        assert len(result.warnings) == 0  # Single item is valid
    
    def test_validate_institutional_department_sequence_multiple_items_warns(self):
        """Test that multiple items in institutional department sequence generates warning."""
        dept_item1 = Dataset()
        dept_item1.CodeValue = "394802001"
        dept_item1.CodingSchemeDesignator = "SCT"
        dept_item1.CodeMeaning = "General medicine"
        
        dept_item2 = Dataset()
        dept_item2.CodeValue = "394803002"
        dept_item2.CodingSchemeDesignator = "SCT"
        dept_item2.CodeMeaning = "Cardiology"
        
        dataset = Dataset()
        dataset.Manufacturer = "Test Manufacturer"
        dataset.InstitutionalDepartmentTypeCodeSequence = [dept_item1, dept_item2]
        
        config = ValidationConfig(validate_sequences=True)
        result = GeneralEquipmentValidator.validate(dataset, config)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        assert len(result.warnings) == 1
        assert "contains 2 items" in result.warnings[0]
        assert "only a single item is permitted" in result.warnings[0]
    
    def test_validate_institutional_department_sequence_missing_code_value_fails(self):
        """Test that missing code value in institutional department sequence fails validation."""
        dept_item = Dataset()
        # Missing CodeValue
        dept_item.CodingSchemeDesignator = "SCT"
        dept_item.CodeMeaning = "General medicine"
        
        dataset = Dataset()
        dataset.Manufacturer = "Test Manufacturer"
        dataset.InstitutionalDepartmentTypeCodeSequence = [dept_item]
        
        config = ValidationConfig(validate_sequences=True)
        result = GeneralEquipmentValidator.validate(dataset, config)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 1
        assert "Code Value (0008,0100) is required" in result.errors[0]
        assert "Institutional Department Type Code Sequence" in result.errors[0]
    
    def test_validate_udi_sequence_valid_passes(self):
        """Test that valid UDI sequence passes validation."""
        udi_item = Dataset()
        udi_item.UniqueDeviceIdentifier = "(01)12345678901234(11)141231(17)150707(10)A123B456(21)12345"
        
        dataset = Dataset()
        dataset.Manufacturer = "Test Manufacturer"
        dataset.UDISequence = [udi_item]
        
        config = ValidationConfig(validate_sequences=True)
        result = GeneralEquipmentValidator.validate(dataset, config)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        assert len(result.warnings) == 0
    
    def test_validate_udi_sequence_missing_identifier_fails(self):
        """Test that missing unique device identifier in UDI sequence fails validation."""
        udi_item = Dataset()
        # Missing UniqueDeviceIdentifier
        udi_item.DeviceIdentifier = "12345678901234"
        
        dataset = Dataset()
        dataset.Manufacturer = "Test Manufacturer"
        dataset.UDISequence = [udi_item]
        
        config = ValidationConfig(validate_sequences=True)
        result = GeneralEquipmentValidator.validate(dataset, config)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 1
        assert "Unique Device Identifier is required" in result.errors[0]
        assert "UDI Sequence" in result.errors[0]
    
    # ValidationConfig Tests
    
    def test_validate_with_conditional_requirements_disabled(self):
        """Test validation with conditional requirements disabled."""
        dataset = Dataset()
        dataset.Manufacturer = "Test Manufacturer"
        dataset.PixelPaddingRangeLimit = 100
        dataset.PixelData = b"test_pixel_data"
        # Missing PixelPaddingValue but conditional validation disabled
        
        config = ValidationConfig(validate_conditional_requirements=False)
        result = GeneralEquipmentValidator.validate(dataset, config)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0  # Should not validate conditional requirements
    
    def test_validate_with_sequences_disabled(self):
        """Test validation with sequence validation disabled."""
        dept_item = Dataset()
        # Missing required fields
        
        dataset = Dataset()
        dataset.Manufacturer = "Test Manufacturer"
        dataset.InstitutionalDepartmentTypeCodeSequence = [dept_item]
        
        config = ValidationConfig(validate_sequences=False)
        result = GeneralEquipmentValidator.validate(dataset, config)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0  # Should not validate sequences
    
    def test_validate_with_default_config(self):
        """Test validation with default configuration."""
        dataset = Dataset()
        dataset.Manufacturer = "Test Manufacturer"
        
        result = GeneralEquipmentValidator.validate(dataset)  # No config provided
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        assert len(result.warnings) == 0
