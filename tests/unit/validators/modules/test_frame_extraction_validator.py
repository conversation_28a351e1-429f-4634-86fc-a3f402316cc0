"""Tests for Frame Extraction Module Validator."""

import pytest
from pydicom import Dataset

from src.pyrt_dicom.validators.modules.frame_extraction_validator import FrameExtractionValidator
from src.pyrt_dicom.validators.modules.base_validator import ValidationConfig
from src.pyrt_dicom.validators.validation_result import ValidationResult


class TestFrameExtractionValidator:
    """Test class for FrameExtractionValidator following DICOM PS3.3 C.12.3 specification."""
    
    @pytest.fixture
    def valid_simple_frame_list_dataset(self):
        """Create a valid dataset with Simple Frame List."""
        dataset = Dataset()
        
        # Create sequence item with Simple Frame List
        item = Dataset()
        item.MultiFrameSourceSOPInstanceUID = "1.2.840.10008.1.2.1"
        item.SimpleFrameList = [1, 3, 5, 7, 9]
        
        dataset.FrameExtractionSequence = [item]
        return dataset
    
    @pytest.fixture
    def valid_calculated_frame_list_dataset(self):
        """Create a valid dataset with Calculated Frame List."""
        dataset = Dataset()
        
        # Create sequence item with Calculated Frame List
        item = Dataset()
        item.MultiFrameSourceSOPInstanceUID = "1.2.840.10008.1.2.1"
        item.CalculatedFrameList = [1, 10, 2, 15, 25, 3]  # Two triplets
        
        dataset.FrameExtractionSequence = [item]
        return dataset
    
    @pytest.fixture
    def valid_time_range_dataset(self):
        """Create a valid dataset with Time Range."""
        dataset = Dataset()
        
        # Create sequence item with Time Range
        item = Dataset()
        item.MultiFrameSourceSOPInstanceUID = "1.2.840.10008.1.2.1"
        item.TimeRange = [120000.0, 130000.0]  # Start and end times as float
        
        dataset.FrameExtractionSequence = [item]
        return dataset
    
    @pytest.fixture
    def validation_config(self):
        """Create default validation configuration."""
        config = ValidationConfig()
        config.validate_sequences = True
        return config
    
    # Valid Data Tests
    
    def test_valid_simple_frame_list_passes_validation(self, valid_simple_frame_list_dataset, validation_config):
        """Test that valid dataset with Simple Frame List passes validation."""
        result = FrameExtractionValidator.validate(valid_simple_frame_list_dataset, validation_config)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        assert len(result.warnings) == 0
    
    def test_valid_calculated_frame_list_passes_validation(self, valid_calculated_frame_list_dataset, validation_config):
        """Test that valid dataset with Calculated Frame List passes validation."""
        result = FrameExtractionValidator.validate(valid_calculated_frame_list_dataset, validation_config)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        assert len(result.warnings) == 0
    
    def test_valid_time_range_passes_validation(self, valid_time_range_dataset, validation_config):
        """Test that valid dataset with Time Range passes validation."""
        result = FrameExtractionValidator.validate(valid_time_range_dataset, validation_config)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        assert len(result.warnings) == 0
    
    def test_multiple_sequence_items_validation(self, validation_config):
        """Test validation of multiple sequence items."""
        dataset = Dataset()
        
        # Create multiple sequence items with different extraction methods
        item1 = Dataset()
        item1.MultiFrameSourceSOPInstanceUID = "1.2.840.10008.1.2.1"
        item1.SimpleFrameList = [1, 2, 3]
        
        item2 = Dataset()
        item2.MultiFrameSourceSOPInstanceUID = "1.2.840.10008.1.2.2"
        item2.CalculatedFrameList = [5, 15, 2]
        
        item3 = Dataset()
        item3.MultiFrameSourceSOPInstanceUID = "1.2.840.10008.1.2.3"
        item3.TimeRange = [100000.0, 200000.0]
        
        dataset.FrameExtractionSequence = [item1, item2, item3]
        
        result = FrameExtractionValidator.validate(dataset, validation_config)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        assert len(result.warnings) == 0
    
    # Type 1 Element Tests (Missing Required Elements)
    
    def test_missing_frame_extraction_sequence_error(self, validation_config):
        """Test validation error when Frame Extraction Sequence is missing."""
        dataset = Dataset()  # Empty dataset
        
        result = FrameExtractionValidator.validate(dataset, validation_config)
        
        assert len(result.errors) == 1
        assert "Missing required Frame Extraction Sequence (0008,1164)" in result.errors[0]
        assert "Type 1 element is mandatory" in result.errors[0]
    
    def test_empty_frame_extraction_sequence_error(self, validation_config):
        """Test validation error when Frame Extraction Sequence is empty."""
        dataset = Dataset()
        dataset.FrameExtractionSequence = []  # Empty sequence
        
        result = FrameExtractionValidator.validate(dataset, validation_config)
        
        assert len(result.errors) == 1
        assert "Frame Extraction Sequence (0008,1164) is empty but must contain at least one item" in result.errors[0]
    
    def test_missing_multiframe_source_sop_instance_uid_error(self, validation_config):
        """Test validation error when Multi-frame Source SOP Instance UID is missing."""
        dataset = Dataset()
        
        # Create sequence item without Multi-frame Source SOP Instance UID
        item = Dataset()
        item.SimpleFrameList = [1, 2, 3]
        # Missing MultiFrameSourceSOPInstanceUID
        
        dataset.FrameExtractionSequence = [item]
        
        result = FrameExtractionValidator.validate(dataset, validation_config)
        
        assert len(result.errors) == 1
        assert "Missing required Multi-frame Source SOP Instance UID (0008,1167)" in result.errors[0]
    
    # Conditional Validation Tests (Type 1C Logic)
    
    def test_missing_all_frame_list_methods_error(self, validation_config):
        """Test validation error when no frame list method is present."""
        dataset = Dataset()
        
        # Create sequence item with only required UID, no frame list methods
        item = Dataset()
        item.MultiFrameSourceSOPInstanceUID = "1.2.840.10008.1.2.1"
        # Missing all of: SimpleFrameList, CalculatedFrameList, TimeRange
        
        dataset.FrameExtractionSequence = [item]
        
        result = FrameExtractionValidator.validate(dataset, validation_config)
        
        assert len(result.errors) == 1
        error_msg = result.errors[0]
        assert "Missing frame extraction method. Exactly one of the following Type 1C elements must be present" in error_msg
        assert "Simple Frame List (0008,1161) for direct frame numbers" in error_msg
    
    def test_multiple_frame_list_methods_error(self, validation_config):
        """Test validation error when multiple frame list methods are present."""
        dataset = Dataset()
        
        # Create sequence item with multiple frame list methods
        item = Dataset()
        item.MultiFrameSourceSOPInstanceUID = "1.2.840.10008.1.2.1"
        item.SimpleFrameList = [1, 2, 3]
        item.CalculatedFrameList = [1, 10, 2]
        # Having both SimpleFrameList and CalculatedFrameList should cause error
        
        dataset.FrameExtractionSequence = [item]
        
        result = FrameExtractionValidator.validate(dataset, validation_config)
        
        assert len(result.errors) == 1
        error_msg = result.errors[0]
        assert "Multiple frame extraction methods found: SimpleFrameList, CalculatedFrameList" in error_msg
        assert "exactly one extraction method must be present per sequence item" in error_msg
    
    # Simple Frame List Validation Tests
    
    @pytest.mark.parametrize("invalid_frame_list,expected_error", [
        ([], "Simple Frame List (0008,1161) must contain at least one frame number"),
        ([0], "Simple Frame List (0008,1161) frame 1 must be positive"),
        ([-1, 2, 3], "Simple Frame List (0008,1161) frame 1 must be positive"),
        ([1, "invalid", 3], "Simple Frame List (0008,1161) frame 2 must be an integer"),
        ([1, None, 3], "Simple Frame List (0008,1161) frame 2 must be an integer"),
    ])
    def test_simple_frame_list_validation_errors(self, invalid_frame_list, expected_error, validation_config):
        """Test Simple Frame List validation with various invalid inputs."""
        dataset = Dataset()
        
        item = Dataset()
        item.MultiFrameSourceSOPInstanceUID = "1.2.840.10008.1.2.1"
        item.SimpleFrameList = invalid_frame_list
        
        dataset.FrameExtractionSequence = [item]
        
        result = FrameExtractionValidator.validate(dataset, validation_config)
        
        assert len(result.errors) >= 1
        assert any(expected_error in error for error in result.errors)
    
    def test_simple_frame_list_string_assignment_error(self, validation_config):
        """Test that assigning string to Simple Frame List causes appropriate error."""
        dataset = Dataset()
        
        item = Dataset()
        item.MultiFrameSourceSOPInstanceUID = "1.2.840.10008.1.2.1"
        # Directly assign string to simulate malformed data
        item.add_new((0x0008, 0x1161), 'UL', "not_a_list")
        
        dataset.FrameExtractionSequence = [item]
        
        result = FrameExtractionValidator.validate(dataset, validation_config)
        
        assert len(result.errors) >= 1
        assert any("Simple Frame List (0008,1161) must be a list of integers, not a string" in error for error in result.errors)
    
    # Calculated Frame List Validation Tests
    
    @pytest.mark.parametrize("invalid_frame_list,expected_error", [
        ([], "Calculated Frame List (0008,1162) is empty but must contain at least one triplet"),
        ([1, 2], "Calculated Frame List (0008,1162) must contain triplets (start, end, increment)"),  # Not multiple of 3
        ([1, 2, 3, 4], "Calculated Frame List (0008,1162) must contain triplets (start, end, increment)"),  # Not multiple of 3
        ([0, 5, 1], "Calculated Frame List (0008,1162) triplet 1 start must be positive"),
        ([1, 0, 1], "Calculated Frame List (0008,1162) triplet 1 end must be positive"),
        ([1, 5, 0], "Calculated Frame List (0008,1162) triplet 1 increment must be positive"),
        ([5, 3, 1], "Calculated Frame List (0008,1162) triplet 1 start must be <= end"),
        ([1, "invalid", 1], "Calculated Frame List (0008,1162) triplet 1 values must be integers"),
    ])
    def test_calculated_frame_list_validation_errors(self, invalid_frame_list, expected_error, validation_config):
        """Test Calculated Frame List validation with various invalid inputs."""
        dataset = Dataset()
        
        item = Dataset()
        item.MultiFrameSourceSOPInstanceUID = "1.2.840.10008.1.2.1"
        item.CalculatedFrameList = invalid_frame_list
        
        dataset.FrameExtractionSequence = [item]
        
        result = FrameExtractionValidator.validate(dataset, validation_config)
        
        assert len(result.errors) >= 1
        assert any(expected_error in error for error in result.errors)
    
    def test_calculated_frame_list_string_assignment_error(self, validation_config):
        """Test that assigning string to Calculated Frame List causes appropriate error."""
        dataset = Dataset()
        
        item = Dataset()
        item.MultiFrameSourceSOPInstanceUID = "1.2.840.10008.1.2.1"
        # Directly assign string to simulate malformed data
        item.add_new((0x0008, 0x1162), 'UL', "not_a_list")
        
        dataset.FrameExtractionSequence = [item]
        
        result = FrameExtractionValidator.validate(dataset, validation_config)
        
        assert len(result.errors) >= 1
        assert any("Calculated Frame List (0008,1162) must be a list" in error for error in result.errors)
    
    # Time Range Validation Tests
    
    @pytest.mark.parametrize("invalid_time_range,expected_error", [
        ([100000.0], "Time Range (0008,1163) must contain exactly 2 floating point values (start_time, end_time)"),
        ([100000.0, 200000.0, 300000.0], "Time Range (0008,1163) must contain exactly 2 floating point values (start_time, end_time)"),
        ([-1.0, 200000.0], "Time Range (0008,1163) start time must be non-negative"),
        ([100000.0, -1.0], "Time Range (0008,1163) end time must be non-negative"),
        (["invalid", 200000.0], "Time Range (0008,1163) start time must be a floating point number"),
        ([100000.0, "invalid"], "Time Range (0008,1163) end time must be a floating point number"),
        ([200000.0, 100000.0], "Time Range (0008,1163) start time must be <= end time"),
    ])
    def test_time_range_validation_errors(self, invalid_time_range, expected_error, validation_config):
        """Test Time Range validation with various invalid inputs."""
        dataset = Dataset()
        
        item = Dataset()
        item.MultiFrameSourceSOPInstanceUID = "1.2.840.10008.1.2.1"
        item.TimeRange = invalid_time_range
        
        dataset.FrameExtractionSequence = [item]
        
        result = FrameExtractionValidator.validate(dataset, validation_config)
        
        assert len(result.errors) >= 1
        assert any(expected_error in error for error in result.errors)
    
    def test_time_range_string_assignment_error(self, validation_config):
        """Test that assigning string to Time Range causes appropriate error."""
        dataset = Dataset()
        
        item = Dataset()
        item.MultiFrameSourceSOPInstanceUID = "1.2.840.10008.1.2.1"
        # Directly assign string to simulate malformed data
        item.add_new((0x0008, 0x1163), 'FD', "not_a_list")
        
        dataset.FrameExtractionSequence = [item]
        
        result = FrameExtractionValidator.validate(dataset, validation_config)
        
        assert len(result.errors) >= 1
        assert any("Time Range (0008,1163) must be a list" in error for error in result.errors)
    
    def test_empty_time_range_error(self, validation_config):
        """Test that empty Time Range causes appropriate error."""
        dataset = Dataset()
        
        item = Dataset()
        item.MultiFrameSourceSOPInstanceUID = "1.2.840.10008.1.2.1"
        item.TimeRange = []  # Empty list
        
        dataset.FrameExtractionSequence = [item]
        
        result = FrameExtractionValidator.validate(dataset, validation_config)
        
        assert len(result.errors) >= 1
        assert any("Time Range (0008,1163) must contain exactly 2 floating point values (start_time, end_time)" in error for error in result.errors)
    
    # UID Format Validation Tests
    
    @pytest.mark.parametrize("invalid_uid,expected_error", [
        ("", "Invalid Multi-frame Source SOP Instance UID (0008,1167) format"),
        ("1", "Invalid Multi-frame Source SOP Instance UID (0008,1167) format"),
        ("1.", "Invalid Multi-frame Source SOP Instance UID (0008,1167) format"),
        (".1.2", "Invalid Multi-frame Source SOP Instance UID (0008,1167) format"),
        ("1.2.", "Invalid Multi-frame Source SOP Instance UID (0008,1167) format"),
        ("1.02.3", "Invalid Multi-frame Source SOP Instance UID (0008,1167) format"),  # Leading zero
        ("1.abc.3", "Invalid Multi-frame Source SOP Instance UID (0008,1167) format"),  # Non-numeric
    ])
    def test_uid_format_validation_errors(self, invalid_uid, expected_error, validation_config):
        """Test UID format validation with various invalid inputs."""
        dataset = Dataset()
        
        item = Dataset()
        item.MultiFrameSourceSOPInstanceUID = invalid_uid
        item.SimpleFrameList = [1, 2, 3]
        
        dataset.FrameExtractionSequence = [item]
        
        result = FrameExtractionValidator.validate(dataset, validation_config)
        
        assert len(result.errors) >= 1
        assert any(expected_error in error for error in result.errors)
    
    # ValidationConfig Tests
    
    def test_sequence_validation_disabled(self):
        """Test that sequence validation can be disabled via config."""
        dataset = Dataset()
        
        # Create invalid sequence item (missing UID)
        item = Dataset()
        item.SimpleFrameList = [1, 2, 3]
        # Missing MultiFrameSourceSOPInstanceUID
        
        dataset.FrameExtractionSequence = [item]
        
        # Disable sequence validation
        config = ValidationConfig()
        config.validate_sequences = False
        
        result = FrameExtractionValidator.validate(dataset, config)
        
        # Should only check for sequence presence, not content
        assert len(result.errors) == 0
    
    def test_validation_with_none_config(self):
        """Test validation with None config uses defaults."""
        dataset = Dataset()
        
        item = Dataset()
        item.MultiFrameSourceSOPInstanceUID = "1.2.840.10008.1.2.1"
        item.SimpleFrameList = [1, 2, 3]
        
        dataset.FrameExtractionSequence = [item]
        
        result = FrameExtractionValidator.validate(dataset, None)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
    
    # ValidationResult Structure Tests
    
    def test_validation_result_structure(self, valid_simple_frame_list_dataset, validation_config):
        """Test that ValidationResult has correct structure."""
        result = FrameExtractionValidator.validate(valid_simple_frame_list_dataset, validation_config)
        
        assert isinstance(result, ValidationResult)
        assert hasattr(result, 'errors')
        assert hasattr(result, 'warnings')
        assert isinstance(result.errors, list)
        assert isinstance(result.warnings, list)
    
    def test_multiple_errors_collected(self, validation_config):
        """Test that multiple validation errors are collected."""
        dataset = Dataset()
        
        # Create sequence item with multiple errors
        item = Dataset()
        # Missing MultiFrameSourceSOPInstanceUID
        item.SimpleFrameList = [0, -1, "invalid"]  # Multiple invalid frame numbers
        
        dataset.FrameExtractionSequence = [item]
        
        result = FrameExtractionValidator.validate(dataset, validation_config)
        
        assert len(result.errors) > 1
        # Should have error for missing UID and multiple frame number errors
        assert any("Missing required Multi-frame Source SOP Instance UID" in error for error in result.errors)
        assert any("must be positive" in error for error in result.errors)
        assert any("must be an integer" in error for error in result.errors)
    
    # Error Message Quality Tests
    
    def test_error_messages_include_dicom_tags(self, validation_config):
        """Test that error messages include DICOM tag references."""
        dataset = Dataset()
        
        item = Dataset()
        # Missing MultiFrameSourceSOPInstanceUID and frame list method
        
        dataset.FrameExtractionSequence = [item]
        
        result = FrameExtractionValidator.validate(dataset, validation_config)
        
        assert len(result.errors) >= 2
        
        # Check that DICOM tags are included in error messages
        all_errors = ' '.join(result.errors)
        assert "(0008,1167)" in all_errors  # Multi-frame Source SOP Instance UID tag
        assert "(0008,1161)" in all_errors or "(0008,1162)" in all_errors or "(0008,1163)" in all_errors  # Frame list tags
    
    def test_error_messages_are_specific_and_actionable(self, validation_config):
        """Test that error messages are specific and provide actionable guidance."""
        dataset = Dataset()
        
        item = Dataset()
        item.MultiFrameSourceSOPInstanceUID = "invalid_uid"
        item.SimpleFrameList = [0, -1]  # Invalid frame numbers
        
        dataset.FrameExtractionSequence = [item]
        
        result = FrameExtractionValidator.validate(dataset, validation_config)
        
        # Check that errors are specific and actionable
        for error in result.errors:
            # Should mention what's wrong and what's expected
            assert len(error) > 20  # Reasonably detailed
            assert any(keyword in error.lower() for keyword in ['must', 'should', 'invalid', 'missing', 'required', 'positive'])
    
    # Real-World Scenario Tests
    
    def test_real_world_frame_extraction_scenario(self, validation_config):
        """Test realistic frame extraction scenario with multiple extraction items."""
        dataset = Dataset()
        
        # Simulate extracting frames from multiple source instances
        item1 = Dataset()
        item1.MultiFrameSourceSOPInstanceUID = "1.2.840.10008.*******.1.481.1.100"
        item1.SimpleFrameList = [1, 5, 10, 15, 20]  # Extract specific frames
        
        item2 = Dataset()
        item2.MultiFrameSourceSOPInstanceUID = "1.2.840.10008.*******.1.481.1.200"
        item2.CalculatedFrameList = [1, 100, 5, 200, 300, 10]  # Two triplets
        
        item3 = Dataset()
        item3.MultiFrameSourceSOPInstanceUID = "1.2.840.10008.*******.1.481.1.300"
        item3.TimeRange = [0.0, 3600.0]  # Extract frames within 1 hour time range
        
        dataset.FrameExtractionSequence = [item1, item2, item3]
        
        result = FrameExtractionValidator.validate(dataset, validation_config)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        assert len(result.warnings) == 0
    
    def test_edge_case_large_frame_numbers(self, validation_config):
        """Test edge case with large frame numbers."""
        dataset = Dataset()
        
        item = Dataset()
        item.MultiFrameSourceSOPInstanceUID = "1.2.840.10008.1.2.1"
        item.SimpleFrameList = [1, 1000, 10000, 65535]  # Large but valid frame numbers
        
        dataset.FrameExtractionSequence = [item]
        
        result = FrameExtractionValidator.validate(dataset, validation_config)
        
        assert len(result.errors) == 0
    
    def test_edge_case_floating_point_precision(self, validation_config):
        """Test edge case with high precision floating point times."""
        dataset = Dataset()
        
        item = Dataset()
        item.MultiFrameSourceSOPInstanceUID = "1.2.840.10008.1.2.1"
        item.TimeRange = [123456.789123, 654321.987654]  # High precision
        
        dataset.FrameExtractionSequence = [item]
        
        result = FrameExtractionValidator.validate(dataset, validation_config)
        
        assert len(result.errors) == 0