"""Tests for General Study Module Validator - DICOM PS3.3 C.7.2.1

Comprehensive test suite for GeneralStudyValidator ensuring correct validation
of all Type 1, Type 2, Type 3, Type 1C, and Type 2C requirements according
to the DICOM standard.
"""

import pytest
from pydicom import Dataset
from pyrt_dicom.validators.modules.general_study_validator import GeneralStudyValidator
from pyrt_dicom.validators.modules.base_validator import ValidationConfig
from pyrt_dicom.validators.validation_result import ValidationResult


class TestGeneralStudyValidator:
    """Test class for GeneralStudyValidator following pytest framework requirements."""
    
    def test_validate_valid_minimal_dataset_passes(self):
        """Test that dataset with minimal required elements passes validation."""
        dataset = Dataset()
        # Type 1 requirement
        dataset.StudyInstanceUID = "*******.*******.9"
        # Type 2 requirements (can be empty)
        dataset.StudyDate = ""
        dataset.StudyTime = ""
        dataset.ReferringPhysicianName = ""
        dataset.StudyID = ""
        dataset.AccessionNumber = ""
        
        result = GeneralStudyValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        assert len(result.warnings) == 0
    
    def test_validate_valid_complete_dataset_passes(self):
        """Test that dataset with all elements passes validation."""
        dataset = Dataset()
        # Type 1 requirement
        dataset.StudyInstanceUID = "*******.*******.9"
        # Type 2 requirements
        dataset.StudyDate = "20240101"
        dataset.StudyTime = "120000"
        dataset.ReferringPhysicianName = "Smith^John"
        dataset.StudyID = "STUDY001"
        dataset.AccessionNumber = "ACC123456"
        # Type 3 optional elements
        dataset.StudyDescription = "Chest CT with contrast"
        dataset.PhysiciansOfRecord = "Jones^Mary"
        
        result = GeneralStudyValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        assert len(result.warnings) == 0
    
    def test_validate_missing_study_instance_uid_errors(self):
        """Test that missing Study Instance UID (Type 1) generates error."""
        dataset = Dataset()
        # Missing StudyInstanceUID (Type 1)
        dataset.StudyDate = ""
        dataset.StudyTime = ""
        dataset.ReferringPhysicianName = ""
        dataset.StudyID = ""
        dataset.AccessionNumber = ""
        
        result = GeneralStudyValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 1
        assert "Study Instance UID (0020,000D) is required and must be non-empty (Type 1)" in result.errors[0]
        assert "Provide a unique identifier for the Study" in result.errors[0]
    
    def test_validate_empty_study_instance_uid_errors(self):
        """Test that empty Study Instance UID (Type 1) generates error."""
        dataset = Dataset()
        dataset.StudyInstanceUID = ""  # Type 1 cannot be empty
        dataset.StudyDate = ""
        dataset.StudyTime = ""
        dataset.ReferringPhysicianName = ""
        dataset.StudyID = ""
        dataset.AccessionNumber = ""
        
        result = GeneralStudyValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 1
        assert "Study Instance UID (0020,000D) is required and must be non-empty (Type 1)" in result.errors[0]
    
    def test_validate_missing_type2_elements_errors(self):
        """Test that missing Type 2 elements generate errors."""
        dataset = Dataset()
        dataset.StudyInstanceUID = "*******.*******.9"
        # Missing all Type 2 elements
        
        result = GeneralStudyValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 5  # 5 Type 2 elements missing
        
        error_messages = " ".join(result.errors)
        assert "Study Date (0008,0020) is required (Type 2)" in error_messages
        assert "Study Time (0008,0030) is required (Type 2)" in error_messages
        assert "Referring Physician's Name (0008,0090) is required (Type 2)" in error_messages
        assert "Study ID (0020,0010) is required (Type 2)" in error_messages
        assert "Accession Number (0008,0050) is required (Type 2)" in error_messages
    
    def test_validate_empty_type2_elements_passes(self):
        """Test that empty Type 2 elements pass validation."""
        dataset = Dataset()
        dataset.StudyInstanceUID = "*******.*******.9"
        # Type 2 elements can be empty
        dataset.StudyDate = ""
        dataset.StudyTime = ""
        dataset.ReferringPhysicianName = ""
        dataset.StudyID = ""
        dataset.AccessionNumber = ""
        
        result = GeneralStudyValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
    
    def test_validate_sequence_single_item_constraints(self):
        """Test validation of sequences that must have only single items."""
        dataset = Dataset()
        dataset.StudyInstanceUID = "*******.*******.9"
        dataset.StudyDate = ""
        dataset.StudyTime = ""
        dataset.ReferringPhysicianName = ""
        dataset.StudyID = ""
        dataset.AccessionNumber = ""

        # Add sequences with multiple items (should error)
        item1 = Dataset()
        item2 = Dataset()

        dataset.ReferringPhysicianIdentificationSequence = [item1, item2]  # Should be single item
        dataset.IssuerOfAccessionNumberSequence = [item1, item2]  # Should be single item
        dataset.RequestingServiceCodeSequence = [item1, item2]  # Should be single item

        result = GeneralStudyValidator.validate(dataset)

        assert isinstance(result, ValidationResult)
        # Should have errors for sequence count constraints plus content validation errors
        assert len(result.errors) > 3  # At least 3 sequence count errors plus content errors

        error_messages = " ".join(result.errors)
        # Check for sequence count constraint errors
        assert "Referring Physician Identification Sequence (0008,0096)" in error_messages
        assert "only a single Item is permitted" in error_messages
        assert "Issuer of Accession Number Sequence (0008,0051)" in error_messages
        assert "Requesting Service Code Sequence (0032,1034)" in error_messages

        # Count specific sequence constraint errors
        sequence_count_errors = [error for error in result.errors if "only a single Item is permitted" in error]
        assert len(sequence_count_errors) == 3
    
    def test_validate_physician_correspondence_warnings(self):
        """Test validation of physician name/ID sequence correspondence."""
        dataset = Dataset()
        dataset.StudyInstanceUID = "*******.*******.9"
        dataset.StudyDate = ""
        dataset.StudyTime = ""
        dataset.ReferringPhysicianName = ""
        dataset.StudyID = ""
        dataset.AccessionNumber = ""
        
        # Add mismatched physician names and sequences
        dataset.ConsultingPhysicianName = ["Smith^John", "Jones^Mary"]  # 2 names
        dataset.ConsultingPhysicianIdentificationSequence = [Dataset()]  # 1 sequence item
        
        dataset.PhysiciansOfRecord = ["Brown^Bob"]  # 1 name
        dataset.PhysiciansOfRecordIdentificationSequence = [Dataset(), Dataset()]  # 2 sequence items
        
        result = GeneralStudyValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert len(result.warnings) == 2
        
        warning_messages = " ".join(result.warnings)
        assert "Consulting Physician Identification Sequence" in warning_messages
        assert "number and order should correspond" in warning_messages
        assert "Physician(s) of Record Identification Sequence" in warning_messages
    
    def test_validate_person_identification_macro_requirements(self):
        """Test validation of Person Identification Macro (Table 10-1) requirements."""
        dataset = Dataset()
        dataset.StudyInstanceUID = "*******.*******.9"
        dataset.StudyDate = ""
        dataset.StudyTime = ""
        dataset.ReferringPhysicianName = ""
        dataset.StudyID = ""
        dataset.AccessionNumber = ""
        
        # Add physician identification sequence with invalid items
        invalid_item = Dataset()
        # Missing required Person Identification Code Sequence (Type 1)
        # Missing required Institution Name or Institution Code Sequence (Type 1C)
        
        dataset.ReferringPhysicianIdentificationSequence = [invalid_item]
        
        result = GeneralStudyValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 2
        
        error_messages = " ".join(result.errors)
        assert "Person Identification Code Sequence (0040,1101) is required (Type 1)" in error_messages
        assert "Either Institution Name (0008,0080) or Institution Code Sequence (0008,0082)" in error_messages
        assert "Type 1C requirement" in error_messages
    
    def test_validate_code_sequence_macro_requirements(self):
        """Test validation of Code Sequence Macro (Table 8.8-1) requirements."""
        dataset = Dataset()
        dataset.StudyInstanceUID = "*******.*******.9"
        dataset.StudyDate = ""
        dataset.StudyTime = ""
        dataset.ReferringPhysicianName = ""
        dataset.StudyID = ""
        dataset.AccessionNumber = ""
        
        # Add code sequence with invalid items
        invalid_code_item = Dataset()
        # Missing required Code Meaning (Type 1)
        # Missing required code values (Type 1C)
        
        dataset.ProcedureCodeSequence = [invalid_code_item]
        
        result = GeneralStudyValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 2
        
        error_messages = " ".join(result.errors)
        assert "Code Meaning (0008,0104) is required and must be non-empty (Type 1)" in error_messages
        assert "At least one of Code Value (0008,0100), Long Code Value (0008,0119)" in error_messages
        assert "Type 1C requirement" in error_messages
    
    def test_validate_with_validation_config(self):
        """Test validation with custom ValidationConfig."""
        dataset = Dataset()
        dataset.StudyInstanceUID = "*******.*******.9"
        dataset.StudyDate = ""
        dataset.StudyTime = ""
        dataset.ReferringPhysicianName = ""
        dataset.StudyID = ""
        dataset.AccessionNumber = ""
        
        # Add sequence that would normally be validated
        invalid_item = Dataset()
        dataset.ReferringPhysicianIdentificationSequence = [invalid_item]
        
        # Disable sequence validation
        config = ValidationConfig()
        config.validate_sequences = False
        
        result = GeneralStudyValidator.validate(dataset, config)
        
        assert isinstance(result, ValidationResult)
        # Should have no sequence-related errors when sequence validation is disabled
        assert len(result.errors) == 0
    
    def test_validate_returns_validation_result_type(self):
        """Test that validate method returns correct ValidationResult type."""
        dataset = Dataset()
        dataset.StudyInstanceUID = "*******.*******.9"
        dataset.StudyDate = ""
        dataset.StudyTime = ""
        dataset.ReferringPhysicianName = ""
        dataset.StudyID = ""
        dataset.AccessionNumber = ""
        
        result = GeneralStudyValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert hasattr(result, 'errors')
        assert hasattr(result, 'warnings')
        assert isinstance(result.errors, list)
        assert isinstance(result.warnings, list)
