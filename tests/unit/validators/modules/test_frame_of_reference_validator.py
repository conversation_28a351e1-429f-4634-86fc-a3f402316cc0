"""Test FrameOfReferenceValidator - DICOM PS3.3 C.7.4.1"""

from pydicom import Dataset

from src.pyrt_dicom.validators.modules.frame_of_reference_validator import FrameOfReferenceValidator
from src.pyrt_dicom.validators.modules.base_validator import ValidationConfig
from src.pyrt_dicom.validators.validation_result import ValidationResult
from src.pyrt_dicom.modules.modules.frame_of_reference_module import FrameOfReferenceModule


class TestFrameOfReferenceValidator:
    """Test FrameOfReferenceValidator validation logic."""

    def test_validate_empty_dataset_fails(self):
        """Test that an empty dataset fails validation (missing required Type 1 elements)."""
        dataset = Dataset()
        result = FrameOfReferenceValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 2  # Missing Frame of Reference UID and Position Reference Indicator
        assert len(result.warnings) == 0
        
        # Check specific error messages
        error_messages = ' '.join(result.errors)
        assert "Frame of Reference UID (0020,0052) is required (Type 1)" in error_messages
        assert "Position Reference Indicator (0020,1040) is required (Type 2)" in error_messages

    def test_validate_valid_module_passes(self):
        """Test that a valid FrameOfReferenceModule passes validation."""
        module = FrameOfReferenceModule.from_required_elements(
            frame_of_reference_uid="1.2.840.10008.*******.*******.9",
            position_reference_indicator="STERNAL_NOTCH"
        )

        # Use the module's dataset for validation
        dataset = module.to_dataset()
        result = FrameOfReferenceValidator.validate(dataset)

        assert len(result.errors) == 0
        assert len(result.warnings) == 1  # Should have warning for patient-based system

    def test_validate_valid_module_with_empty_position_reference(self):
        """Test that a module with empty Position Reference Indicator passes validation."""
        module = FrameOfReferenceModule.from_required_elements(
            frame_of_reference_uid="1.2.840.10008.*******.*******.9",
            position_reference_indicator=""  # Empty is allowed for Type 2
        )

        # Use the module's dataset for validation
        dataset = module.to_dataset()
        result = FrameOfReferenceValidator.validate(dataset)

        assert len(result.errors) == 0
        assert len(result.warnings) == 0

    def test_validate_missing_frame_of_reference_uid_fails(self):
        """Test validation fails when Frame of Reference UID is missing."""
        dataset = Dataset()
        dataset.PositionReferenceIndicator = "STERNAL_NOTCH"
        # Missing FrameOfReferenceUID
        
        result = FrameOfReferenceValidator.validate(dataset)
        
        assert len(result.errors) == 1
        assert "Frame of Reference UID (0020,0052) is required (Type 1)" in result.errors[0]

    def test_validate_empty_frame_of_reference_uid_fails(self):
        """Test validation fails when Frame of Reference UID is empty string."""
        dataset = Dataset()
        dataset.FrameOfReferenceUID = ""  # Empty string
        dataset.PositionReferenceIndicator = "STERNAL_NOTCH"
        
        result = FrameOfReferenceValidator.validate(dataset)
        
        assert len(result.errors) >= 1
        error_messages = ' '.join(result.errors)
        assert "Frame of Reference UID (0020,0052) must be a non-empty string" in error_messages

    def test_validate_whitespace_frame_of_reference_uid_fails(self):
        """Test validation fails when Frame of Reference UID is only whitespace."""
        dataset = Dataset()
        dataset.FrameOfReferenceUID = "   "  # Only whitespace
        dataset.PositionReferenceIndicator = "STERNAL_NOTCH"
        
        result = FrameOfReferenceValidator.validate(dataset)
        
        assert len(result.errors) >= 1
        error_messages = ' '.join(result.errors)
        assert "Frame of Reference UID (0020,0052) must be a non-empty string" in error_messages

    def test_validate_missing_position_reference_indicator_fails(self):
        """Test validation fails when Position Reference Indicator is missing."""
        dataset = Dataset()
        dataset.FrameOfReferenceUID = "1.2.840.10008.*******.*******.9"
        # Missing PositionReferenceIndicator
        
        result = FrameOfReferenceValidator.validate(dataset)
        
        assert len(result.errors) == 1
        assert "Position Reference Indicator (0020,1040) is required (Type 2)" in result.errors[0]

    # UID Format Validation Tests

    def test_validate_valid_uid_format_passes(self):
        """Test validation passes for valid UID formats."""
        valid_uids = [
            "*******.*******.9",
            "1.2.840.10008.*******.*******.9.10.11.12.13.14.15",
            "2.25.123456789012345678901234567890123456789",  # UUID-based UID
            "1.2.3",  # Minimal valid UID
        ]
        
        for uid in valid_uids:
            dataset = Dataset()
            dataset.FrameOfReferenceUID = uid
            dataset.PositionReferenceIndicator = "STERNAL_NOTCH"
            
            result = FrameOfReferenceValidator.validate(dataset)
            
            assert len(result.errors) == 0, f"Valid UID '{uid}' should not produce errors"

    def test_validate_uid_format_too_long_fails(self):
        """Test validation fails when UID exceeds 64 characters."""
        long_uid = "*******.*******.9.10.11.12.13.14.15.16.17.18.19.20.21.22.23.24.25"  # > 64 chars
        dataset = Dataset()
        dataset.FrameOfReferenceUID = long_uid
        dataset.PositionReferenceIndicator = "STERNAL_NOTCH"
        
        result = FrameOfReferenceValidator.validate(dataset)
        
        assert len(result.errors) == 1
        assert f"exceeds maximum length of 64 characters (length: {len(long_uid)})" in result.errors[0]

    def test_validate_uid_format_invalid_characters_fails(self):
        """Test validation fails when UID contains invalid characters."""
        invalid_uids = [
            "*******.*******.9a",    # Contains letter
            "*******.*******.9-",    # Contains hyphen
            "*******.*******.9_",    # Contains underscore
            "*******.*******.9!",    # Contains special character
        ]
        
        for uid in invalid_uids:
            dataset = Dataset()
            dataset.FrameOfReferenceUID = uid
            dataset.PositionReferenceIndicator = "STERNAL_NOTCH"
            
            result = FrameOfReferenceValidator.validate(dataset)
            
            assert len(result.errors) >= 1, f"UID '{uid}' should produce errors but got: {result.errors}"
            error_messages = ' '.join(result.errors)
            assert "contains invalid characters" in error_messages, f"Expected 'contains invalid characters' in: {error_messages}"
            assert "may only contain digits (0-9) and periods (.)" in error_messages
        
        # Test space separately as PyDicom may strip it
        dataset_space = Dataset()
        dataset_space.FrameOfReferenceUID = "*******.*******.9 space"  # Space in middle
        dataset_space.PositionReferenceIndicator = "STERNAL_NOTCH"
        
        result_space = FrameOfReferenceValidator.validate(dataset_space)
        if len(result_space.errors) > 0:  # Only assert if PyDicom preserved the space
            error_messages = ' '.join(result_space.errors)
            assert "contains invalid characters" in error_messages

    def test_validate_uid_format_starts_with_period_fails(self):
        """Test validation fails when UID starts with a period."""
        dataset = Dataset()
        dataset.FrameOfReferenceUID = ".*******.*******.9"
        dataset.PositionReferenceIndicator = "STERNAL_NOTCH"
        
        result = FrameOfReferenceValidator.validate(dataset)
        
        assert len(result.errors) >= 1
        error_messages = ' '.join(result.errors)
        assert "cannot start or end with a period" in error_messages

    def test_validate_uid_format_ends_with_period_fails(self):
        """Test validation fails when UID ends with a period."""
        dataset = Dataset()
        dataset.FrameOfReferenceUID = "*******.*******.9."
        dataset.PositionReferenceIndicator = "STERNAL_NOTCH"
        
        result = FrameOfReferenceValidator.validate(dataset)
        
        assert len(result.errors) >= 1
        error_messages = ' '.join(result.errors)
        assert "cannot start or end with a period" in error_messages

    def test_validate_uid_format_consecutive_periods_fails(self):
        """Test validation fails when UID contains consecutive periods."""
        dataset = Dataset()
        dataset.FrameOfReferenceUID = "1.2..3.4.*******.9"
        dataset.PositionReferenceIndicator = "STERNAL_NOTCH"
        
        result = FrameOfReferenceValidator.validate(dataset)
        
        assert len(result.errors) >= 1
        error_messages = ' '.join(result.errors)
        assert "cannot contain consecutive periods" in error_messages

    def test_validate_uid_format_leading_zero_fails(self):
        """Test validation fails when UID components have leading zeros."""
        invalid_uids = [
            "0*******.*******.9",     # Leading zero in first component
            "1.02.3.4.*******.9",     # Leading zero in middle component
            "*******.*******.09",     # Leading zero in last component
        ]
        
        for uid in invalid_uids:
            dataset = Dataset()
            dataset.FrameOfReferenceUID = uid
            dataset.PositionReferenceIndicator = "STERNAL_NOTCH"
            
            result = FrameOfReferenceValidator.validate(dataset)
            
            assert len(result.errors) == 1
            assert "has leading zero" in result.errors[0]

    def test_validate_uid_format_zero_component_valid(self):
        """Test validation passes when UID has single '0' components (not leading zeros)."""
        dataset = Dataset()
        dataset.FrameOfReferenceUID = "1.0.3.4.*******.0"  # Single zeros are valid
        dataset.PositionReferenceIndicator = "STERNAL_NOTCH"
        
        result = FrameOfReferenceValidator.validate(dataset)
        
        assert len(result.errors) == 0

    # Position Reference Indicator Validation Tests

    def test_validate_anatomical_reference_indicators_valid(self):
        """Test validation passes for known anatomical reference indicators."""
        anatomical_indicators = [
            "ILIAC_CREST",
            "ORBITAL_MEDIAL",
            "STERNAL_NOTCH",
            "SYMPHYSIS_PUBIS",
            "XIPHOID",
            "LOWER_COSTAL_MARGIN",
            "EXTERNAL_AUDITORY_MEATUS"
        ]

        for indicator in anatomical_indicators:
            dataset = Dataset()
            dataset.FrameOfReferenceUID = "*******.*******.9"
            dataset.PositionReferenceIndicator = indicator

            result = FrameOfReferenceValidator.validate(dataset)

            assert len(result.errors) == 0, f"Anatomical indicator '{indicator}' should be valid"
            assert len(result.warnings) == 1, f"Anatomical indicator '{indicator}' should generate patient-based warning"
            assert "patient-based coordinate system" in result.warnings[0]

    def test_validate_slide_reference_indicator_valid_with_warning(self):
        """Test validation passes for slide reference indicator but generates contextual warning."""
        dataset = Dataset()
        dataset.FrameOfReferenceUID = "*******.*******.9"
        dataset.PositionReferenceIndicator = "SLIDE_CORNER"
        
        result = FrameOfReferenceValidator.validate(dataset)
        
        assert len(result.errors) == 0
        assert len(result.warnings) == 1
        assert "indicates a slide-based coordinate system" in result.warnings[0]
        assert "Ensure this is appropriate for the imaging context" in result.warnings[0]

    def test_validate_corneal_reference_indicators_valid_with_warning(self):
        """Test validation passes for corneal reference indicators but generates contextual warnings."""
        corneal_indicators = ["CORNEAL_VERTEX_R", "CORNEAL_VERTEX_L"]
        
        for indicator in corneal_indicators:
            dataset = Dataset()
            dataset.FrameOfReferenceUID = "*******.*******.9"
            dataset.PositionReferenceIndicator = indicator
            
            result = FrameOfReferenceValidator.validate(dataset)
            
            assert len(result.errors) == 0
            assert len(result.warnings) == 1
            assert f"Position Reference Indicator '{indicator}'" in result.warnings[0]
            assert "indicates a corneal coordinate system" in result.warnings[0]
            assert "Ensure this is appropriate for the imaging context" in result.warnings[0]

    def test_validate_unknown_position_reference_indicator_warns(self):
        """Test validation warns for unknown position reference indicators."""
        unknown_indicators = [
            "UNKNOWN_ANATOMICAL_POINT",
            "CUSTOM_REFERENCE",
            "NOT_STANDARD_VALUE"
        ]
        
        for indicator in unknown_indicators:
            dataset = Dataset()
            dataset.FrameOfReferenceUID = "*******.*******.9"
            dataset.PositionReferenceIndicator = indicator
            
            result = FrameOfReferenceValidator.validate(dataset)
            
            assert len(result.errors) == 0  # Should not be an error, just a warning
            assert len(result.warnings) == 1
            assert f"value '{indicator}' is not a standard DICOM value" in result.warnings[0]
            assert "Standard values per PS3.3 C.*******.2 include:" in result.warnings[0]
            
            # Check that all known values are listed
            warning_message = result.warnings[0]
            for known in ["ILIAC_CREST", "SLIDE_CORNER", "CORNEAL_VERTEX_R", "CORNEAL_VERTEX_L"]:
                assert known in warning_message

    def test_validate_empty_position_reference_indicator_passes(self):
        """Test validation passes for empty Position Reference Indicator (Type 2)."""
        dataset = Dataset()
        dataset.FrameOfReferenceUID = "*******.*******.9"
        dataset.PositionReferenceIndicator = ""  # Empty is valid for Type 2
        
        result = FrameOfReferenceValidator.validate(dataset)
        
        assert len(result.errors) == 0
        assert len(result.warnings) == 0

    def test_validate_whitespace_position_reference_indicator_passes(self):
        """Test validation passes for whitespace-only Position Reference Indicator."""
        dataset = Dataset()
        dataset.FrameOfReferenceUID = "*******.*******.9"
        dataset.PositionReferenceIndicator = "   "  # Only whitespace (treated as empty)
        
        result = FrameOfReferenceValidator.validate(dataset)
        
        assert len(result.errors) == 0
        assert len(result.warnings) == 0

    # Module Integration Tests

    def test_validate_patient_based_module_example(self):
        """Test validation of patient-based frame of reference example."""
        module = FrameOfReferenceModule.from_required_elements(
            frame_of_reference_uid="1.2.840.10008.*******.*******.9.10",
            position_reference_indicator="STERNAL_NOTCH"
        )

        # Use the module's dataset for validation
        dataset = module.to_dataset()
        result = FrameOfReferenceValidator.validate(dataset)

        assert len(result.errors) == 0
        assert len(result.warnings) == 1  # Should have warning for patient-based system
        assert "patient-based coordinate system" in result.warnings[0]
        assert module.is_patient_based
        assert module.reference_type == "PATIENT"

    def test_validate_slide_based_module_example(self):
        """Test validation of slide-based frame of reference example."""
        module = FrameOfReferenceModule.from_required_elements(
            frame_of_reference_uid="1.2.840.10008.2.3.4.*******.9.10.11",
            position_reference_indicator="SLIDE_CORNER"
        )

        # Use the module's dataset for validation
        dataset = module.to_dataset()
        result = FrameOfReferenceValidator.validate(dataset)

        assert len(result.errors) == 0
        assert len(result.warnings) == 1  # Contextual warning for slide-based
        assert "slide-based coordinate system" in result.warnings[0]
        assert module.is_slide_based
        assert module.reference_type == "SLIDE"

    def test_validate_corneal_based_module_example(self):
        """Test validation of corneal coordinate system example."""
        for eye_side, indicator in [("RIGHT", "CORNEAL_VERTEX_R"), ("LEFT", "CORNEAL_VERTEX_L")]:
            # Use numeric ID instead of text in UID to avoid invalid UID format
            eye_id = "101" if eye_side == "RIGHT" else "102"
            module = FrameOfReferenceModule.from_required_elements(
                frame_of_reference_uid=f"1.2.840.10008.3.4.*******.9.10.11.{eye_id}",
                position_reference_indicator=indicator
            )

            # Use the module's dataset for validation
            dataset = module.to_dataset()
            result = FrameOfReferenceValidator.validate(dataset)

            assert len(result.errors) == 0
            assert len(result.warnings) == 1  # Contextual warning for corneal
            assert "corneal coordinate system" in result.warnings[0]
            assert module.is_corneal_based
            assert module.reference_type == "CORNEAL"
            assert module.corneal_eye_side == eye_side

    def test_validate_mammography_module_example(self):
        """Test validation of mammography example with empty position reference."""
        module = FrameOfReferenceModule.from_required_elements(
            frame_of_reference_uid="1.2.840.10008.4.*******.9.10.11.12.13",
            position_reference_indicator=""  # Empty for mammographic images
        )

        # Use the module's dataset for validation
        dataset = module.to_dataset()
        result = FrameOfReferenceValidator.validate(dataset)

        assert len(result.errors) == 0
        assert len(result.warnings) == 0
        assert not module.has_meaningful_reference
        assert module.reference_type == "EMPTY"

    # Error Message Quality Tests

    def test_error_messages_contain_dicom_tags(self):
        """Test that error messages contain proper DICOM tag references."""
        dataset = Dataset()
        # Create various validation errors
        dataset.FrameOfReferenceUID = ""  # Empty UID
        # Missing Position Reference Indicator
        
        result = FrameOfReferenceValidator.validate(dataset)
        
        # Check that all error messages contain DICOM tag references
        for error in result.errors:
            assert any(tag in error for tag in ["(0020,0052)", "(0020,1040)"]), f"Error missing DICOM tag: {error}"

    def test_error_messages_are_descriptive(self):
        """Test that error messages provide helpful context and guidance."""
        dataset = Dataset()
        dataset.FrameOfReferenceUID = "invalid..uid..format"
        dataset.PositionReferenceIndicator = "UNKNOWN_VALUE"
        
        result = FrameOfReferenceValidator.validate(dataset)
        
        # Check that error messages are descriptive
        error_messages = ' '.join(result.errors)
        assert "cannot contain consecutive periods" in error_messages or "contains invalid characters" in error_messages
        
        # Check warning messages are helpful
        warning_messages = ' '.join(result.warnings)
        assert "is not a standard DICOM value" in warning_messages
        assert "Standard values per PS3.3 C.*******.2 include:" in warning_messages

    # ValidationResult Structure Tests

    def test_validation_result_structure(self):
        """Test that ValidationResult is properly structured."""
        dataset = Dataset()
        result = FrameOfReferenceValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert hasattr(result, 'errors')
        assert hasattr(result, 'warnings')
        assert isinstance(result.errors, list)
        assert isinstance(result.warnings, list)

    def test_validation_result_with_configuration(self):
        """Test validation with different configuration options."""
        dataset = Dataset()
        dataset.FrameOfReferenceUID = "*******.*******.9"
        dataset.PositionReferenceIndicator = "UNKNOWN_VALUE"

        # Test with default configuration (check_enumerated_values=True)
        config1 = ValidationConfig()
        result1 = FrameOfReferenceValidator.validate(dataset, config1)
        assert len(result1.errors) == 0
        assert len(result1.warnings) == 1  # Should warn about unknown value

        # Test with enumerated value checking disabled
        config2 = ValidationConfig(check_enumerated_values=False)
        result2 = FrameOfReferenceValidator.validate(dataset, config2)
        assert len(result2.errors) == 0
        assert len(result2.warnings) == 0  # Should not warn when checking disabled

        # Test with None configuration
        result3 = FrameOfReferenceValidator.validate(dataset, None)
        assert len(result3.errors) == 0
        assert len(result3.warnings) == 1  # Default behavior should warn
        assert isinstance(result3, ValidationResult)

    # Comprehensive Integration Tests

    def test_validate_all_known_anatomical_indicators_comprehensive(self):
        """Test comprehensive validation of all anatomical indicators from module."""
        anatomical_indicators = FrameOfReferenceModule.create_anatomical_reference_indicators()

        for indicator_key, description in anatomical_indicators.items():
            module = FrameOfReferenceModule.from_required_elements(
                frame_of_reference_uid=f"1.2.840.10008.1.2.3.{hash(indicator_key) % 1000}",
                position_reference_indicator=indicator_key
            )

            # Use the module's dataset for validation
            dataset = module.to_dataset()
            result = FrameOfReferenceValidator.validate(dataset)

            assert len(result.errors) == 0, f"Anatomical indicator '{indicator_key}' should be valid"
            assert len(result.warnings) == 1, f"Anatomical indicator '{indicator_key}' should warn about patient-based system"
            assert "patient-based coordinate system" in result.warnings[0]
            assert module.is_patient_based

    def test_validate_special_indicators_comprehensive(self):
        """Test comprehensive validation of all special indicators."""
        # Test slide indicator
        slide_module = FrameOfReferenceModule.from_required_elements(
            frame_of_reference_uid="1.2.840.10008.2.3.4.*******.9.10",
            position_reference_indicator=FrameOfReferenceModule.create_slide_reference_indicator()
        )

        # Use the module's dataset for validation
        dataset = slide_module.to_dataset()
        result = FrameOfReferenceValidator.validate(dataset)
        assert len(result.errors) == 0
        assert len(result.warnings) == 1  # Contextual warning
        assert slide_module.is_slide_based

        # Test corneal indicators
        corneal_indicators = FrameOfReferenceModule.create_corneal_reference_indicators()
        for indicator_key, description in corneal_indicators.items():
            corneal_module = FrameOfReferenceModule.from_required_elements(
                frame_of_reference_uid=f"1.2.840.10008.3.4.5.{hash(indicator_key) % 1000}",
                position_reference_indicator=indicator_key
            )

            # Use the module's dataset for validation
            dataset = corneal_module.to_dataset()
            result = FrameOfReferenceValidator.validate(dataset)
            assert len(result.errors) == 0
            assert len(result.warnings) == 1  # Contextual warning
            assert corneal_module.is_corneal_based

    def test_validate_complex_uid_formats(self):
        """Test validation of complex but valid UID formats."""
        complex_uids = [
            # UUID-derived UIDs (2.25 prefix)
            "2.25.123456789012345678901234567890123456789",
            "2.25.329800735698586629295641978511506172918",
            
            # Long but valid UIDs (near 64 character limit)
            "1.2.840.10008.*******.*******.9.10.11.12.13.14.15.16.17.18.19",
            
            # Minimal valid UIDs
            "1.2",
            "0.0",
            
            # Real-world example UIDs
            "1.2.840.10008.5.1.4.1.1.481.3",  # RT Dose Storage
            "1.2.276.0.7230010.3.1.4.8323329.5340.1495927169.335056"  # Implementation-specific
        ]
        
        for uid in complex_uids:
            dataset = Dataset()
            dataset.FrameOfReferenceUID = uid
            dataset.PositionReferenceIndicator = "STERNAL_NOTCH"
            
            result = FrameOfReferenceValidator.validate(dataset)
            
            assert len(result.errors) == 0, f"Complex UID '{uid}' should be valid"

    def test_validate_dicom_standard_conformance(self):
        """Test validation against DICOM PS3.3 C.7.4.1 requirements."""
        # Test that Frame of Reference Module meets all DICOM standard requirements
        
        # 1. Frame of Reference UID is Type 1 (required)
        dataset_missing_uid = Dataset()
        dataset_missing_uid.PositionReferenceIndicator = "STERNAL_NOTCH"
        result = FrameOfReferenceValidator.validate(dataset_missing_uid)
        assert len(result.errors) == 1
        assert "Type 1" in result.errors[0]
        
        # 2. Position Reference Indicator is Type 2 (required but may be empty)
        dataset_missing_pri = Dataset()
        dataset_missing_pri.FrameOfReferenceUID = "*******.*******.9"
        result = FrameOfReferenceValidator.validate(dataset_missing_pri)
        assert len(result.errors) == 1
        assert "Type 2" in result.errors[0]
        
        # 3. Valid complete module
        valid_dataset = Dataset()
        valid_dataset.FrameOfReferenceUID = "1.2.840.10008.*******.*******.9"
        valid_dataset.PositionReferenceIndicator = "STERNAL_NOTCH"
        result = FrameOfReferenceValidator.validate(valid_dataset)
        assert len(result.errors) == 0