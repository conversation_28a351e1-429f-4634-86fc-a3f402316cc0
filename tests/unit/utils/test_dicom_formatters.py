"""
Test DICOM data formatting utilities.

Tests for format_date_value, format_time_value, and format_enum_value functions
that handle conversion of Python data types to DICOM-compliant string formats.
"""

from datetime import datetime, date
from enum import Enum

from pyrt_dicom.utils.dicom_formatters import (
    format_date_value,
    format_time_value,
    format_enum_value
)


class SampleEnum(Enum):
    """Sample enum for testing enum value formatting."""
    OPTION_A = "A"
    OPTION_B = "B"


class TestFormatDateValue:
    """Test format_date_value function."""
    
    def test_datetime_object(self):
        """Test formatting datetime object to DICOM DA format."""
        dt = datetime(2024, 1, 15, 14, 30, 45)
        result = format_date_value(dt)
        assert result == "20240115"
    
    def test_date_object(self):
        """Test formatting date object to DICOM DA format."""
        d = date(2023, 12, 25)
        result = format_date_value(d)
        assert result == "20231225"
    
    def test_string_value(self):
        """Test that string values are returned as-is."""
        date_str = "20220601"
        result = format_date_value(date_str)
        assert result == "20220601"
    
    def test_integer_value(self):
        """Test that integer values are converted to string."""
        date_int = 20210301
        result = format_date_value(date_int)
        assert result == "20210301"
    
    def test_none_value(self):
        """Test that None is converted to string 'None'."""
        result = format_date_value(None)
        assert result == "None"


class TestFormatTimeValue:
    """Test format_time_value function."""
    
    def test_datetime_without_microseconds(self):
        """Test formatting datetime object without microseconds."""
        dt = datetime(2024, 1, 15, 14, 30, 45)
        result = format_time_value(dt)
        assert result == "143045"
    
    def test_datetime_with_microseconds(self):
        """Test formatting datetime object with microseconds."""
        dt = datetime(2024, 1, 15, 14, 30, 45, 123456)
        result = format_time_value(dt)
        assert result == "143045.123456"
    
    def test_datetime_midnight(self):
        """Test formatting datetime at midnight."""
        dt = datetime(2024, 1, 15, 0, 0, 0)
        result = format_time_value(dt)
        assert result == "000000"
    
    def test_datetime_with_partial_microseconds(self):
        """Test formatting datetime with partial microseconds."""
        dt = datetime(2024, 1, 15, 9, 5, 3, 100)
        result = format_time_value(dt)
        assert result == "090503.000100"
    
    def test_string_value(self):
        """Test that string values are returned as-is."""
        time_str = "120000"
        result = format_time_value(time_str)
        assert result == "120000"
    
    def test_integer_value(self):
        """Test that integer values are converted to string."""
        time_int = 235959
        result = format_time_value(time_int)
        assert result == "235959"
    
    def test_none_value(self):
        """Test that None is converted to string 'None'."""
        result = format_time_value(None)
        assert result == "None"


class TestFormatEnumValue:
    """Test format_enum_value function."""
    
    def test_enum_with_value_attribute(self):
        """Test extracting value from enum object."""
        enum_obj = SampleEnum.OPTION_A
        result = format_enum_value(enum_obj)
        assert result == "A"
    
    def test_enum_option_b(self):
        """Test extracting value from different enum option."""
        enum_obj = SampleEnum.OPTION_B
        result = format_enum_value(enum_obj)
        assert result == "B"
    
    def test_string_value(self):
        """Test that string values are returned as-is."""
        string_val = "some_string"
        result = format_enum_value(string_val)
        assert result == "some_string"
    
    def test_integer_value(self):
        """Test that integer values are returned as-is."""
        int_val = 42
        result = format_enum_value(int_val)
        assert result == 42
    
    def test_none_value(self):
        """Test that None is returned as-is."""
        result = format_enum_value(None)
        assert result is None
    
    def test_object_without_value_attribute(self):
        """Test that objects without value attribute are returned as-is."""
        class CustomObject:
            def __init__(self, data):
                self.data = data
        
        obj = CustomObject("test_data")
        result = format_enum_value(obj)
        assert result is obj
    
    def test_list_value(self):
        """Test that list values are returned as-is."""
        list_val = [1, 2, 3]
        result = format_enum_value(list_val)
        assert result == [1, 2, 3]


class TestFormattersIntegration:
    """Integration tests for formatter functions."""
    
    def test_combined_datetime_formatting(self):
        """Test using both date and time formatters on same datetime."""
        dt = datetime(2024, 3, 20, 16, 45, 30, 500000)
        
        date_result = format_date_value(dt)
        time_result = format_time_value(dt)
        
        assert date_result == "20240320"
        assert time_result == "164530.500000"
    
    def test_dicom_standard_compliance(self):
        """Test that formatted values comply with DICOM standards."""
        # DICOM DA (Date) should be 8 characters YYYYMMDD
        dt = datetime(2024, 1, 1)
        date_result = format_date_value(dt)
        assert len(date_result) == 8
        assert date_result.isdigit()
        
        # DICOM TM (Time) should be HHMMSS or HHMMSS.FFFFFF
        time_result = format_time_value(dt)
        assert len(time_result) == 6  # HHMMSS format
        assert time_result.isdigit()