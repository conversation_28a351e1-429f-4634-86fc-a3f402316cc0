"""
Test GeneralEquipmentModule (M - Mandatory) functionality.

GeneralEquipmentModule implements DICOM PS3.3 C.7.5.1 General Equipment Module.
Required for all RTDoseIOD instances.
"""

import pytest
from pyrt_dicom.modules import GeneralEquipmentModule
from pyrt_dicom.validators import ValidationResult


class TestGeneralEquipmentModule:
    """Test GeneralEquipmentModule (M - Mandatory) functionality."""
    
    def test_from_required_elements_success(self):
        """Test successful creation with required elements."""
        equipment = GeneralEquipmentModule.from_required_elements(
            manufacturer="Test Manufacturer"
        )
        
        assert equipment.Manufacturer == "Test Manufacturer"
    
    def test_with_optional_elements(self):
        """Test adding optional equipment information."""
        equipment = GeneralEquipmentModule.from_required_elements(
            manufacturer="Varian Medical Systems"
        ).with_optional_elements(
            institution_name="Test Hospital",
            station_name="TrueBeam STx",
            manufacturers_model_name="TrueBeam",
            device_serial_number="12345",
            software_versions="v2.7.1"
        )
        
        assert equipment.InstitutionName == "Test Hospital"
        assert equipment.StationName == "TrueBeam STx"
        assert equipment.ManufacturerModelName == "TrueBeam"
        assert equipment.DeviceSerialNumber == "12345"
        assert equipment.SoftwareVersions == "v2.7.1"
    
    def test_various_manufacturers(self):
        """Test various manufacturer names."""
        manufacturers = [
            "Varian Medical Systems",
            "Elekta",
            "Siemens Healthineers",
            "Philips Healthcare",
            "GE Healthcare",
            "RaySearch Laboratories"
        ]
        
        for manufacturer in manufacturers:
            equipment = GeneralEquipmentModule.from_required_elements(
                manufacturer=manufacturer
            )
            assert equipment.Manufacturer == manufacturer
    
    def test_treatment_planning_systems(self):
        """Test equipment information for treatment planning systems."""
        equipment = GeneralEquipmentModule.from_required_elements(
            manufacturer="Eclipse Treatment Planning System"
        ).with_optional_elements(
            station_name="TPS-01",
            manufacturers_model_name="Eclipse",
            software_versions="v16.1.0"
        )
        
        assert "Eclipse" in equipment.Manufacturer
        assert equipment.StationName == "TPS-01"
        assert equipment.SoftwareVersions == "v16.1.0"
    
    def test_linac_equipment_info(self):
        """Test equipment information for linear accelerators."""
        equipment = GeneralEquipmentModule.from_required_elements(
            manufacturer="Varian Medical Systems"
        ).with_optional_elements(
            station_name="TrueBeam STx #1",
            manufacturers_model_name="TrueBeam STx",
            device_serial_number="TB001234",
            software_versions="v2.7.1",
            institution_name="Cancer Treatment Center"
        )
        
        assert equipment.Manufacturer == "Varian Medical Systems"
        assert "TrueBeam" in equipment.StationName
        assert "STx" in equipment.ManufacturerModelName
        assert equipment.DeviceSerialNumber.startswith("TB")
    
    def test_empty_manufacturer_handling(self):
        """Test handling of empty manufacturer (Type 2)."""
        equipment = GeneralEquipmentModule.from_required_elements(
            manufacturer=""  # Empty but not None
        )
        
        assert equipment.Manufacturer == ""
    
    def test_institutional_department_info(self):
        """Test institutional and department information."""
        equipment = GeneralEquipmentModule.from_required_elements(
            manufacturer="Test Manufacturer"
        ).with_optional_elements(
            institution_name="University Medical Center",
            institutional_department_name="Radiation Oncology",
            station_name="Physics WS"
        )
        
        assert equipment.InstitutionName == "University Medical Center"
        assert equipment.InstitutionalDepartmentName == "Radiation Oncology"
        assert equipment.StationName == "Physics WS"
    
    def test_software_version_formats(self):
        """Test various software version formats."""
        software_versions = [
            "v1.0",
            "v2.7.1",
            "16.1.0",
            "2024.1",
            "R2023b",
            "Build 12345"
        ]
        
        for version in software_versions:
            equipment = GeneralEquipmentModule.from_required_elements(
                manufacturer="Test Manufacturer"
            ).with_optional_elements(
                software_versions=version
            )
            assert equipment.SoftwareVersions == version
    
    def test_device_serial_number_formats(self):
        """Test various device serial number formats."""
        serial_numbers = [
            "12345",
            "TB001234",
            "SN-ABC-123",
            "VMS-001-2024",
            "LINAC001"
        ]
        
        for serial in serial_numbers:
            equipment = GeneralEquipmentModule.from_required_elements(
                manufacturer="Test Manufacturer"
            ).with_optional_elements(
                device_serial_number=serial
            )
            assert equipment.DeviceSerialNumber == serial
    
    def test_validation_method_exists(self):
        """Test that validation method exists and is callable."""
        equipment = GeneralEquipmentModule.from_required_elements(
            manufacturer="Test Manufacturer"
        )
        
        assert hasattr(equipment, 'validate')
        assert callable(equipment.validate)
        
        # Test validation result structure
        validation_result = equipment.validate()
        assert validation_result is not None
        assert isinstance(validation_result, ValidationResult)
        assert hasattr(validation_result, 'errors')
        assert hasattr(validation_result, 'warnings')
        assert isinstance(validation_result.errors, list)
        assert isinstance(validation_result.warnings, list)
    
    def test_equipment_for_dose_calculation(self):
        """Test equipment information specific to dose calculation."""
        equipment = GeneralEquipmentModule.from_required_elements(
            manufacturer="RayStation Treatment Planning System"
        ).with_optional_elements(
            manufacturers_model_name="RayStation",
            software_versions="v12.0.0",
            station_name="DOSE-CALC-01"
        )
        
        # Verify equipment suitable for dose calculation
        assert "RayStation" in equipment.Manufacturer
        assert equipment.StationName == "DOSE-CALC-01"
        assert equipment.SoftwareVersions == "v12.0.0"

    def test_pixel_padding_functionality(self):
        """Test pixel padding value functionality."""
        equipment = GeneralEquipmentModule.from_required_elements(
            manufacturer="Test Manufacturer"
        ).with_pixel_padding(
            pixel_padding_value=0
        )

        assert equipment.PixelPaddingValue == 0
        assert equipment.has_pixel_padding is True

        # Test without pixel padding
        equipment_no_padding = GeneralEquipmentModule.from_required_elements(
            manufacturer="Test Manufacturer"
        )
        assert equipment_no_padding.has_pixel_padding is False

    def test_calibration_date_time_pairing_valid(self):
        """Test valid calibration date/time pairing."""
        # Valid: Date only (single value stored as string)
        equipment = GeneralEquipmentModule.from_required_elements(
            manufacturer="Test Manufacturer"
        ).with_optional_elements(
            date_of_last_calibration=["20240101"]
        )
        assert equipment.DateOfLastCalibration == "20240101"  # pydicom converts single-item list to string
        assert equipment.has_calibration_info is True

        # Valid: Date and time paired (single values stored as strings)
        equipment_paired = GeneralEquipmentModule.from_required_elements(
            manufacturer="Test Manufacturer"
        ).with_optional_elements(
            date_of_last_calibration=["20240101"],
            time_of_last_calibration=["120000"]
        )
        assert equipment_paired.DateOfLastCalibration == "20240101"  # pydicom converts single-item list to string
        assert equipment_paired.TimeOfLastCalibration == "120000"  # pydicom converts single-item list to string
        assert equipment_paired.has_calibration_info is True

        # Valid: Multiple dates and times (stored as lists)
        equipment_multiple = GeneralEquipmentModule.from_required_elements(
            manufacturer="Test Manufacturer"
        ).with_optional_elements(
            date_of_last_calibration=["20240101", "20240201"],
            time_of_last_calibration=["120000", "130000"]
        )
        assert equipment_multiple.DateOfLastCalibration == ["20240101", "20240201"]
        assert equipment_multiple.TimeOfLastCalibration == ["120000", "130000"]
        assert equipment_multiple.has_calibration_info is True

    def test_calibration_date_time_pairing_invalid(self):
        """Test invalid calibration date/time pairing raises error."""
        # Invalid: Time without date should raise ValueError
        with pytest.raises(ValueError, match="Time of Last Calibration.*has no meaning unless.*Date of Last Calibration"):
            GeneralEquipmentModule.from_required_elements(
                manufacturer="Test Manufacturer"
            ).with_optional_elements(
                time_of_last_calibration=["120000"]  # Time without date
            )

    def test_institutional_department_type_code_sequence_creation(self):
        """Test creation of institutional department type code sequence items."""
        # Create sequence item
        dept_item = GeneralEquipmentModule.create_institutional_department_type_code_item(
            code_value="394802001",
            coding_scheme_designator="SCT",
            code_meaning="General medicine"
        )

        assert dept_item.CodeValue == "394802001"
        assert dept_item.CodingSchemeDesignator == "SCT"
        assert dept_item.CodeMeaning == "General medicine"
        assert not hasattr(dept_item, 'CodingSchemeVersion')

        # Create sequence item with version
        dept_item_with_version = GeneralEquipmentModule.create_institutional_department_type_code_item(
            code_value="394802001",
            coding_scheme_designator="SCT",
            code_meaning="General medicine",
            coding_scheme_version="2023-03"
        )

        assert dept_item_with_version.CodingSchemeVersion == "2023-03"

        # Test with sequence in module
        equipment = GeneralEquipmentModule.from_required_elements(
            manufacturer="Test Manufacturer"
        ).with_optional_elements(
            institutional_department_type_code_sequence=[dept_item]
        )

        assert len(equipment.InstitutionalDepartmentTypeCodeSequence) == 1
        assert equipment.InstitutionalDepartmentTypeCodeSequence[0].CodeValue == "394802001"

    def test_udi_sequence_creation(self):
        """Test creation of UDI sequence items."""
        # Create UDI item with minimal required data
        udi_item = GeneralEquipmentModule.create_udi_item(
            unique_device_identifier="(01)12345678901234(11)141231(17)150707(10)A123B456(21)12345"
        )

        assert udi_item.UniqueDeviceIdentifier == "(01)12345678901234(11)141231(17)150707(10)A123B456(21)12345"
        assert not hasattr(udi_item, 'DeviceIdentifier')

        # Create UDI item with all optional data
        udi_item_full = GeneralEquipmentModule.create_udi_item(
            unique_device_identifier="(01)12345678901234(11)141231(17)150707(10)A123B456(21)12345",
            device_identifier="12345678901234",
            issuer_of_udi="GS1",
            udi_issuer_country_code="US"
        )

        assert udi_item_full.DeviceIdentifier == "12345678901234"
        assert udi_item_full.IssuerOfUDI == "GS1"
        assert udi_item_full.UDIIssuerCountryCode == "US"

        # Test with sequence in module
        equipment = GeneralEquipmentModule.from_required_elements(
            manufacturer="Test Manufacturer"
        ).with_optional_elements(
            udi_sequence=[udi_item_full]
        )

        assert len(equipment.UDISequence) == 1
        assert equipment.UDISequence[0].UniqueDeviceIdentifier == "(01)12345678901234(11)141231(17)150707(10)A123B456(21)12345"

    def test_property_has_institution_info(self):
        """Test has_institution_info property."""
        # No institution info
        equipment = GeneralEquipmentModule.from_required_elements(
            manufacturer="Test Manufacturer"
        )
        assert equipment.has_institution_info is False

        # With institution name
        equipment_with_name = GeneralEquipmentModule.from_required_elements(
            manufacturer="Test Manufacturer"
        ).with_optional_elements(
            institution_name="Test Hospital"
        )
        assert equipment_with_name.has_institution_info is True

        # With institution address
        equipment_with_address = GeneralEquipmentModule.from_required_elements(
            manufacturer="Test Manufacturer"
        ).with_optional_elements(
            institution_address="123 Main St, City, State"
        )
        assert equipment_with_address.has_institution_info is True

    def test_property_has_device_identification(self):
        """Test has_device_identification property."""
        # No device identification
        equipment = GeneralEquipmentModule.from_required_elements(
            manufacturer="Test Manufacturer"
        )
        assert equipment.has_device_identification is False

        # With model name
        equipment_with_model = GeneralEquipmentModule.from_required_elements(
            manufacturer="Test Manufacturer"
        ).with_optional_elements(
            manufacturers_model_name="SuperScan 3000"
        )
        assert equipment_with_model.has_device_identification is True

        # With serial number
        equipment_with_serial = GeneralEquipmentModule.from_required_elements(
            manufacturer="Test Manufacturer"
        ).with_optional_elements(
            device_serial_number="12345"
        )
        assert equipment_with_serial.has_device_identification is True

        # With device UID
        equipment_with_uid = GeneralEquipmentModule.from_required_elements(
            manufacturer="Test Manufacturer"
        ).with_optional_elements(
            device_uid="*******.*******.9"
        )
        assert equipment_with_uid.has_device_identification is True