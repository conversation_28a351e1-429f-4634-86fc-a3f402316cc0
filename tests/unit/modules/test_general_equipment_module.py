"""
Test GeneralEquipmentModule (M - Mandatory) functionality.

GeneralEquipmentModule implements DICOM PS3.3 C.7.5.1 General Equipment Module.
Required for all RTDoseIOD instances.
"""

import pytest
from pyrt_dicom.modules import GeneralEquipmentModule
from pyrt_dicom.validators import ValidationResult


class TestGeneralEquipmentModule:
    """Test GeneralEquipmentModule (M - Mandatory) functionality."""
    
    def test_from_required_elements_success(self):
        """Test successful creation with required elements."""
        equipment = GeneralEquipmentModule.from_required_elements(
            manufacturer="Test Manufacturer"
        )
        
        assert equipment.Manufacturer == "Test Manufacturer"
    
    def test_with_optional_elements(self):
        """Test adding optional equipment information."""
        equipment = GeneralEquipmentModule.from_required_elements(
            manufacturer="Varian Medical Systems"
        ).with_optional_elements(
            institution_name="Test Hospital",
            station_name="TrueBeam STx",
            manufacturers_model_name="TrueBeam",
            device_serial_number="12345",
            software_versions="v2.7.1"
        )
        
        assert equipment.InstitutionName == "Test Hospital"
        assert equipment.StationName == "TrueBeam STx"
        assert equipment.ManufacturerModelName == "TrueBeam"
        assert equipment.DeviceSerialNumber == "12345"
        assert equipment.SoftwareVersions == "v2.7.1"
    
    def test_various_manufacturers(self):
        """Test various manufacturer names."""
        manufacturers = [
            "Varian Medical Systems",
            "Elekta",
            "Siemens Healthineers",
            "Philips Healthcare",
            "GE Healthcare",
            "RaySearch Laboratories"
        ]
        
        for manufacturer in manufacturers:
            equipment = GeneralEquipmentModule.from_required_elements(
                manufacturer=manufacturer
            )
            assert equipment.Manufacturer == manufacturer
    
    def test_treatment_planning_systems(self):
        """Test equipment information for treatment planning systems."""
        equipment = GeneralEquipmentModule.from_required_elements(
            manufacturer="Eclipse Treatment Planning System"
        ).with_optional_elements(
            station_name="TPS-01",
            manufacturers_model_name="Eclipse",
            software_versions="v16.1.0"
        )
        
        assert "Eclipse" in equipment.Manufacturer
        assert equipment.StationName == "TPS-01"
        assert equipment.SoftwareVersions == "v16.1.0"
    
    def test_linac_equipment_info(self):
        """Test equipment information for linear accelerators."""
        equipment = GeneralEquipmentModule.from_required_elements(
            manufacturer="Varian Medical Systems"
        ).with_optional_elements(
            station_name="TrueBeam STx #1",
            manufacturers_model_name="TrueBeam STx",
            device_serial_number="TB001234",
            software_versions="v2.7.1",
            institution_name="Cancer Treatment Center"
        )
        
        assert equipment.Manufacturer == "Varian Medical Systems"
        assert "TrueBeam" in equipment.StationName
        assert "STx" in equipment.ManufacturerModelName
        assert equipment.DeviceSerialNumber.startswith("TB")
    
    def test_empty_manufacturer_handling(self):
        """Test handling of empty manufacturer (Type 2)."""
        equipment = GeneralEquipmentModule.from_required_elements(
            manufacturer=""  # Empty but not None
        )
        
        assert equipment.Manufacturer == ""
    
    def test_institutional_department_info(self):
        """Test institutional and department information."""
        equipment = GeneralEquipmentModule.from_required_elements(
            manufacturer="Test Manufacturer"
        ).with_optional_elements(
            institution_name="University Medical Center",
            institutional_department_name="Radiation Oncology",
            station_name="Physics WS"
        )
        
        assert equipment.InstitutionName == "University Medical Center"
        assert equipment.InstitutionalDepartmentName == "Radiation Oncology"
        assert equipment.StationName == "Physics WS"
    
    def test_software_version_formats(self):
        """Test various software version formats."""
        software_versions = [
            "v1.0",
            "v2.7.1",
            "16.1.0",
            "2024.1",
            "R2023b",
            "Build 12345"
        ]
        
        for version in software_versions:
            equipment = GeneralEquipmentModule.from_required_elements(
                manufacturer="Test Manufacturer"
            ).with_optional_elements(
                software_versions=version
            )
            assert equipment.SoftwareVersions == version
    
    def test_device_serial_number_formats(self):
        """Test various device serial number formats."""
        serial_numbers = [
            "12345",
            "TB001234",
            "SN-ABC-123",
            "VMS-001-2024",
            "LINAC001"
        ]
        
        for serial in serial_numbers:
            equipment = GeneralEquipmentModule.from_required_elements(
                manufacturer="Test Manufacturer"
            ).with_optional_elements(
                device_serial_number=serial
            )
            assert equipment.DeviceSerialNumber == serial
    
    def test_validation_method_exists(self):
        """Test that validation method exists and is callable."""
        equipment = GeneralEquipmentModule.from_required_elements(
            manufacturer="Test Manufacturer"
        )
        
        assert hasattr(equipment, 'validate')
        assert callable(equipment.validate)
        
        # Test validation result structure
        validation_result = equipment.validate()
        assert validation_result is not None
        assert isinstance(validation_result, ValidationResult)
        assert hasattr(validation_result, 'errors')
        assert hasattr(validation_result, 'warnings')
        assert isinstance(validation_result.errors, list)
        assert isinstance(validation_result.warnings, list)
    
    def test_equipment_for_dose_calculation(self):
        """Test equipment information specific to dose calculation."""
        equipment = GeneralEquipmentModule.from_required_elements(
            manufacturer="RayStation Treatment Planning System"
        ).with_optional_elements(
            manufacturers_model_name="RayStation",
            software_versions="v12.0.0",
            station_name="DOSE-CALC-01"
        )
        
        # Verify equipment suitable for dose calculation
        assert "RayStation" in equipment.Manufacturer
        assert equipment.StationName == "DOSE-CALC-01"
        assert equipment.SoftwareVersions == "v12.0.0"