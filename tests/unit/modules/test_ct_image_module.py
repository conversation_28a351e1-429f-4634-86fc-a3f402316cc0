"""
Test CTImageModule functionality.

CTImageModule implements DICOM PS3.3 C.8.2.1 CT Image Module.
Contains attributes that describe CT images including pixel characteristics,
acquisition parameters, and reconstruction details.
"""

import pytest
from pydicom import Dataset
from pyrt_dicom.modules import CTImageModule
from pyrt_dicom.enums.contrast_ct_enums import (
    MultiEnergyCTAcquisition, RotationDirection, ExposureModulationType,
    CTImageTypeValue3, CTImageTypeValue4, CTSamplesPerPixel, CTBitsAllocated,
    CTBitsStored, RescaleType, FilterMaterial, ScanOptions, FilterType,
    CTImageTypeValue1, CTImageTypeValue2
)
from pyrt_dicom.enums.image_enums import PhotometricInterpretation, ImageType
from pyrt_dicom.validators import ValidationResult


class TestCTImageModule:
    """Test CTImageModule functionality."""
    
    def test_from_required_elements_success(self):
        """Test successful creation with required elements."""
        ct_image = CTImageModule.from_required_elements(
            image_type=[
                CTImageTypeValue1.ORIGINAL.value,
                CTImageTypeValue2.PRIMARY.value,
                CTImageTypeValue3.AXIAL.value
            ],
            samples_per_pixel=CTSamplesPerPixel.ONE.value,
            photometric_interpretation=PhotometricInterpretation.MONOCHROME2.value,
            bits_allocated=CTBitsAllocated.SIXTEEN.value,
            bits_stored=CTBitsStored.SIXTEEN.value,
            high_bit=15,
            rescale_intercept=-1024.0,
            rescale_slope=1.0,
            kvp=120.0,
            acquisition_number=1
        )

        # Test using to_dataset() method
        dataset = ct_image.to_dataset()
        assert dataset.ImageType == ["ORIGINAL", "PRIMARY", "AXIAL"]
        assert dataset.SamplesPerPixel == 1
        assert dataset.PhotometricInterpretation == "MONOCHROME2"
        assert dataset.BitsAllocated == 16
        assert dataset.BitsStored == 16
        assert dataset.HighBit == 15
        assert dataset.RescaleIntercept == -1024.0
        assert dataset.RescaleSlope == 1.0
        assert dataset.KVP == 120.0
        assert dataset.AcquisitionNumber == 1
    
    def test_type_1_elements_with_enums(self):
        """Test Type 1 elements with enum values."""
        ct_image = CTImageModule.from_required_elements(
            image_type=[ImageType.ORIGINAL, ImageType.PRIMARY, CTImageTypeValue3.LOCALIZER],
            samples_per_pixel=CTSamplesPerPixel.ONE,
            photometric_interpretation=PhotometricInterpretation.MONOCHROME2,
            bits_allocated=CTBitsAllocated.SIXTEEN,
            bits_stored=CTBitsStored.TWELVE,
            high_bit=11,
            rescale_intercept=0.0,
            rescale_slope=1.0,
            kvp=110.0,
            acquisition_number=5
        )

        dataset = ct_image.to_dataset()
        assert dataset.ImageType == ["ORIGINAL", "PRIMARY", "LOCALIZER"]
        assert dataset.BitsStored == 12
        assert dataset.HighBit == 11
    
    def test_type_2_elements_empty_values(self):
        """Test Type 2 elements can have empty values."""
        ct_image = CTImageModule.from_required_elements(
            image_type=["ORIGINAL", "PRIMARY", "AXIAL"],
            samples_per_pixel=1,
            photometric_interpretation="MONOCHROME2",
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            rescale_intercept=-1024.0,
            rescale_slope=1.0,
            kvp="",  # Type 2 can be empty
            acquisition_number=""  # Type 2 can be empty
        )

        dataset = ct_image.to_dataset()
        assert dataset.KVP == ""
        assert dataset.AcquisitionNumber == ""
    
    def test_with_optional_elements(self):
        """Test adding optional Type 3 elements."""
        ct_image = CTImageModule.from_required_elements(
            image_type=["ORIGINAL", "PRIMARY", "AXIAL"],
            samples_per_pixel=1,
            photometric_interpretation="MONOCHROME2",
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            rescale_intercept=-1024.0,
            rescale_slope=1.0,
            kvp=120.0,
            acquisition_number=1
        ).with_optional_elements(
            multi_energy_ct_acquisition=MultiEnergyCTAcquisition.NO,
            scan_options=ScanOptions.HELICAL_CT,
            data_collection_diameter=500.0,
            convolution_kernel="STANDARD",
            revolution_time=0.5,
            filter_type=FilterType.ALUMINUM,
            filter_material=[FilterMaterial.ALUMINUM, FilterMaterial.COPPER]
        )

        dataset = ct_image.to_dataset()
        assert dataset.MultienergyCTAcquisition == "NO"
        assert dataset.ScanOptions == "HELICAL_CT"
        assert dataset.DataCollectionDiameter == 500.0
        assert dataset.ConvolutionKernel == "STANDARD"
        assert dataset.RevolutionTime == 0.5
        assert dataset.FilterType == "ALUMINUM"
        assert dataset.FilterMaterial == ["AL", "CU"]
    
    def test_rescale_type_conditional_required(self):
        """Test rescale type conditional requirement."""
        ct_image = CTImageModule.from_required_elements(
            image_type=["ORIGINAL", "PRIMARY", "AXIAL"],
            samples_per_pixel=1,
            photometric_interpretation="MONOCHROME2",
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            rescale_intercept=-1024.0,
            rescale_slope=1.0,
            kvp=120.0,
            acquisition_number=1
        ).with_optional_elements(
            multi_energy_ct_acquisition=MultiEnergyCTAcquisition.YES
        ).with_rescale_type_conditional(
            rescale_type=RescaleType.HU
        )

        dataset = ct_image.to_dataset()
        assert dataset.MultienergyCTAcquisition == "YES"
        assert dataset.RescaleType == "HU"
    
    def test_energy_weighting_conditional(self):
        """Test energy weighting factor conditional logic."""
        # Create derivation code sequence with multi-energy proportional weighting
        derivation_item = Dataset()
        derivation_item.CodeValue = '113097'
        derivation_item.CodingSchemeDesignator = 'DCM'
        derivation_item.CodeMeaning = 'Multi-energy proportional weighting'
        derivation_sequence = [derivation_item]

        ct_image = CTImageModule.from_required_elements(
            image_type=["DERIVED", "SECONDARY", "AXIAL"],
            samples_per_pixel=1,
            photometric_interpretation="MONOCHROME2",
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            rescale_intercept=-1024.0,
            rescale_slope=1.0,
            kvp=120.0,
            acquisition_number=1
        ).with_energy_weighting_conditional(
            energy_weighting_factor=0.5,
            derivation_code_sequence=derivation_sequence
        )

        dataset = ct_image.to_dataset()
        assert dataset.EnergyWeightingFactor == 0.5
    
    def test_water_equivalent_diameter_conditional(self):
        """Test water equivalent diameter calculation method conditional."""
        # Create Dataset object for sequence
        code_item = Dataset()
        code_item.CodeValue = '113821'
        code_item.CodingSchemeDesignator = 'DCM'
        code_item.CodeMeaning = 'Equivalent diameter'

        ct_image = CTImageModule.from_required_elements(
            image_type=["ORIGINAL", "PRIMARY", "AXIAL"],
            samples_per_pixel=1,
            photometric_interpretation="MONOCHROME2",
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            rescale_intercept=-1024.0,
            rescale_slope=1.0,
            kvp=120.0,
            acquisition_number=1
        ).with_optional_elements(
            water_equivalent_diameter=350.0
        ).with_water_equivalent_diameter_conditional(
            water_equivalent_diameter_calculation_method_code_sequence=[code_item]
        )

        dataset = ct_image.to_dataset()
        assert dataset.WaterEquivalentDiameter == 350.0
        assert hasattr(dataset, 'WaterEquivalentDiameterCalculationMethodCodeSequence')
    
    def test_create_ctdi_phantom_type_code_item(self):
        """Test CTDI phantom type code item creation."""
        item = CTImageModule.create_ctdi_phantom_type_code_item(
            code_value="113681",
            code_meaning="IEC Body Dosimetry Phantom"
        )
        
        assert isinstance(item, Dataset)
        assert item.CodeValue == "113681"
        assert item.CodingSchemeDesignator == "DCM"
        assert item.CodeMeaning == "IEC Body Dosimetry Phantom"
    
    def test_create_ct_additional_x_ray_source_item(self):
        """Test CT additional X-Ray source sequence item creation."""
        item = CTImageModule.create_ct_additional_x_ray_source_item(
            kvp=140.0,
            x_ray_tube_current_in_ma=200.0,
            data_collection_diameter=500.0,
            focal_spots=[0.6, 1.2],
            filter_type="ALUMINUM",
            filter_material=["AL", "CU"],
            exposure_in_mas=100.0,
            energy_weighting_factor=0.3
        )
        
        assert isinstance(item, Dataset)
        assert item.KVP == 140.0
        assert item.XRayTubeCurrentInmA == 200.0
        assert item.DataCollectionDiameter == 500.0
        assert item.FocalSpots == [0.6, 1.2]
        assert item.FilterType == "ALUMINUM"
        assert item.FilterMaterial == ["AL", "CU"]
        assert item.ExposureInmAs == 100.0
        assert item.EnergyWeightingFactor == 0.3
    
    def test_property_is_multi_energy(self):
        """Test is_multi_energy property."""
        # Single energy CT
        single_energy = CTImageModule.from_required_elements(
            image_type=["ORIGINAL", "PRIMARY", "AXIAL"],
            samples_per_pixel=1,
            photometric_interpretation="MONOCHROME2",
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            rescale_intercept=-1024.0,
            rescale_slope=1.0,
            kvp=120.0,
            acquisition_number=1
        ).with_optional_elements(
            multi_energy_ct_acquisition=MultiEnergyCTAcquisition.NO
        )
        
        assert not single_energy.is_multi_energy
        
        # Multi-energy CT
        multi_energy = CTImageModule.from_required_elements(
            image_type=["ORIGINAL", "PRIMARY", "AXIAL"],
            samples_per_pixel=1,
            photometric_interpretation="MONOCHROME2",
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            rescale_intercept=-1024.0,
            rescale_slope=1.0,
            kvp=120.0,
            acquisition_number=1
        ).with_optional_elements(
            multi_energy_ct_acquisition=MultiEnergyCTAcquisition.YES
        )
        
        assert multi_energy.is_multi_energy
    
    def test_property_is_axial_image(self):
        """Test is_axial_image property."""
        axial_image = CTImageModule.from_required_elements(
            image_type=["ORIGINAL", "PRIMARY", "AXIAL"],
            samples_per_pixel=1,
            photometric_interpretation="MONOCHROME2",
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            rescale_intercept=-1024.0,
            rescale_slope=1.0,
            kvp=120.0,
            acquisition_number=1
        )
        
        assert axial_image.is_axial_image
        assert not axial_image.is_localizer_image
    
    def test_property_is_localizer_image(self):
        """Test is_localizer_image property."""
        localizer_image = CTImageModule.from_required_elements(
            image_type=["ORIGINAL", "PRIMARY", "LOCALIZER"],
            samples_per_pixel=1,
            photometric_interpretation="MONOCHROME2",
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            rescale_intercept=-1024.0,
            rescale_slope=1.0,
            kvp=120.0,
            acquisition_number=1
        )
        
        assert localizer_image.is_localizer_image
        assert not localizer_image.is_axial_image
    
    def test_property_has_rescale_type(self):
        """Test has_rescale_type property."""
        ct_image = CTImageModule.from_required_elements(
            image_type=["ORIGINAL", "PRIMARY", "AXIAL"],
            samples_per_pixel=1,
            photometric_interpretation="MONOCHROME2",
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            rescale_intercept=-1024.0,
            rescale_slope=1.0,
            kvp=120.0,
            acquisition_number=1
        )
        
        assert not ct_image.has_rescale_type
        
        ct_image.with_rescale_type_conditional(rescale_type=RescaleType.HU)
        assert ct_image.has_rescale_type
    
    def test_property_has_water_equivalent_diameter(self):
        """Test has_water_equivalent_diameter property."""
        ct_image = CTImageModule.from_required_elements(
            image_type=["ORIGINAL", "PRIMARY", "AXIAL"],
            samples_per_pixel=1,
            photometric_interpretation="MONOCHROME2",
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            rescale_intercept=-1024.0,
            rescale_slope=1.0,
            kvp=120.0,
            acquisition_number=1
        )
        
        assert not ct_image.has_water_equivalent_diameter
        
        ct_image.with_optional_elements(water_equivalent_diameter=350.0)
        assert ct_image.has_water_equivalent_diameter
    
    def test_validation_method_exists(self):
        """Test that validation method exists and is callable."""
        ct_image = CTImageModule.from_required_elements(
            image_type=["ORIGINAL", "PRIMARY", "AXIAL"],
            samples_per_pixel=1,
            photometric_interpretation="MONOCHROME2",
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            rescale_intercept=-1024.0,
            rescale_slope=1.0,
            kvp=120.0,
            acquisition_number=1
        )
        
        assert hasattr(ct_image, 'validate')
        assert callable(ct_image.validate)
        
        # Test validation result structure
        validation_result = ct_image.validate()
        assert validation_result is not None
        assert isinstance(validation_result, ValidationResult)
        assert hasattr(validation_result, 'errors')
        assert hasattr(validation_result, 'warnings')
        assert isinstance(validation_result.errors, list)
        assert isinstance(validation_result.warnings, list)
    
    def test_multi_energy_image_type_value_4(self):
        """Test multi-energy CT with Image Type Value 4."""
        ct_image = CTImageModule.from_required_elements(
            image_type=[
                "DERIVED",
                "SECONDARY",
                "AXIAL",
                CTImageTypeValue4.VMI.value
            ],
            samples_per_pixel=1,
            photometric_interpretation="MONOCHROME2",
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            rescale_intercept=-1024.0,
            rescale_slope=1.0,
            kvp=120.0,
            acquisition_number=1
        ).with_optional_elements(
            multi_energy_ct_acquisition=MultiEnergyCTAcquisition.YES
        )

        dataset = ct_image.to_dataset()
        assert len(dataset.ImageType) == 4
        assert dataset.ImageType[3] == "VMI"
        assert ct_image.is_multi_energy
    
    def test_method_chaining(self):
        """Test that method chaining works correctly."""
        ct_image = (CTImageModule
                   .from_required_elements(
                       image_type=["ORIGINAL", "PRIMARY", "AXIAL"],
                       samples_per_pixel=1,
                       photometric_interpretation="MONOCHROME2",
                       bits_allocated=16,
                       bits_stored=16,
                       high_bit=15,
                       rescale_intercept=-1024.0,
                       rescale_slope=1.0,
                       kvp=120.0,
                       acquisition_number=1
                   )
                   .with_optional_elements(
                       scan_options=ScanOptions.HELICAL_CT,
                       convolution_kernel="STANDARD"
                   )
                   .with_rescale_type_conditional(
                       rescale_type=RescaleType.HU
                   ))

        dataset = ct_image.to_dataset()
        assert dataset.ScanOptions == "HELICAL_CT"
        assert dataset.ConvolutionKernel == "STANDARD"
        assert dataset.RescaleType == "HU"

    def test_to_dataset_method(self):
        """Test that to_dataset() method works correctly."""
        ct_image = CTImageModule.from_required_elements(
            image_type=["ORIGINAL", "PRIMARY", "AXIAL"],
            samples_per_pixel=1,
            photometric_interpretation="MONOCHROME2",
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            rescale_intercept=-1024.0,
            rescale_slope=1.0,
            kvp=120.0,
            acquisition_number=1
        ).with_optional_elements(
            convolution_kernel="STANDARD"
        )

        # Test that to_dataset() returns a pydicom Dataset
        dataset = ct_image.to_dataset()
        assert isinstance(dataset, Dataset)

        # Test that dataset contains expected attributes
        assert hasattr(dataset, 'ImageType')
        assert hasattr(dataset, 'SamplesPerPixel')
        assert hasattr(dataset, 'ConvolutionKernel')

        # Test that dataset is a copy (not the same object)
        dataset2 = ct_image.to_dataset()
        assert dataset is not dataset2
        assert dataset.ImageType == dataset2.ImageType