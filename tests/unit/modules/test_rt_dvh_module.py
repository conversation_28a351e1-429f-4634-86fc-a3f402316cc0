"""
Test RTDVHModule (U - Optional) functionality.

RTDVHModule implements DICOM PS3.3 C.8.8.4 RT DVH Module.
Optional module for dose-volume histogram data enhancement.
"""

import pytest
import numpy as np
from pyrt_dicom.modules import RTDVHModule
from pyrt_dicom.enums import DVHType, DoseUnits, DVHVolumeUnits, DVHROIContributionType, DoseType
from pyrt_dicom.validators import ValidationResult


class TestRTDVHModule:
    """Test RTDVHModule (U - Optional) functionality."""
    
    def _create_basic_dvh(self, dvh_data=None, dvh_type=None, invalid_type=False):
        """Helper method to create basic DVH structure."""
        if dvh_data is None:
            # DVH data format: [D1, V1, D2, V2, D3, V3, ...] (dose, volume pairs)
            dvh_data = [0.0, 100.0, 10.0, 95.0, 20.0, 90.0, 30.0, 85.0, 40.0, 80.0]
        
        if dvh_type is None:
            dvh_type = DVHType.CUMULATIVE
        
        # Create referenced structure set
        ref_struct_set = RTDVHModule.create_referenced_structure_set_item(
            referenced_sop_class_uid="1.2.840.10008.*******.1.481.3",
            referenced_sop_instance_uid="*******.*******.9"
        )
        
        # Create DVH item
        if invalid_type:
            dvh_item = RTDVHModule.create_dvh_item(
                dvh_referenced_roi_sequence=[
                    RTDVHModule.create_dvh_referenced_roi_item(
                        referenced_roi_number=1,
                        dvh_roi_contribution_type='INVALID_TYPE'
                    )
                ],
                dvh_type='INVALID_TYPE',
                dose_units='INVALID_UNIT',
                dose_type='INVALID_TYPE',
                dvh_dose_scaling=-1.0,
                dvh_volume_units='INVALID_UNIT',
                dvh_number_of_bins=-5,
                dvh_data=[],
                dvh_minimum_dose=100.0,
                dvh_maximum_dose=0.0,
                dvh_mean_dose=50.0
            )
        else:
            dvh_item = RTDVHModule.create_dvh_item(
                dvh_referenced_roi_sequence=[
                    RTDVHModule.create_dvh_referenced_roi_item(
                        referenced_roi_number=1,
                        dvh_roi_contribution_type=DVHROIContributionType.INCLUDED
                    )
                ],
                dvh_type=dvh_type,
                dose_units=DoseUnits.GY,
                dose_type=DoseType.PHYSICAL,
                dvh_dose_scaling=0.01,
                dvh_volume_units=DVHVolumeUnits.CM3,
                dvh_number_of_bins=len(dvh_data) // 2,  # Number of dose-volume pairs
                dvh_data=dvh_data.tolist() if hasattr(dvh_data, 'tolist') else dvh_data,
                dvh_minimum_dose=0.0,
                dvh_maximum_dose=50.0,
                dvh_mean_dose=25.0
            )
        
        return RTDVHModule.from_required_elements(
            referenced_structure_set_sequence=[ref_struct_set],
            dvh_sequence=[dvh_item]
        )
    
    def test_from_required_elements_success(self):
        """Test successful creation with required elements."""
        # DVH data as dose,volume pairs: [D1, V1, D2, V2, ...]
        dvh_data = [0.0, 100.0, 10.0, 95.0, 20.0, 90.0, 30.0, 85.0, 40.0, 80.0]
        dvh = self._create_basic_dvh(dvh_data)
        
        assert len(dvh.DVHSequence) == 1
        assert dvh.DVHSequence[0].DVHType == DVHType.CUMULATIVE.value
        assert dvh.DVHSequence[0].DoseUnits == DoseUnits.GY.value
    
    def test_dvh_type_validation(self):
        """Test DVH type enumeration validation."""
        dvh_data = [0.0, 100.0, 10.0, 80.0, 20.0, 60.0, 30.0, 40.0, 40.0, 20.0]
        
        # Test that invalid DVH type can be set without validation
        dvh_invalid = self._create_basic_dvh(dvh_data, invalid_type=True)
        assert 'INVALID_TYPE' in str(dvh_invalid.DVHSequence[0].DVHType)
        
        # Test validation of valid types
        for dvh_type in [DVHType.CUMULATIVE, DVHType.DIFFERENTIAL]:
            dvh = self._create_basic_dvh(dvh_data, dvh_type)
            validation_result = dvh.validate()
            assert len(validation_result.errors) == 0
            assert dvh.DVHSequence[0].DVHType == dvh_type.value
    
    def test_multiple_roi_dvh_sequences(self):
        """Test DVH sequences for multiple ROIs."""
        # Target DVH as dose,volume pairs
        target_dvh = [0.0, 100.0, 10.0, 95.0, 20.0, 90.0, 30.0, 85.0, 40.0, 80.0]
        # OAR DVH as dose,volume pairs  
        oar_dvh = [0.0, 100.0, 5.0, 60.0, 10.0, 30.0, 15.0, 10.0, 20.0, 5.0]
        
        ref_struct_set = RTDVHModule.create_referenced_structure_set_item(
            referenced_sop_class_uid="1.2.840.10008.*******.1.481.3",
            referenced_sop_instance_uid="*******.*******.9"
        )
        
        target_dvh_item = RTDVHModule.create_dvh_item(
            dvh_referenced_roi_sequence=[
                RTDVHModule.create_dvh_referenced_roi_item(
                    referenced_roi_number=1,
                    dvh_roi_contribution_type=DVHROIContributionType.INCLUDED
                )
            ],
            dvh_type=DVHType.CUMULATIVE,
            dose_units=DoseUnits.GY,
            dose_type=DoseType.PHYSICAL,
            dvh_dose_scaling=0.01,
            dvh_volume_units=DVHVolumeUnits.CM3,
            dvh_number_of_bins=len(target_dvh) // 2,
            dvh_data=target_dvh,
            dvh_minimum_dose=0.0,
            dvh_maximum_dose=50.0,
            dvh_mean_dose=40.0
        )
        
        oar_dvh_item = RTDVHModule.create_dvh_item(
            dvh_referenced_roi_sequence=[
                RTDVHModule.create_dvh_referenced_roi_item(
                    referenced_roi_number=2,
                    dvh_roi_contribution_type=DVHROIContributionType.INCLUDED
                )
            ],
            dvh_type=DVHType.CUMULATIVE,
            dose_units=DoseUnits.GY,
            dose_type=DoseType.PHYSICAL,
            dvh_dose_scaling=0.01,
            dvh_volume_units=DVHVolumeUnits.CM3,
            dvh_number_of_bins=len(oar_dvh) // 2,
            dvh_data=oar_dvh,
            dvh_minimum_dose=0.0,
            dvh_maximum_dose=20.0,
            dvh_mean_dose=8.0
        )
        
        dvh = RTDVHModule.from_required_elements(
            referenced_structure_set_sequence=[ref_struct_set],
            dvh_sequence=[target_dvh_item, oar_dvh_item]
        )
        
        assert len(dvh.DVHSequence) == 2
        assert dvh.DVHSequence[0].DVHReferencedROISequence[0].ReferencedROINumber == 1
        assert dvh.DVHSequence[1].DVHReferencedROISequence[0].ReferencedROINumber == 2
    
    def test_dose_units_validation(self):
        """Test dose units enumeration validation."""
        dvh_data = [0.0, 100.0, 10.0, 80.0, 20.0, 60.0, 30.0, 40.0, 40.0, 20.0]
        
        # Test validation of valid dose units
        for dose_units in [DoseUnits.GY, DoseUnits.RELATIVE]:
            dvh = self._create_basic_dvh(dvh_data)
            validation_result = dvh.validate()
            assert len(validation_result.errors) == 0
    
    def test_volume_units_validation(self):
        """Test volume units enumeration validation."""
        dvh_data = [0.0, 100.0, 10.0, 85.0, 20.0, 70.0, 30.0, 55.0, 40.0, 40.0]
        
        # Test validation of valid volume units
        for volume_units in [DVHVolumeUnits.CM3, DVHVolumeUnits.PERCENT]:
            dvh = self._create_basic_dvh(dvh_data)
            validation_result = dvh.validate()
            assert len(validation_result.errors) == 0
    
    def test_dvh_data_array_validation(self):
        """Test DVH data array validation and formats."""
        # Test different valid DVH data formats
        valid_formats = [
            [0.0, 100.0, 10.0, 80.0, 20.0, 60.0, 30.0, 40.0, 40.0, 20.0],  # List
            np.array([0.0, 100.0, 10.0, 80.0, 20.0, 60.0, 30.0, 40.0, 40.0, 20.0]),  # Array
        ]
        
        for data in valid_formats:
            dvh = self._create_basic_dvh(data)
            validation_result = dvh.validate()
            assert len(validation_result.errors) == 0
    
    def test_roi_contribution_types(self):
        """Test ROI contribution type validation."""
        dvh_data = [0.0, 100.0, 10.0, 75.0, 20.0, 50.0, 30.0, 25.0, 40.0, 0.0]
        
        # Test valid contribution types
        for contrib_type in [DVHROIContributionType.INCLUDED, DVHROIContributionType.EXCLUDED]:
            dvh = self._create_basic_dvh(dvh_data)
            # No additional validation needed - creation success is the test
            assert len(dvh.DVHSequence) == 1
    
    def test_dvh_dose_scaling_validation(self):
        """Test DVH dose scaling factor validation."""
        dvh_data = [0.0, 100.0, 10.0, 75.0, 20.0, 50.0, 30.0, 25.0, 40.0, 0.0]
        
        # Test valid scaling factors
        valid_scaling_factors = [0.001, 0.01, 0.1, 1.0, 10.0]
        
        for scaling in valid_scaling_factors:
            dvh = self._create_basic_dvh(dvh_data)
            validation_result = dvh.validate()
            assert len(validation_result.errors) == 0
    
    def test_dvh_statistics_validation(self):
        """Test DVH statistical parameters validation."""
        dvh_data = [0.0, 100.0, 5.0, 90.0, 10.0, 80.0, 15.0, 70.0, 20.0, 60.0, 25.0, 50.0, 30.0, 40.0, 35.0, 30.0, 40.0, 20.0, 45.0, 10.0]
        dvh = self._create_basic_dvh(dvh_data)
        
        # Verify basic statistical consistency can be checked
        assert hasattr(dvh.DVHSequence[0], 'DVHMinimumDose')
        assert hasattr(dvh.DVHSequence[0], 'DVHMaximumDose')
        assert hasattr(dvh.DVHSequence[0], 'DVHMeanDose')
    
    def test_differential_dvh_handling(self):
        """Test differential DVH data handling."""
        # Differential DVH as dose,volume pairs
        diff_dvh_data = [0.0, 5.0, 5.0, 15.0, 10.0, 25.0, 15.0, 30.0, 20.0, 20.0, 25.0, 5.0]
        dvh = self._create_basic_dvh(diff_dvh_data, DVHType.DIFFERENTIAL)
        
        assert dvh.DVHSequence[0].DVHType == DVHType.DIFFERENTIAL.value
        assert len(dvh.DVHSequence[0].DVHData) == len(diff_dvh_data)
    
    def test_with_optional_elements(self):
        """Test adding optional DVH elements."""
        dvh_data = [0.0, 100.0, 10.0, 80.0, 20.0, 60.0, 30.0, 40.0, 40.0, 20.0]
        dvh = self._create_basic_dvh(dvh_data).with_optional_elements(
            dvh_normalization_point=[100.0, 50.0, 25.0],
            dvh_normalization_dose_value=200.0
        )
        
        assert hasattr(dvh, 'DVHNormalizationPoint')
        assert hasattr(dvh, 'DVHNormalizationDoseValue')
    
    def test_empty_dvh_sequence(self):
        """Test handling of empty DVH sequence."""
        ref_struct_set = RTDVHModule.create_referenced_structure_set_item(
            referenced_sop_class_uid="1.2.840.10008.*******.1.481.3",
            referenced_sop_instance_uid="*******.*******.9"
        )
        
        dvh = RTDVHModule.from_required_elements(
            referenced_structure_set_sequence=[ref_struct_set],
            dvh_sequence=[]
        )
        
        assert len(dvh.DVHSequence) == 0
    
    def test_optional_module_behavior(self):
        """Test that RTDVHModule behaves as optional enhancement."""
        dvh_data = [0.0, 100.0, 10.0, 90.0, 20.0, 80.0, 30.0, 70.0, 40.0, 60.0]
        dvh = self._create_basic_dvh(dvh_data)
        
        # Module should function independently
        assert len(dvh.DVHSequence) == 1
        assert dvh.dvh_count == 1
        assert dvh.total_roi_count == 1
    
    def test_validation_method_exists(self):
        """Test that validation method exists and is callable."""
        # Test with invalid data
        dvh_invalid = self._create_basic_dvh(invalid_type=True)
        
        assert hasattr(dvh_invalid, 'validate')
        assert callable(dvh_invalid.validate)
        
        # Test validation result structure
        validation_result = dvh_invalid.validate()
        assert validation_result is not None
        assert isinstance(validation_result, ValidationResult)
        assert hasattr(validation_result, 'errors')
        assert hasattr(validation_result, 'warnings')
        assert isinstance(validation_result.errors, list)
        assert isinstance(validation_result.warnings, list)

        # Test with valid data
        dvh_data = [0.0, 100.0, 10.0, 75.0, 20.0, 50.0, 30.0, 25.0, 40.0, 0.0]
        valid_dvh = self._create_basic_dvh(dvh_data)
        
        valid_result = valid_dvh.validate()
        assert valid_result is not None
        assert isinstance(valid_result, ValidationResult)
        assert hasattr(valid_result, 'errors')
        assert hasattr(valid_result, 'warnings')
        assert isinstance(valid_result.errors, list)
        assert isinstance(valid_result.warnings, list)
    
    def test_high_resolution_dvh_data(self):
        """Test high-resolution DVH data handling."""
        # High-resolution DVH with 500 dose,volume pairs (1000 total values)
        doses = np.linspace(0, 50, 500)
        volumes = np.linspace(100, 0, 500)
        high_res_dvh = []
        for dose, volume in zip(doses, volumes):
            high_res_dvh.extend([dose, volume])
        
        dvh = self._create_basic_dvh(high_res_dvh)
        
        assert dvh.DVHSequence[0].DVHNumberOfBins == 500
        assert len(dvh.DVHSequence[0].DVHData) == 1000