"""
Test MultiEnergyCTImageModule functionality.

MultiEnergyCTImageModule implements DICOM PS3.3 C.8.2.2 Multi-energy CT Image Module.
Contains attributes that describe a Multi-energy CT image with complex nested sequences.
"""

import pytest
from datetime import datetime
from pydicom import Dataset
from pyrt_dicom.modules import MultiEnergyCTImageModule
from pyrt_dicom.enums.contrast_ct_enums import MultiEnergySourceTechnique, MultiEnergyDetectorType
from pyrt_dicom.validators import ValidationResult


class TestMultiEnergyCTImageModule:
    """Test MultiEnergyCTImageModule functionality."""
    
    def test_from_required_elements_success(self):
        """Test successful creation with required elements."""
        # Create a complete acquisition sequence
        acquisition_sequence = [
            MultiEnergyCTImageModule.create_multi_energy_acquisition_item(
                x_ray_source_sequence=[
                    MultiEnergyCTImageModule.create_x_ray_source_item(
                        x_ray_source_index=1,
                        x_ray_source_id="SOURCE_001",
                        multi_energy_source_technique=MultiEnergySourceTechnique.SWITCHING_SOURCE,
                        source_start_datetime="20240101120000.000000",
                        source_end_datetime="20240101120030.000000",
                        switching_phase_number=1
                    )
                ],
                x_ray_detector_sequence=[
                    MultiEnergyCTImageModule.create_x_ray_detector_item(
                        x_ray_detector_index=1,
                        x_ray_detector_id="DETECTOR_001",
                        multi_energy_detector_type=MultiEnergyDetectorType.PHOTON_COUNTING,
                        nominal_max_energy=120.0,
                        nominal_min_energy=20.0
                    )
                ],
                path_sequence=[
                    MultiEnergyCTImageModule.create_path_item(
                        path_index=1,
                        referenced_x_ray_source_index=1,
                        referenced_x_ray_detector_index=1
                    )
                ]
            )
        ]
        
        module = MultiEnergyCTImageModule.from_required_elements(
            multi_energy_ct_acquisition_sequence=acquisition_sequence
        )
        
        assert hasattr(module, 'MultiEnergyCTAcquisitionSequence')
        assert len(module.MultiEnergyCTAcquisitionSequence) == 1
        assert module.acquisition_sequence_count == 1
    
    def test_with_optional_elements(self):
        """Test adding optional elements."""
        # Create basic module
        acquisition_sequence = [
            MultiEnergyCTImageModule.create_multi_energy_acquisition_item(
                x_ray_source_sequence=[
                    MultiEnergyCTImageModule.create_x_ray_source_item(
                        x_ray_source_index=1,
                        x_ray_source_id="SOURCE_001",
                        multi_energy_source_technique=MultiEnergySourceTechnique.CONSTANT_SOURCE,
                        source_start_datetime="20240101120000.000000",
                        source_end_datetime="20240101120030.000000"
                    )
                ],
                x_ray_detector_sequence=[
                    MultiEnergyCTImageModule.create_x_ray_detector_item(
                        x_ray_detector_index=1,
                        x_ray_detector_id="DETECTOR_001",
                        multi_energy_detector_type=MultiEnergyDetectorType.INTEGRATING
                    )
                ],
                path_sequence=[
                    MultiEnergyCTImageModule.create_path_item(
                        path_index=1,
                        referenced_x_ray_source_index=1,
                        referenced_x_ray_detector_index=1
                    )
                ]
            )
        ]
        
        module = MultiEnergyCTImageModule.from_required_elements(
            multi_energy_ct_acquisition_sequence=acquisition_sequence
        ).with_optional_elements(
            multi_energy_acquisition_description="Dual-energy CT acquisition"
        )
        
        assert module.has_acquisition_description
        assert module.MultiEnergyAcquisitionDescription == "Dual-energy CT acquisition"
    
    def test_create_x_ray_source_item_switching_source(self):
        """Test X-Ray source item creation with switching source technique."""
        source_item = MultiEnergyCTImageModule.create_x_ray_source_item(
            x_ray_source_index=1,
            x_ray_source_id="SOURCE_001",
            multi_energy_source_technique=MultiEnergySourceTechnique.SWITCHING_SOURCE,
            source_start_datetime="20240101120000.000000",
            source_end_datetime="20240101120030.000000",
            switching_phase_number=1,
            switching_phase_nominal_duration=0.5,
            switching_phase_transition_duration=0.1,
            generator_power=100.0
        )
        
        assert isinstance(source_item, Dataset)
        assert source_item.XRaySourceIndex == 1
        assert source_item.XRaySourceID == "SOURCE_001"
        assert source_item.MultiEnergySourceTechnique == "SWITCHING_SOURCE"
        assert source_item.SourceStartDateTime == "20240101120000.000000"
        assert source_item.SourceEndDateTime == "20240101120030.000000"
        assert source_item.SwitchingPhaseNumber == 1
        assert source_item.SwitchingPhaseNominalDuration == 0.5
        assert source_item.SwitchingPhaseTransitionDuration == 0.1
        assert source_item.GeneratorPower == 100.0  # Generator Power
    
    def test_create_x_ray_source_item_constant_source(self):
        """Test X-Ray source item creation with constant source technique."""
        source_item = MultiEnergyCTImageModule.create_x_ray_source_item(
            x_ray_source_index=1,
            x_ray_source_id="SOURCE_001",
            multi_energy_source_technique=MultiEnergySourceTechnique.CONSTANT_SOURCE,
            source_start_datetime="20240101120000.000000",
            source_end_datetime="20240101120030.000000"
        )
        
        assert isinstance(source_item, Dataset)
        assert source_item.XRaySourceIndex == 1
        assert source_item.MultiEnergySourceTechnique == "CONSTANT_SOURCE"
        # Switching phase number should not be present for constant source
        assert not hasattr(source_item, 'SwitchingPhaseNumber')
    
    def test_create_x_ray_detector_item_photon_counting(self):
        """Test X-Ray detector item creation with photon counting type."""
        detector_item = MultiEnergyCTImageModule.create_x_ray_detector_item(
            x_ray_detector_index=1,
            x_ray_detector_id="DETECTOR_001",
            multi_energy_detector_type=MultiEnergyDetectorType.PHOTON_COUNTING,
            nominal_max_energy=120.0,
            nominal_min_energy=20.0,
            x_ray_detector_label="Main Detector",
            effective_bin_energy=70.0
        )
        
        assert isinstance(detector_item, Dataset)
        assert detector_item.XRayDetectorIndex == 1
        assert detector_item.XRayDetectorID == "DETECTOR_001"
        assert detector_item.MultiEnergyDetectorType == "PHOTON_COUNTING"
        assert detector_item.NominalMaxEnergy == 120.0
        assert detector_item.NominalMinEnergy == 20.0
        assert detector_item.XRayDetectorLabel == "Main Detector"
        assert detector_item.EffectiveBinEnergy == 70.0
    
    def test_create_x_ray_detector_item_integrating(self):
        """Test X-Ray detector item creation with integrating type."""
        detector_item = MultiEnergyCTImageModule.create_x_ray_detector_item(
            x_ray_detector_index=1,
            x_ray_detector_id="DETECTOR_001",
            multi_energy_detector_type=MultiEnergyDetectorType.INTEGRATING
        )
        
        assert isinstance(detector_item, Dataset)
        assert detector_item.XRayDetectorIndex == 1
        assert detector_item.MultiEnergyDetectorType == "INTEGRATING"
        # Energy values may or may not be present for integrating detectors
    
    def test_create_path_item(self):
        """Test path item creation."""
        path_item = MultiEnergyCTImageModule.create_path_item(
            path_index=1,
            referenced_x_ray_source_index=1,
            referenced_x_ray_detector_index=1
        )
        
        assert isinstance(path_item, Dataset)
        assert path_item.MultiEnergyCTPathIndex == 1
        assert path_item.ReferencedXRaySourceIndex == 1
        assert path_item.ReferencedXRayDetectorIndex == 1
    
    def test_datetime_formatting(self):
        """Test datetime object handling in source creation."""
        start_datetime = datetime(2024, 1, 1, 12, 0, 0)
        end_datetime = datetime(2024, 1, 1, 12, 0, 30)
        
        source_item = MultiEnergyCTImageModule.create_x_ray_source_item(
            x_ray_source_index=1,
            x_ray_source_id="SOURCE_001",
            multi_energy_source_technique="CONSTANT_SOURCE",
            source_start_datetime=start_datetime,
            source_end_datetime=end_datetime
        )
        
        # Should format datetime objects as DICOM DT strings
        assert source_item.SourceStartDateTime.startswith("20240101120000")
        assert source_item.SourceEndDateTime.startswith("20240101120030")
    
    def test_property_methods(self):
        """Test property and helper methods."""
        # Create module with switching sources and photon counting detectors
        acquisition_sequence = [
            MultiEnergyCTImageModule.create_multi_energy_acquisition_item(
                x_ray_source_sequence=[
                    MultiEnergyCTImageModule.create_x_ray_source_item(
                        x_ray_source_index=1,
                        x_ray_source_id="SOURCE_001",
                        multi_energy_source_technique=MultiEnergySourceTechnique.SWITCHING_SOURCE,
                        source_start_datetime="20240101120000.000000",
                        source_end_datetime="20240101120030.000000",
                        switching_phase_number=1
                    ),
                    MultiEnergyCTImageModule.create_x_ray_source_item(
                        x_ray_source_index=2,
                        x_ray_source_id="SOURCE_002",
                        multi_energy_source_technique=MultiEnergySourceTechnique.CONSTANT_SOURCE,
                        source_start_datetime="20240101120000.000000",
                        source_end_datetime="20240101120030.000000"
                    )
                ],
                x_ray_detector_sequence=[
                    MultiEnergyCTImageModule.create_x_ray_detector_item(
                        x_ray_detector_index=1,
                        x_ray_detector_id="DETECTOR_001",
                        multi_energy_detector_type=MultiEnergyDetectorType.PHOTON_COUNTING,
                        nominal_max_energy=120.0,
                        nominal_min_energy=20.0
                    )
                ],
                path_sequence=[
                    MultiEnergyCTImageModule.create_path_item(
                        path_index=1,
                        referenced_x_ray_source_index=1,
                        referenced_x_ray_detector_index=1
                    ),
                    MultiEnergyCTImageModule.create_path_item(
                        path_index=2,
                        referenced_x_ray_source_index=2,
                        referenced_x_ray_detector_index=1
                    )
                ]
            )
        ]
        
        module = MultiEnergyCTImageModule.from_required_elements(
            multi_energy_ct_acquisition_sequence=acquisition_sequence
        )
        
        # Test property methods
        assert module.acquisition_sequence_count == 1
        assert module.has_switching_sources is True
        assert module.has_photon_counting_detectors is True
        assert module.get_source_count() == 2
        assert module.get_detector_count() == 1
        assert module.get_path_count() == 2
        assert module.has_acquisition_description is False
    
    def test_validate_method_exists(self):
        """Test validation method exists and returns proper structure."""
        acquisition_sequence = [
            MultiEnergyCTImageModule.create_multi_energy_acquisition_item(
                x_ray_source_sequence=[
                    MultiEnergyCTImageModule.create_x_ray_source_item(
                        x_ray_source_index=1,
                        x_ray_source_id="SOURCE_001",
                        multi_energy_source_technique=MultiEnergySourceTechnique.CONSTANT_SOURCE,
                        source_start_datetime="20240101120000.000000",
                        source_end_datetime="20240101120030.000000"
                    )
                ],
                x_ray_detector_sequence=[
                    MultiEnergyCTImageModule.create_x_ray_detector_item(
                        x_ray_detector_index=1,
                        x_ray_detector_id="DETECTOR_001",
                        multi_energy_detector_type=MultiEnergyDetectorType.INTEGRATING
                    )
                ],
                path_sequence=[
                    MultiEnergyCTImageModule.create_path_item(
                        path_index=1,
                        referenced_x_ray_source_index=1,
                        referenced_x_ray_detector_index=1
                    )
                ]
            )
        ]
        
        ct_image_module = MultiEnergyCTImageModule.from_required_elements(
            multi_energy_ct_acquisition_sequence=acquisition_sequence
        )
        
        assert hasattr(ct_image_module, 'validate')
        assert callable(ct_image_module.validate)
        
        # Test validation result structure
        validation_result = ct_image_module.validate()
        assert validation_result is not None
        assert isinstance(validation_result, ValidationResult)
        assert hasattr(validation_result, 'errors')
        assert hasattr(validation_result, 'warnings')
        assert isinstance(validation_result.errors, list)
        assert isinstance(validation_result.warnings, list)
    
    def test_empty_properties(self):
        """Test property methods with minimal module."""
        # Create module without complex features
        acquisition_sequence = [
            MultiEnergyCTImageModule.create_multi_energy_acquisition_item(
                x_ray_source_sequence=[
                    MultiEnergyCTImageModule.create_x_ray_source_item(
                        x_ray_source_index=1,
                        x_ray_source_id="SOURCE_001",
                        multi_energy_source_technique=MultiEnergySourceTechnique.CONSTANT_SOURCE,
                        source_start_datetime="20240101120000.000000",
                        source_end_datetime="20240101120030.000000"
                    )
                ],
                x_ray_detector_sequence=[
                    MultiEnergyCTImageModule.create_x_ray_detector_item(
                        x_ray_detector_index=1,
                        x_ray_detector_id="DETECTOR_001",
                        multi_energy_detector_type=MultiEnergyDetectorType.INTEGRATING
                    )
                ],
                path_sequence=[
                    MultiEnergyCTImageModule.create_path_item(
                        path_index=1,
                        referenced_x_ray_source_index=1,
                        referenced_x_ray_detector_index=1
                    )
                ]
            )
        ]
        
        module = MultiEnergyCTImageModule.from_required_elements(
            multi_energy_ct_acquisition_sequence=acquisition_sequence
        )
        
        # Test properties with no switching sources or photon counting detectors
        assert module.has_switching_sources is False
        assert module.has_photon_counting_detectors is False
