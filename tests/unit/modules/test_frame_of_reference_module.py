"""
Test FrameOfReferenceModule (M - Mandatory) functionality.

FrameOfReferenceModule implements DICOM PS3.3 C.7.4.1 Frame of Reference Module.
Required for all RTDoseIOD instances.
"""

import pytest
from pydicom.uid import generate_uid
from pyrt_dicom.modules import FrameOfReferenceModule
from pyrt_dicom.validators.modules.frame_of_reference_validator import FrameOfReferenceValidator
from pyrt_dicom.validators import ValidationResult


class TestFrameOfReferenceModule:
    """Test FrameOfReferenceModule (M - Mandatory) functionality."""
    
    def test_from_required_elements_success(self):
        """Test successful creation with required elements."""
        frame_uid = generate_uid()
        frame_ref = FrameOfReferenceModule.from_required_elements(
            frame_of_reference_uid=frame_uid
        )

        dataset = frame_ref.to_dataset()
        assert dataset.FrameOfReferenceUID == frame_uid
    
    def test_frame_uid_uniqueness(self):
        """Test frame of reference UID uniqueness."""
        frame_ref1 = FrameOfReferenceModule.from_required_elements(
            frame_of_reference_uid=generate_uid()
        )
        frame_ref2 = FrameOfReferenceModule.from_required_elements(
            frame_of_reference_uid=generate_uid()
        )

        # UIDs should be different
        dataset1 = frame_ref1.to_dataset()
        dataset2 = frame_ref2.to_dataset()
        assert dataset1.FrameOfReferenceUID != dataset2.FrameOfReferenceUID
    
    def test_with_optional_elements(self):
        """Test adding optional elements."""
        frame_ref = FrameOfReferenceModule.from_required_elements(
            frame_of_reference_uid=generate_uid(),
            position_reference_indicator="ISOCENTER"
        )

        dataset = frame_ref.to_dataset()
        assert hasattr(dataset, 'PositionReferenceIndicator')
        assert dataset.PositionReferenceIndicator == "ISOCENTER"
    
    def test_specific_frame_uid_format(self):
        """Test specific frame UID format validation."""
        # Test with specific valid UID
        specific_uid = "1.2.826.0.1.3680043.2.1125.1.12345678901234567890"
        frame_ref = FrameOfReferenceModule.from_required_elements(
            frame_of_reference_uid=specific_uid
        )

        dataset = frame_ref.to_dataset()
        assert dataset.FrameOfReferenceUID == specific_uid
    
    def test_position_reference_indicators(self):
        """Test various position reference indicator values."""
        indicators = ["ISOCENTER", "COUCH", "EXTERNAL", "PHANTOM"]

        for indicator in indicators:
            frame_ref = FrameOfReferenceModule.from_required_elements(
                frame_of_reference_uid=generate_uid(),
                position_reference_indicator=indicator
            )
            dataset = frame_ref.to_dataset()
            assert dataset.PositionReferenceIndicator == indicator
    
    def test_frame_of_reference_for_rt_dose(self):
        """Test frame of reference specific to RT dose requirements."""
        # RT Dose typically uses consistent frame of reference with planning CT
        frame_uid = generate_uid()
        frame_ref = FrameOfReferenceModule.from_required_elements(
            frame_of_reference_uid=frame_uid
        )

        # Verify UID is preserved for spatial consistency
        dataset = frame_ref.to_dataset()
        assert dataset.FrameOfReferenceUID == frame_uid
    
    def test_multiple_references_same_frame(self):
        """Test that multiple objects can reference the same frame."""
        shared_frame_uid = generate_uid()

        # Multiple modules using same frame of reference
        frame_ref1 = FrameOfReferenceModule.from_required_elements(
            frame_of_reference_uid=shared_frame_uid
        )
        frame_ref2 = FrameOfReferenceModule.from_required_elements(
            frame_of_reference_uid=shared_frame_uid
        )

        # Both should have the same frame UID
        dataset1 = frame_ref1.to_dataset()
        dataset2 = frame_ref2.to_dataset()
        assert dataset1.FrameOfReferenceUID == dataset2.FrameOfReferenceUID
        assert dataset1.FrameOfReferenceUID == shared_frame_uid
    
    def test_validation_method_exists(self):
        """Test that validation method exists and is callable."""
        frame_ref = FrameOfReferenceModule.from_required_elements(
            frame_of_reference_uid=generate_uid()
        )
        
        assert hasattr(frame_ref, 'validate')
        assert callable(frame_ref.validate)
        
        # Test validation result structure
        validation_result = frame_ref.validate()
        assert validation_result is not None
        assert isinstance(validation_result, ValidationResult)
        assert hasattr(validation_result, 'errors')
        assert hasattr(validation_result, 'warnings')
        assert isinstance(validation_result.errors, list)
        assert isinstance(validation_result.warnings, list)
    
    def test_frame_reference_consistency(self):
        """Test frame reference consistency requirements."""
        # Create frame reference for treatment planning consistency
        planning_frame_uid = generate_uid()

        frame_ref = FrameOfReferenceModule.from_required_elements(
            frame_of_reference_uid=planning_frame_uid
        )

        # Verify consistency for RT dose spatial alignment
        dataset = frame_ref.to_dataset()
        assert dataset.FrameOfReferenceUID == planning_frame_uid

    def test_to_dataset_method(self):
        """Test that to_dataset() method works correctly."""
        frame_uid = generate_uid()
        frame_ref = FrameOfReferenceModule.from_required_elements(
            frame_of_reference_uid=frame_uid,
            position_reference_indicator="STERNAL_NOTCH"
        )

        dataset = frame_ref.to_dataset()
        assert isinstance(dataset, type(frame_ref.to_dataset()))  # Should be pydicom.Dataset
        assert dataset.FrameOfReferenceUID == frame_uid
        assert dataset.PositionReferenceIndicator == "STERNAL_NOTCH"
        assert len(dataset) == 2  # Should have 2 elements

    def test_anatomical_reference_indicators(self):
        """Test anatomical reference indicators helper method."""
        indicators = FrameOfReferenceModule.create_anatomical_reference_indicators()

        assert isinstance(indicators, dict)
        assert "STERNAL_NOTCH" in indicators
        assert "ILIAC_CREST" in indicators
        assert "XIPHOID" in indicators
        assert len(indicators) > 0

    def test_slide_reference_indicator(self):
        """Test slide reference indicator helper method."""
        slide_indicator = FrameOfReferenceModule.create_slide_reference_indicator()
        assert slide_indicator == "SLIDE_CORNER"

    def test_corneal_reference_indicators(self):
        """Test corneal reference indicators helper method."""
        indicators = FrameOfReferenceModule.create_corneal_reference_indicators()

        assert isinstance(indicators, dict)
        assert "CORNEAL_VERTEX_R" in indicators
        assert "CORNEAL_VERTEX_L" in indicators
        assert len(indicators) == 2

    def test_patient_based_frame_properties(self):
        """Test patient-based frame of reference properties."""
        frame_ref = FrameOfReferenceModule.from_required_elements(
            frame_of_reference_uid=generate_uid(),
            position_reference_indicator="STERNAL_NOTCH"
        )

        assert frame_ref.is_patient_based
        assert not frame_ref.is_slide_based
        assert not frame_ref.is_corneal_based
        assert frame_ref.has_meaningful_reference
        assert frame_ref.reference_type == "PATIENT"
        assert frame_ref.corneal_eye_side is None

    def test_slide_based_frame_properties(self):
        """Test slide-based frame of reference properties."""
        frame_ref = FrameOfReferenceModule.from_required_elements(
            frame_of_reference_uid=generate_uid(),
            position_reference_indicator="SLIDE_CORNER"
        )

        assert not frame_ref.is_patient_based
        assert frame_ref.is_slide_based
        assert not frame_ref.is_corneal_based
        assert frame_ref.has_meaningful_reference
        assert frame_ref.reference_type == "SLIDE"
        assert frame_ref.corneal_eye_side is None

    def test_corneal_based_frame_properties(self):
        """Test corneal-based frame of reference properties."""
        # Test right eye
        frame_ref_r = FrameOfReferenceModule.from_required_elements(
            frame_of_reference_uid=generate_uid(),
            position_reference_indicator="CORNEAL_VERTEX_R"
        )

        assert not frame_ref_r.is_patient_based
        assert not frame_ref_r.is_slide_based
        assert frame_ref_r.is_corneal_based
        assert frame_ref_r.has_meaningful_reference
        assert frame_ref_r.reference_type == "CORNEAL"
        assert frame_ref_r.corneal_eye_side == "RIGHT"

        # Test left eye
        frame_ref_l = FrameOfReferenceModule.from_required_elements(
            frame_of_reference_uid=generate_uid(),
            position_reference_indicator="CORNEAL_VERTEX_L"
        )

        assert frame_ref_l.is_corneal_based
        assert frame_ref_l.corneal_eye_side == "LEFT"

    def test_empty_reference_indicator_properties(self):
        """Test frame of reference with empty position reference indicator."""
        frame_ref = FrameOfReferenceModule.from_required_elements(
            frame_of_reference_uid=generate_uid(),
            position_reference_indicator=""
        )

        assert not frame_ref.is_patient_based
        assert not frame_ref.is_slide_based
        assert not frame_ref.is_corneal_based
        assert not frame_ref.has_meaningful_reference
        assert frame_ref.reference_type == "EMPTY"
        assert frame_ref.corneal_eye_side is None

    def test_unknown_reference_indicator_properties(self):
        """Test frame of reference with unknown position reference indicator."""
        frame_ref = FrameOfReferenceModule.from_required_elements(
            frame_of_reference_uid=generate_uid(),
            position_reference_indicator="UNKNOWN_REFERENCE"
        )

        assert not frame_ref.is_patient_based
        assert not frame_ref.is_slide_based
        assert not frame_ref.is_corneal_based
        assert frame_ref.has_meaningful_reference
        assert frame_ref.reference_type == "UNKNOWN"
        assert frame_ref.corneal_eye_side is None

    def test_frame_compatibility(self):
        """Test frame of reference compatibility checking."""
        shared_uid = generate_uid()
        different_uid = generate_uid()

        frame_ref1 = FrameOfReferenceModule.from_required_elements(
            frame_of_reference_uid=shared_uid
        )
        frame_ref2 = FrameOfReferenceModule.from_required_elements(
            frame_of_reference_uid=shared_uid
        )
        frame_ref3 = FrameOfReferenceModule.from_required_elements(
            frame_of_reference_uid=different_uid
        )

        # Same UID should be compatible
        assert frame_ref1.is_compatible_with(frame_ref2)
        assert frame_ref2.is_compatible_with(frame_ref1)

        # Different UID should not be compatible
        assert not frame_ref1.is_compatible_with(frame_ref3)
        assert not frame_ref3.is_compatible_with(frame_ref1)

        # Non-FrameOfReferenceModule should not be compatible
        assert not frame_ref1.is_compatible_with("not a module")
        assert not frame_ref1.is_compatible_with(None)

    def test_with_optional_elements_no_args(self):
        """Test with_optional_elements method with no arguments."""
        frame_ref = FrameOfReferenceModule.from_required_elements(
            frame_of_reference_uid=generate_uid()
        )

        # Should return self for chaining
        result = frame_ref.with_optional_elements()
        assert result is frame_ref

    def test_with_optional_elements_invalid_args(self):
        """Test with_optional_elements method with invalid arguments."""
        frame_ref = FrameOfReferenceModule.from_required_elements(
            frame_of_reference_uid=generate_uid()
        )

        # Should raise ValueError for unexpected arguments
        with pytest.raises(ValueError, match="FrameOfReferenceModule has no optional elements"):
            frame_ref.with_optional_elements(invalid_arg="value")

    def test_module_properties(self):
        """Test basic module properties."""
        frame_ref = FrameOfReferenceModule.from_required_elements(
            frame_of_reference_uid=generate_uid(),
            position_reference_indicator="STERNAL_NOTCH"
        )

        assert frame_ref.module_name == "FrameOfReferenceModule"
        assert frame_ref.has_data
        assert frame_ref.get_element_count() == 2
        assert "FrameOfReferenceModule" in str(frame_ref)
        assert "elements=2" in str(frame_ref)
