"""
Test RTBrachyApplicationSetupsModule functionality.

RTBrachyApplicationSetupsModule implements DICOM PS3.3 C.8.8.15 
RT Brachy Application Setups Module.
"""

from pyrt_dicom.modules import RTBrachyApplicationSetupsModule
from pyrt_dicom.validators import ValidationResult
from pydicom import Dataset


class TestRTBrachyApplicationSetupsModule:
    """Test RTBrachyApplicationSetupsModule functionality."""
    
    def test_from_required_elements_success(self):
        """Test successful creation with required elements."""
        # Create sample source sequence item as Dataset
        source_data = RTBrachyApplicationSetupsModule.create_source_item(
            source_number=1,
            source_type="POINT",
            source_isotope_name="Ir-192",
            source_isotope_half_life=73.8,
            reference_air_kerma_rate=40800.0,
            source_strength_reference_date="20231201",
            source_strength_reference_time="120000"
        )
        source_dataset = Dataset()
        for key, value in source_data.items():
            setattr(source_dataset, key, value)
        
        # Create sample treatment machine sequence item as Dataset
        machine_data = RTBrachyApplicationSetupsModule.create_treatment_machine_item(
            treatment_machine_name="HDR Unit 1"
        )
        machine_dataset = Dataset()
        for key, value in machine_data.items():
            setattr(machine_dataset, key, value)
        
        # Create sample application setup sequence item as Dataset
        setup_data = RTBrachyApplicationSetupsModule.create_application_setup_item(
            application_setup_type="INTRACAVITARY",
            application_setup_number=1
        )
        setup_dataset = Dataset()
        for key, value in setup_data.items():
            setattr(setup_dataset, key, value)
        
        module = RTBrachyApplicationSetupsModule.from_required_elements(
            brachy_treatment_technique="HDR",
            brachy_treatment_type="ISOCENTRIC",
            treatment_machine_sequence=[machine_dataset],
            source_sequence=[source_dataset],
            application_setup_sequence=[setup_dataset]
        )
        
        assert module.BrachyTreatmentTechnique == "HDR"
        assert module.BrachyTreatmentType == "ISOCENTRIC"
        assert len(module.TreatmentMachineSequence) == 1
        assert len(module.SourceSequence) == 1
        assert len(module.ApplicationSetupSequence) == 1
    
    def test_required_elements_validation(self):
        """Test validation of required elements."""
        # Test minimal creation with required fields
        module = RTBrachyApplicationSetupsModule.from_required_elements(
            brachy_treatment_technique="",
            brachy_treatment_type="",
            treatment_machine_sequence=[],
            source_sequence=[],
            application_setup_sequence=[]
        )
        
        # Should be able to create with empty values
        assert module.BrachyTreatmentTechnique == ""
        assert module.BrachyTreatmentType == ""
        assert len(module.TreatmentMachineSequence) == 0
        assert len(module.SourceSequence) == 0
        assert len(module.ApplicationSetupSequence) == 0
    
    def test_with_optional_elements(self):
        """Test adding optional elements."""
        module = RTBrachyApplicationSetupsModule.from_required_elements(
            brachy_treatment_technique="HDR",
            brachy_treatment_type="ISOCENTRIC",
            treatment_machine_sequence=[],
            source_sequence=[],
            application_setup_sequence=[]
        ).with_optional_elements()
        
        # Should return self for method chaining
        assert isinstance(module, RTBrachyApplicationSetupsModule)
    
    def test_create_source_item(self):
        """Test source item creation."""
        source = RTBrachyApplicationSetupsModule.create_source_item(
            source_number=1,
            source_type="POINT",
            source_isotope_name="Ir-192",
            source_isotope_half_life=73.8,
            reference_air_kerma_rate=40800.0,
            source_strength_reference_date="20231201",
            source_strength_reference_time="120000"
        )
        
        assert source['SourceNumber'] == 1
        assert source['SourceType'] == "POINT"
        assert source['SourceIsotopeName'] == "Ir-192"
        assert source['SourceIsotopeHalfLife'] == 73.8
        assert source['ReferenceAirKermaRate'] == 40800.0
        assert source['SourceStrengthReferenceDate'] == "20231201"
        assert source['SourceStrengthReferenceTime'] == "120000"
    
    def test_create_source_item_with_optional_elements(self):
        """Test source item creation with optional elements."""
        source = RTBrachyApplicationSetupsModule.create_source_item(
            source_number=1,
            source_type="POINT",
            source_isotope_name="Ir-192",
            source_isotope_half_life=73.8,
            reference_air_kerma_rate=40800.0,
            source_strength_reference_date="20231201",
            source_strength_reference_time="120000",
            source_serial_number="SN123456",
            source_model_id="MODEL_A",
            source_description="HDR Source",
            source_manufacturer="ACME Corp"
        )
        
        assert source['SourceSerialNumber'] == "SN123456"
        assert source['SourceModelID'] == "MODEL_A"
        assert source['SourceDescription'] == "HDR Source"
        assert source['SourceManufacturer'] == "ACME Corp"
    
    def test_create_treatment_machine_item(self):
        """Test treatment machine item creation."""
        machine = RTBrachyApplicationSetupsModule.create_treatment_machine_item(
            treatment_machine_name="HDR Unit 1"
        )
        
        assert machine['TreatmentMachineName'] == "HDR Unit 1"
    
    def test_create_treatment_machine_item_with_optional_elements(self):
        """Test treatment machine item creation with optional elements."""
        machine = RTBrachyApplicationSetupsModule.create_treatment_machine_item(
            treatment_machine_name="HDR Unit 1",
            manufacturer="ACME Corp",
            institution_name="General Hospital",
            manufacturers_model_name="HDR-2000"
        )
        
        assert machine['TreatmentMachineName'] == "HDR Unit 1"
        assert machine['Manufacturer'] == "ACME Corp"
        assert machine['InstitutionName'] == "General Hospital"
        assert machine['ManufacturerModelName'] == "HDR-2000"
    
    def test_create_application_setup_item(self):
        """Test application setup item creation."""
        setup = RTBrachyApplicationSetupsModule.create_application_setup_item(
            application_setup_type="INTRACAVITARY",
            application_setup_number=1
        )
        
        assert setup['ApplicationSetupType'] == "INTRACAVITARY"
        assert setup['ApplicationSetupNumber'] == 1
    
    def test_create_application_setup_item_with_optional_elements(self):
        """Test application setup item creation with optional elements."""
        setup = RTBrachyApplicationSetupsModule.create_application_setup_item(
            application_setup_type="INTRACAVITARY",
            application_setup_number=1,
            application_setup_name="Prostate Implant",
            total_reference_air_kerma=1000.0
        )
        
        assert setup['ApplicationSetupType'] == "INTRACAVITARY"
        assert setup['ApplicationSetupNumber'] == 1
        assert setup['ApplicationSetupName'] == "Prostate Implant"
        assert setup['TotalReferenceAirKerma'] == 1000.0
    
    def test_has_brachy_setups_property(self):
        """Test has_brachy_setups property."""
        # Test without setup sequence
        module = RTBrachyApplicationSetupsModule.from_required_elements(
            brachy_treatment_technique="HDR",
            brachy_treatment_type="ISOCENTRIC",
            treatment_machine_sequence=[],
            source_sequence=[],
            application_setup_sequence=[]
        )
        
        # The property checks for BrachyApplicationSetupSequence, not ApplicationSetupSequence
        assert not module.has_brachy_setups
        
        # Add the sequence and test again
        module.BrachyApplicationSetupSequence = [{'BrachyApplicationSetupNumber': 1}]
        assert module.has_brachy_setups
    
    def test_brachy_setup_count_property(self):
        """Test brachy_setup_count property."""
        module = RTBrachyApplicationSetupsModule.from_required_elements(
            brachy_treatment_technique="HDR",
            brachy_treatment_type="ISOCENTRIC",
            treatment_machine_sequence=[],
            source_sequence=[],
            application_setup_sequence=[]
        )
        
        # Initially no setups
        assert module.brachy_setup_count == 0
        
        # Add some setups
        module.BrachyApplicationSetupSequence = [
            {'BrachyApplicationSetupNumber': 1},
            {'BrachyApplicationSetupNumber': 2}
        ]
        assert module.brachy_setup_count == 2
    
    def test_get_brachy_setup_numbers(self):
        """Test get_brachy_setup_numbers method."""
        module = RTBrachyApplicationSetupsModule.from_required_elements(
            brachy_treatment_technique="HDR",
            brachy_treatment_type="ISOCENTRIC",
            treatment_machine_sequence=[],
            source_sequence=[],
            application_setup_sequence=[]
        )
        
        # Initially no setup numbers
        assert module.get_brachy_setup_numbers() == []
        
        # Add some setups
        module.BrachyApplicationSetupSequence = [
            {'BrachyApplicationSetupNumber': 1},
            {'BrachyApplicationSetupNumber': 3},
            {'BrachyApplicationSetupNumber': 2}
        ]
        numbers = module.get_brachy_setup_numbers()
        assert len(numbers) == 3
        assert 1 in numbers
        assert 2 in numbers
        assert 3 in numbers
    
    def test_get_brachy_setup_by_number(self):
        """Test get_brachy_setup_by_number method."""
        module = RTBrachyApplicationSetupsModule.from_required_elements(
            brachy_treatment_technique="HDR",
            brachy_treatment_type="ISOCENTRIC",
            treatment_machine_sequence=[],
            source_sequence=[],
            application_setup_sequence=[]
        )
        
        # Add some setups
        setup1 = {'BrachyApplicationSetupNumber': 1, 'BrachyApplicationSetupName': 'Setup 1'}
        setup2 = {'BrachyApplicationSetupNumber': 2, 'BrachyApplicationSetupName': 'Setup 2'}
        module.BrachyApplicationSetupSequence = [setup1, setup2]
        
        # Test finding existing setup
        found_setup = module.get_brachy_setup_by_number(1)
        assert found_setup is not None
        assert found_setup['BrachyApplicationSetupName'] == 'Setup 1'
        
        # Test finding non-existent setup
        not_found = module.get_brachy_setup_by_number(99)
        assert not_found is None
    
    def test_validation_method_exists(self):
        """Test that validation method exists and is callable."""
        module = RTBrachyApplicationSetupsModule.from_required_elements(
            brachy_treatment_technique="HDR",
            brachy_treatment_type="ISOCENTRIC",
            treatment_machine_sequence=[],
            source_sequence=[],
            application_setup_sequence=[]
        )
        
        assert hasattr(module, 'validate')
        assert callable(module.validate)
        
        # Test validation result structure
        validation_result = module.validate()
        assert validation_result is not None
        assert isinstance(validation_result, ValidationResult)
        assert hasattr(validation_result, 'errors')
        assert hasattr(validation_result, 'warnings')
        assert isinstance(validation_result.errors, list)
        assert isinstance(validation_result.warnings, list)