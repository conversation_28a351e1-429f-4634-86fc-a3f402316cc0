"""
Test GeneralSeriesModule functionality.

GeneralSeriesModule implements DICOM PS3.3 C.7.3.1 General Series Module.
Contains attributes that identify and describe the Series performed as part of a Study.
"""

import pydicom
from pyrt_dicom.modules import GeneralSeriesModule
from pyrt_dicom.enums.series_enums import Modality, Laterality, PatientPosition, AnatomicalOrientationType
from pyrt_dicom.validators import ValidationResult


class TestGeneralSeriesModule:
    """Test GeneralSeriesModule functionality."""
    
    def test_from_required_elements_success(self):
        """Test successful creation with required elements."""
        series = GeneralSeriesModule.from_required_elements(
            modality=Modality.CT,
            series_instance_uid="*******.*******.9.10",
            series_number=1
        )
        
        dataset = series.to_dataset()
        assert dataset.Modality == "CT"
        assert dataset.SeriesInstanceUID == "*******.*******.9.10"
        assert dataset.SeriesNumber == 1
    
    def test_required_elements_type2(self):
        """Test creation with Type 2 elements (required but can be empty)."""
        # SeriesNumber is Type 2 - required but can be None
        series = GeneralSeriesModule.from_required_elements(
            modality=Modality.MR,
            series_instance_uid="*******.*******.9.11",
            series_number=None
        )
        
        dataset = series.to_dataset()
        assert dataset.Modality == "MR"
        assert dataset.SeriesInstanceUID == "*******.*******.9.11"
        assert dataset.SeriesNumber is None
    
    def test_modality_enum_values(self):
        """Test all valid modality enum values."""
        modalities = [Modality.CT, Modality.MR, Modality.RTDOSE, Modality.RTSTRUCT]
        
        for modality in modalities:
            series = GeneralSeriesModule.from_required_elements(
                modality=modality,
                series_instance_uid="*******.*******.9.12",
                series_number=1
            )
            dataset = series.to_dataset()
            assert dataset.Modality == modality.value
    
    def test_modality_string_values(self):
        """Test modality as string value."""
        series = GeneralSeriesModule.from_required_elements(
            modality="US",
            series_instance_uid="*******.*******.9.13",
            series_number=2
        )
        
        dataset = series.to_dataset()
        assert dataset.Modality == "US"
    
    def test_with_optional_elements(self):
        """Test adding optional elements."""
        series = GeneralSeriesModule.from_required_elements(
            modality=Modality.CT,
            series_instance_uid="*******.*******.9.14",
            series_number=3
        ).with_optional_elements(
            series_description="Chest CT with contrast",
            protocol_name="CHEST_CT_PROTOCOL",
            body_part_examined="CHEST"
        )
        
        dataset = series.to_dataset()
        assert hasattr(dataset, 'SeriesDescription')
        assert hasattr(dataset, 'ProtocolName')
        assert hasattr(dataset, 'BodyPartExamined')
        assert dataset.SeriesDescription == "Chest CT with contrast"
        assert dataset.ProtocolName == "CHEST_CT_PROTOCOL"
        assert dataset.BodyPartExamined == "CHEST"
    
    def test_with_patient_positioning(self):
        """Test patient positioning information."""
        series = GeneralSeriesModule.from_required_elements(
            modality=Modality.CT,
            series_instance_uid="*******.*******.9.15",
            series_number=4
        ).with_patient_positioning(
            patient_position=PatientPosition.HFS
        )
        
        dataset = series.to_dataset()
        assert hasattr(dataset, 'PatientPosition')
        assert dataset.PatientPosition == "HFS"
    
    def test_with_operator_information(self):
        """Test operator and performing physician information."""
        series = GeneralSeriesModule.from_required_elements(
            modality=Modality.MR,
            series_instance_uid="*******.*******.9.16",
            series_number=5
        ).with_operator_information(
            operators_name="Tech^John",
            performing_physicians_name="Dr^Smith"
        )
        
        dataset = series.to_dataset()
        assert hasattr(dataset, 'OperatorsName')
        assert hasattr(dataset, 'PerformingPhysicianName')
        assert dataset.OperatorsName == "Tech^John"
        assert dataset.PerformingPhysicianName == "Dr^Smith"
    
    def test_property_methods(self):
        """Test property check methods."""
        series = GeneralSeriesModule.from_required_elements(
            modality=Modality.CT,
            series_instance_uid="*******.*******.9.17",
            series_number=6
        )
        
        # Initially no optional info
        assert not series.has_operator_info
        assert not series.has_performing_physician_info
        assert not series.has_positioning_info
        assert not series.has_pixel_value_info
        
        # Add operator info
        series.with_operator_information(operators_name="Tech^Jane")
        assert series.has_operator_info
        
        # Add positioning info
        series.with_patient_positioning(patient_position=PatientPosition.HFP)
        assert series.has_positioning_info
        
        # Add pixel value info
        series.with_optional_elements(
            smallest_pixel_value_in_series=0,
            largest_pixel_value_in_series=4095
        )
        assert series.has_pixel_value_info
    
    def test_create_related_series_item(self):
        """Test creation of related series sequence item."""
        from pydicom import Dataset
        
        # Create proper Dataset objects for the sequence
        purpose_code_item = Dataset()
        purpose_code_item.CodeValue = "121322"
        purpose_code_item.CodeMeaning = "Source image for image processing operation"
        purpose_code = [purpose_code_item]
        
        item = GeneralSeriesModule.create_related_series_item(
            study_instance_uid="*******.*******.9.20",
            series_instance_uid="*******.*******.9.21",
            purpose_of_reference_code_sequence=purpose_code
        )
        
        assert hasattr(item, 'StudyInstanceUID')
        assert hasattr(item, 'SeriesInstanceUID')
        assert hasattr(item, 'PurposeOfReferenceCodeSequence')
        assert item.StudyInstanceUID == "*******.*******.9.20"
        assert item.SeriesInstanceUID == "*******.*******.9.21"
        assert len(item.PurposeOfReferenceCodeSequence) == 1
        assert item.PurposeOfReferenceCodeSequence[0].CodeValue == "121322"
    
    def test_create_referenced_performed_procedure_step_item(self):
        """Test creation of referenced performed procedure step sequence item."""
        item = GeneralSeriesModule.create_referenced_performed_procedure_step_item(
            referenced_sop_class_uid="1.2.840.10008.3.1.2.3.1",
            referenced_sop_instance_uid="*******.*******.9.22"
        )
        
        assert hasattr(item, 'ReferencedSOPClassUID')
        assert hasattr(item, 'ReferencedSOPInstanceUID')
        assert item.ReferencedSOPClassUID == "1.2.840.10008.3.1.2.3.1"
        assert item.ReferencedSOPInstanceUID == "*******.*******.9.22"
    
    def test_validation_method_exists(self):
        """Test that validation method exists and is callable."""
        series = GeneralSeriesModule.from_required_elements(
            modality=Modality.CT,
            series_instance_uid="*******.*******.9.18",
            series_number=7
        )
        
        assert hasattr(series, 'validate')
        assert callable(series.validate)
        
        # Test validation result structure
        validation_result = series.validate()
        assert validation_result is not None
        assert isinstance(validation_result, ValidationResult)
        assert hasattr(validation_result, 'errors')
        assert hasattr(validation_result, 'warnings')
        assert isinstance(validation_result.errors, list)
        assert isinstance(validation_result.warnings, list)
    
    def test_invalid_modality_handling(self):
        """Test handling of invalid modality values."""
        # Should be able to create with invalid modality value
        series = GeneralSeriesModule.from_required_elements(
            modality="INVALID",
            series_instance_uid="*******.*******.9.19",
            series_number=8
        )
        
        dataset = series.to_dataset()
        assert dataset.Modality == "INVALID"
        
        # Test that validation method can be called with invalid data
        validation_result = series.validate()
        assert validation_result is not None
        assert hasattr(validation_result, 'errors')
        assert hasattr(validation_result, 'warnings')
    
    def test_to_dataset_method(self):
        """Test the to_dataset() method functionality."""
        series = GeneralSeriesModule.from_required_elements(
            modality=Modality.CT,
            series_instance_uid="*******.*******.9.20",
            series_number=1
        ).with_optional_elements(
            series_description="Test Dataset"
        )
        
        dataset = series.to_dataset()
        assert isinstance(dataset, pydicom.Dataset)
        assert len(dataset) > 0
        assert dataset.Modality == "CT"
        assert dataset.SeriesInstanceUID == "*******.*******.9.20"
        assert dataset.SeriesNumber == 1
        assert dataset.SeriesDescription == "Test Dataset"
    
    def test_with_laterality_information(self):
        """Test laterality information for paired body parts."""
        series = GeneralSeriesModule.from_required_elements(
            modality=Modality.MG,
            series_instance_uid="*******.*******.9.21",
            series_number=1
        ).with_laterality_information(
            laterality=Laterality.LEFT,
            body_part_examined="BREAST"
        )
        
        dataset = series.to_dataset()
        assert hasattr(dataset, 'Laterality')
        assert hasattr(dataset, 'BodyPartExamined')
        assert dataset.Laterality == "L"
        assert dataset.BodyPartExamined == "BREAST"
    
    def test_with_anatomical_orientation(self):
        """Test anatomical orientation for non-human organisms."""
        series = GeneralSeriesModule.from_required_elements(
            modality=Modality.CT,
            series_instance_uid="*******.*******.9.22",
            series_number=1
        ).with_anatomical_orientation(
            anatomical_orientation_type=AnatomicalOrientationType.QUADRUPED,
            is_non_human=True
        )
        
        dataset = series.to_dataset()
        assert hasattr(dataset, 'AnatomicalOrientationType')
        assert dataset.AnatomicalOrientationType == "QUADRUPED"
    
    def test_conditional_validation_errors(self):
        """Test that conditional validation errors are raised appropriately."""
        import pytest
        
        # Test laterality requirement error for paired body parts
        with pytest.raises(ValueError, match="Laterality.*is required.*Type 2C"):
            GeneralSeriesModule.from_required_elements(
                modality=Modality.MG,
                series_instance_uid="*******.*******.9.23",
                series_number=1
            ).with_laterality_information(
                laterality=None,  # This should trigger error
                body_part_examined="BREAST"
            )
        
        # Test anatomical orientation requirement error for non-human
        with pytest.raises(ValueError, match="Anatomical Orientation Type.*is required.*Type 1C"):
            GeneralSeriesModule.from_required_elements(
                modality=Modality.CT,
                series_instance_uid="*******.*******.9.24",
                series_number=1
            ).with_anatomical_orientation(
                anatomical_orientation_type=None,  # This should trigger error
                is_non_human=True
            )
    
    def test_new_property_methods(self):
        """Test new property check methods."""
        series = GeneralSeriesModule.from_required_elements(
            modality=Modality.CT,
            series_instance_uid="*******.*******.9.25",
            series_number=1
        )
        
        # Test is_configured
        assert series.is_configured
        
        # Test requires_patient_position (should be False without SOP Class UID)
        assert not series.requires_patient_position
        
        # Test requires_laterality (should be False without paired body part)
        assert not series.requires_laterality