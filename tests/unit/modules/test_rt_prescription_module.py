"""
Test RTPrescriptionModule (Type 3) functionality.

RTPrescriptionModule implements DICOM PS3.3 C.8.8.10 RT Prescription Module.
Contains prescription information for radiotherapy treatment.
"""

import pytest
from pyrt_dicom.modules import RTPrescriptionModule
from pyrt_dicom.enums.rt_enums import DoseReferenceStructureType, DoseReferenceType, DoseValuePurpose, DoseValueInterpretation
from pyrt_dicom.validators import ValidationResult


class TestRTPrescriptionModule:
    """Test RTPrescriptionModule (Type 3) functionality."""
    
    def test_from_required_elements_success(self):
        """Test successful creation with no required elements."""
        prescription = RTPrescriptionModule.from_required_elements()
        
        # Should create an empty module since all elements are Type 3 (optional)
        assert prescription is not None
        assert isinstance(prescription, RTPrescriptionModule)
        assert not hasattr(prescription, 'PrescriptionDescription')
        assert not hasattr(prescription, 'DoseReferenceSequence')
    
    def test_with_optional_elements_prescription_description(self):
        """Test adding prescription description optional element."""
        prescription = RTPrescriptionModule.from_required_elements().with_optional_elements(
            prescription_description="Primary treatment prescription"
        )
        
        assert hasattr(prescription, 'PrescriptionDescription')
        assert prescription.PrescriptionDescription == "Primary treatment prescription"
    
    def test_with_optional_elements_dose_reference_sequence(self):
        """Test adding dose reference sequence optional element."""
        dose_ref_item = RTPrescriptionModule.create_dose_reference_item(
            dose_reference_number=1,
            dose_reference_structure_type=DoseReferenceStructureType.POINT,
            dose_reference_type=DoseReferenceType.TARGET,
            referenced_roi_number=1
        )
        
        prescription = RTPrescriptionModule.from_required_elements().with_optional_elements(
            dose_reference_sequence=[dose_ref_item]
        )
        
        assert hasattr(prescription, 'DoseReferenceSequence')
        assert len(prescription.DoseReferenceSequence) == 1
        assert prescription.DoseReferenceSequence[0].DoseReferenceNumber == 1
    
    def test_create_dose_reference_item_point_type(self):
        """Test creating dose reference item with POINT structure type."""
        dose_ref_item = RTPrescriptionModule.create_dose_reference_item(
            dose_reference_number=1,
            dose_reference_structure_type=DoseReferenceStructureType.POINT,
            dose_reference_type=DoseReferenceType.TARGET,
            referenced_roi_number=1,
            target_prescription_dose=200.0
        )
        
        assert dose_ref_item.DoseReferenceNumber == 1
        assert dose_ref_item.DoseReferenceStructureType == "POINT"
        assert dose_ref_item.DoseReferenceType == "TARGET"
        assert dose_ref_item.ReferencedROINumber == 1
        assert dose_ref_item.TargetPrescriptionDose == 200.0
    
    def test_create_dose_reference_item_volume_type(self):
        """Test creating dose reference item with VOLUME structure type."""
        dose_ref_item = RTPrescriptionModule.create_dose_reference_item(
            dose_reference_number=2,
            dose_reference_structure_type=DoseReferenceStructureType.VOLUME,
            dose_reference_type=DoseReferenceType.ORGAN_AT_RISK,
            referenced_roi_number=2,
            organ_at_risk_maximum_dose=1000.0
        )
        
        assert dose_ref_item.DoseReferenceNumber == 2
        assert dose_ref_item.DoseReferenceStructureType == "VOLUME"
        assert dose_ref_item.DoseReferenceType == "ORGAN_AT_RISK"
        assert dose_ref_item.ReferencedROINumber == 2
        assert dose_ref_item.OrganAtRiskMaximumDose == 1000.0
    
    def test_create_dose_reference_item_coordinates_type(self):
        """Test creating dose reference item with COORDINATES structure type."""
        dose_ref_item = RTPrescriptionModule.create_dose_reference_item(
            dose_reference_number=3,
            dose_reference_structure_type=DoseReferenceStructureType.COORDINATES,
            dose_reference_type=DoseReferenceType.TARGET,
            dose_reference_point_coordinates=[100.0, 200.0, 300.0]
        )
        
        assert dose_ref_item.DoseReferenceNumber == 3
        assert dose_ref_item.DoseReferenceStructureType == "COORDINATES"
        assert dose_ref_item.DoseReferenceType == "TARGET"
        assert dose_ref_item.DoseReferencePointCoordinates == [100.0, 200.0, 300.0]
    
    def test_create_dose_reference_item_conditional_validation(self):
        """Test conditional validation for dose reference items."""
        # Test error when POINT type without referenced_roi_number
        with pytest.raises(ValueError, match="Referenced ROI Number is required"):
            RTPrescriptionModule.create_dose_reference_item(
                dose_reference_number=1,
                dose_reference_structure_type=DoseReferenceStructureType.POINT,
                dose_reference_type=DoseReferenceType.TARGET
            )
        
        # Test error when COORDINATES type without coordinates
        with pytest.raises(ValueError, match="Dose Reference Point Coordinates is required"):
            RTPrescriptionModule.create_dose_reference_item(
                dose_reference_number=1,
                dose_reference_structure_type=DoseReferenceStructureType.COORDINATES,
                dose_reference_type=DoseReferenceType.TARGET
            )
    
    def test_has_prescription_property(self):
        """Test has_prescription property."""
        # Empty prescription
        prescription = RTPrescriptionModule.from_required_elements()
        assert not prescription.has_prescription
        
        # With prescription description
        prescription.with_optional_elements(prescription_description="Test prescription")
        assert prescription.has_prescription
        
        # With dose reference sequence
        empty_prescription = RTPrescriptionModule.from_required_elements()
        dose_ref_item = RTPrescriptionModule.create_dose_reference_item(
            dose_reference_number=1,
            dose_reference_structure_type=DoseReferenceStructureType.POINT,
            dose_reference_type=DoseReferenceType.TARGET,
            referenced_roi_number=1
        )
        empty_prescription.with_optional_elements(dose_reference_sequence=[dose_ref_item])
        assert empty_prescription.has_prescription
    
    def test_dose_reference_count_property(self):
        """Test dose_reference_count property."""
        prescription = RTPrescriptionModule.from_required_elements()
        assert prescription.dose_reference_count == 0
        
        dose_ref_item1 = RTPrescriptionModule.create_dose_reference_item(
            dose_reference_number=1,
            dose_reference_structure_type=DoseReferenceStructureType.POINT,
            dose_reference_type=DoseReferenceType.TARGET,
            referenced_roi_number=1
        )
        dose_ref_item2 = RTPrescriptionModule.create_dose_reference_item(
            dose_reference_number=2,
            dose_reference_structure_type=DoseReferenceStructureType.VOLUME,
            dose_reference_type=DoseReferenceType.ORGAN_AT_RISK,
            referenced_roi_number=2
        )
        
        prescription.with_optional_elements(dose_reference_sequence=[dose_ref_item1, dose_ref_item2])
        assert prescription.dose_reference_count == 2
    
    def test_get_dose_reference_types(self):
        """Test get_dose_reference_types method."""
        prescription = RTPrescriptionModule.from_required_elements()
        
        # Empty sequence
        assert prescription.get_dose_reference_types() == []
        
        # With mixed types
        target_item = RTPrescriptionModule.create_dose_reference_item(
            dose_reference_number=1,
            dose_reference_structure_type=DoseReferenceStructureType.POINT,
            dose_reference_type=DoseReferenceType.TARGET,
            referenced_roi_number=1
        )
        oar_item = RTPrescriptionModule.create_dose_reference_item(
            dose_reference_number=2,
            dose_reference_structure_type=DoseReferenceStructureType.VOLUME,
            dose_reference_type=DoseReferenceType.ORGAN_AT_RISK,
            referenced_roi_number=2
        )
        
        prescription.with_optional_elements(dose_reference_sequence=[target_item, oar_item])
        reference_types = prescription.get_dose_reference_types()
        assert "TARGET" in reference_types
        assert "ORGAN_AT_RISK" in reference_types
        assert len(reference_types) == 2
    
    def test_get_target_dose_references(self):
        """Test get_target_dose_references method."""
        target_item = RTPrescriptionModule.create_dose_reference_item(
            dose_reference_number=1,
            dose_reference_structure_type=DoseReferenceStructureType.POINT,
            dose_reference_type=DoseReferenceType.TARGET,
            referenced_roi_number=1
        )
        oar_item = RTPrescriptionModule.create_dose_reference_item(
            dose_reference_number=2,
            dose_reference_structure_type=DoseReferenceStructureType.VOLUME,
            dose_reference_type=DoseReferenceType.ORGAN_AT_RISK,
            referenced_roi_number=2
        )
        
        prescription = RTPrescriptionModule.from_required_elements().with_optional_elements(
            dose_reference_sequence=[target_item, oar_item]
        )
        
        target_refs = prescription.get_target_dose_references()
        assert len(target_refs) == 1
        assert target_refs[0].DoseReferenceType == "TARGET"
        assert target_refs[0].DoseReferenceNumber == 1
    
    def test_get_organ_at_risk_dose_references(self):
        """Test get_organ_at_risk_dose_references method."""
        target_item = RTPrescriptionModule.create_dose_reference_item(
            dose_reference_number=1,
            dose_reference_structure_type=DoseReferenceStructureType.POINT,
            dose_reference_type=DoseReferenceType.TARGET,
            referenced_roi_number=1
        )
        oar_item = RTPrescriptionModule.create_dose_reference_item(
            dose_reference_number=2,
            dose_reference_structure_type=DoseReferenceStructureType.VOLUME,
            dose_reference_type=DoseReferenceType.ORGAN_AT_RISK,
            referenced_roi_number=2
        )
        
        prescription = RTPrescriptionModule.from_required_elements().with_optional_elements(
            dose_reference_sequence=[target_item, oar_item]
        )
        
        oar_refs = prescription.get_organ_at_risk_dose_references()
        assert len(oar_refs) == 1
        assert oar_refs[0].DoseReferenceType == "ORGAN_AT_RISK"
        assert oar_refs[0].DoseReferenceNumber == 2
    
    def test_validation_method_exists(self):
        """Test that validation method exists and is callable."""
        prescription = RTPrescriptionModule.from_required_elements()
        
        assert hasattr(prescription, 'validate')
        assert callable(prescription.validate)
        
        # Test validation result structure
        validation_result = prescription.validate()
        assert validation_result is not None
        assert isinstance(validation_result, ValidationResult)
        assert hasattr(validation_result, 'errors')
        assert hasattr(validation_result, 'warnings')
        assert isinstance(validation_result.errors, list)
        assert isinstance(validation_result.warnings, list)
    
    
    def test_string_enum_values(self):
        """Test using string values instead of enum objects."""
        dose_ref_item = RTPrescriptionModule.create_dose_reference_item(
            dose_reference_number=1,
            dose_reference_structure_type="POINT",  # String instead of enum
            dose_reference_type="TARGET",           # String instead of enum
            referenced_roi_number=1
        )
        
        assert dose_ref_item.DoseReferenceStructureType == "POINT"
        assert dose_ref_item.DoseReferenceType == "TARGET"
    
    def test_method_chaining(self):
        """Test method chaining functionality."""
        prescription = (RTPrescriptionModule.from_required_elements()
                       .with_optional_elements(prescription_description="Test prescription"))
        
        assert hasattr(prescription, 'PrescriptionDescription')
        assert prescription.PrescriptionDescription == "Test prescription"