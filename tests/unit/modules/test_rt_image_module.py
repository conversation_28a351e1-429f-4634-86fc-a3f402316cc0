"""
Test RTImageModule functionality.

RTImageModule implements DICOM PS3.3 C.8.8.2 RT Image Module.
Describes RT-specific characteristics of a projection image.
"""

import pytest
from pyrt_dicom.modules import RTImageModule
from pyrt_dicom.enums.rt_enums import (
    RTImagePlane, PrimaryDosimeterUnit, PixelIntensityRelationshipSign,
    RTImageTypeValue3, RTBeamLimitingDeviceType, BlockType, BlockDivergence,
    BlockMountingPosition, FluenceDataSource, EnhancedRTBeamLimitingDeviceDefinitionFlag
)
from pyrt_dicom.enums.image_enums import PhotometricInterpretation, PixelRepresentation
from pyrt_dicom.enums.series_enums import PatientPosition
from pyrt_dicom.validators import ValidationResult


class TestRTImageModule:
    """Test RTImageModule functionality."""
    
    def test_from_required_elements_success(self):
        """Test successful creation with required elements."""
        rt_image = RTImageModule.from_required_elements(
            samples_per_pixel=1,
            photometric_interpretation=PhotometricInterpretation.MONOCHROME2,
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            pixel_representation=PixelRepresentation.UNSIGNED,
            rt_image_label="Portal Image 1",
            image_type=["ORIGINAL", "PRIMARY", "PORTAL"],
            rt_image_plane=RTImagePlane.NORMAL
        )
        
        assert rt_image.SamplesPerPixel == 1
        assert rt_image.PhotometricInterpretation == "MONOCHROME2"
        assert rt_image.BitsAllocated == 16
        assert rt_image.BitsStored == 16
        assert rt_image.HighBit == 15
        assert rt_image.PixelRepresentation == 0  # UNSIGNED
        assert rt_image.RTImageLabel == "Portal Image 1"
        assert rt_image.ImageType == ["ORIGINAL", "PRIMARY", "PORTAL"]
        assert rt_image.RTImagePlane == "NORMAL"
    
    def test_required_elements_with_defaults(self):
        """Test creation with default empty values for Type 2 elements."""
        rt_image = RTImageModule.from_required_elements(
            samples_per_pixel=1,
            photometric_interpretation=PhotometricInterpretation.MONOCHROME2,
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            pixel_representation=PixelRepresentation.UNSIGNED,
            rt_image_label="Test Image",
            image_type=["ORIGINAL", "PRIMARY", "PORTAL"]
        )
        
        # Type 2 element should be empty string
        assert rt_image.ConversionType == ""
    
    def test_with_optional_elements(self):
        """Test adding optional elements."""
        rt_image = RTImageModule.from_required_elements(
            samples_per_pixel=1,
            photometric_interpretation=PhotometricInterpretation.MONOCHROME2,
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            pixel_representation=PixelRepresentation.UNSIGNED,
            rt_image_label="Test Image",
            image_type=["ORIGINAL", "PRIMARY", "PORTAL"]
        ).with_optional_elements(
            rt_image_name="Portal verification image",
            rt_image_description="Verification image for beam 1",
            radiation_machine_name="Varian TrueBeam",
            primary_dosimeter_unit=PrimaryDosimeterUnit.MU,
            radiation_machine_sad=1000.0,
            rt_image_sid=1500.0
        )
        
        assert rt_image.RTImageName == "Portal verification image"
        assert rt_image.RTImageDescription == "Verification image for beam 1"
        assert rt_image.RadiationMachineName == "Varian TrueBeam"
        assert rt_image.PrimaryDosimeterUnit == "MU"
        assert rt_image.RadiationMachineSAD == 1000.0
        assert rt_image.RTImageSID == 1500.0
    
    def test_with_pixel_intensity_relationship_sign(self):
        """Test adding pixel intensity relationship sign."""
        rt_image = RTImageModule.from_required_elements(
            samples_per_pixel=1,
            photometric_interpretation=PhotometricInterpretation.MONOCHROME2,
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            pixel_representation=PixelRepresentation.UNSIGNED,
            rt_image_label="Test Image",
            image_type=["ORIGINAL", "PRIMARY", "PORTAL"]
        ).with_optional_elements(
            pixel_intensity_relationship="LIN"
        ).with_pixel_intensity_relationship_sign(
            pixel_intensity_relationship_sign=PixelIntensityRelationshipSign.POSITIVE
        )
        
        assert rt_image.PixelIntensityRelationship == "LIN"
        assert rt_image.PixelIntensityRelationshipSign == 1  # POSITIVE
    
    def test_with_exposure_sequence(self):
        """Test adding exposure sequence."""
        exposure_item = RTImageModule.create_exposure_item(
            kvp=120.0,
            x_ray_tube_current=200.0,
            exposure_time=100.0,
            meterset_exposure=50.0
        )
        
        rt_image = RTImageModule.from_required_elements(
            samples_per_pixel=1,
            photometric_interpretation=PhotometricInterpretation.MONOCHROME2,
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            pixel_representation=PixelRepresentation.UNSIGNED,
            rt_image_label="Test Image",
            image_type=["ORIGINAL", "PRIMARY", "PORTAL"]
        ).with_exposure_sequence([exposure_item])
        
        assert hasattr(rt_image, 'ExposureSequence')
        assert len(rt_image.ExposureSequence) == 1
        assert rt_image.ExposureSequence[0].KVP == 120.0
        assert rt_image.ExposureSequence[0].XRayTubeCurrent == 200.0
        assert rt_image.ExposureSequence[0].ExposureTime == 100.0
        assert rt_image.ExposureSequence[0].MetersetExposure == 50.0
    
    def test_create_beam_limiting_device_item(self):
        """Test creating beam limiting device item."""
        device_item = RTImageModule.create_beam_limiting_device_item(
            rt_beam_limiting_device_type=RTBeamLimitingDeviceType.ASYMX,
            number_of_leaf_jaw_pairs=1,
            source_to_beam_limiting_device_distance=400.0,
            leaf_jaw_positions=[-50.0, 50.0]
        )
        
        assert device_item.RTBeamLimitingDeviceType == "ASYMX"
        assert device_item.NumberOfLeafJawPairs == 1
        assert device_item.SourceToBeamLimitingDeviceDistance == 400.0
        assert device_item.LeafJawPositions == [-50.0, 50.0]
    
    def test_create_block_item(self):
        """Test creating block item."""
        block_item = RTImageModule.create_block_item(
            block_number=1,
            block_type=BlockType.SHIELDING,
            block_divergence=BlockDivergence.PRESENT,
            source_to_block_tray_distance=500.0,
            material_id="LEAD",
            block_number_of_points=4,
            block_data=[-50.0, -50.0, 50.0, -50.0, 50.0, 50.0, -50.0, 50.0],
            block_name="Custom Block",
            block_thickness=70.0
        )
        
        assert block_item.BlockNumber == 1
        assert block_item.BlockType == "SHIELDING"
        assert block_item.BlockDivergence == "PRESENT"
        assert block_item.SourceToBlockTrayDistance == 500.0
        assert block_item.MaterialID == "LEAD"
        assert block_item.BlockNumberOfPoints == 4
        assert len(block_item.BlockData) == 8  # 4 points (x,y)
        assert block_item.BlockName == "Custom Block"
        assert block_item.BlockThickness == 70.0
    
    def test_property_methods(self):
        """Test property methods."""
        # Test portal image
        portal_image = RTImageModule.from_required_elements(
            samples_per_pixel=1,
            photometric_interpretation=PhotometricInterpretation.MONOCHROME2,
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            pixel_representation=PixelRepresentation.UNSIGNED,
            rt_image_label="Portal Image",
            image_type=["ORIGINAL", "PRIMARY", "PORTAL"]
        )
        assert portal_image.is_portal_image is True
        assert portal_image.is_simulator_image is False
        assert portal_image.is_fluence_map is False
        
        # Test fluence map
        fluence_map = RTImageModule.from_required_elements(
            samples_per_pixel=1,
            photometric_interpretation=PhotometricInterpretation.MONOCHROME2,
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            pixel_representation=PixelRepresentation.UNSIGNED,
            rt_image_label="Fluence Map",
            image_type=["ORIGINAL", "PRIMARY", "FLUENCE"]
        )
        assert fluence_map.is_fluence_map is True
        
        # Test exposure data
        exposure_item = RTImageModule.create_exposure_item(
            kvp=120.0,
            x_ray_tube_current=200.0
        )
        rt_image = RTImageModule.from_required_elements(
            samples_per_pixel=1,
            photometric_interpretation=PhotometricInterpretation.MONOCHROME2,
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            pixel_representation=PixelRepresentation.UNSIGNED,
            rt_image_label="Test Image",
            image_type=["ORIGINAL", "PRIMARY", "PORTAL"]
        ).with_exposure_sequence([exposure_item])
        
        assert rt_image.has_exposure_data is True
    
    def test_validation_method_exists(self):
        """Test that validation method exists and is callable."""
        rt_image = RTImageModule.from_required_elements(
            samples_per_pixel=1,
            photometric_interpretation=PhotometricInterpretation.MONOCHROME2,
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            pixel_representation=PixelRepresentation.UNSIGNED,
            rt_image_label="Test Image",
            image_type=["ORIGINAL", "PRIMARY", "PORTAL"]
        )
        
        assert hasattr(rt_image, 'validate')
        assert callable(rt_image.validate)
        
        # Test validation result structure
        validation_result = rt_image.validate()
        assert validation_result is not None
        assert isinstance(validation_result, ValidationResult)
        assert hasattr(validation_result, 'errors')
        assert hasattr(validation_result, 'warnings')
        assert isinstance(validation_result.errors, list)
        assert isinstance(validation_result.warnings, list)
    
    
    def test_method_chaining(self):
        """Test that methods support chaining."""
        exposure_item = RTImageModule.create_exposure_item(
            kvp=120.0,
            x_ray_tube_current=200.0
        )
        
        rt_image = (RTImageModule.from_required_elements(
            samples_per_pixel=1,
            photometric_interpretation=PhotometricInterpretation.MONOCHROME2,
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            pixel_representation=PixelRepresentation.UNSIGNED,
            rt_image_label="Chained Image",
            image_type=["ORIGINAL", "PRIMARY", "PORTAL"]
        )
        .with_optional_elements(
            rt_image_name="Chained Name",
            radiation_machine_name="Varian TrueBeam"
        )
        .with_exposure_sequence([exposure_item]))
        
        assert rt_image.RTImageLabel == "Chained Image"
        assert rt_image.RTImageName == "Chained Name"
        assert rt_image.RadiationMachineName == "Varian TrueBeam"
        assert rt_image.has_exposure_data is True
