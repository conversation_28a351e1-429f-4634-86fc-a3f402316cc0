"""
Test PatientModule (M - Mandatory) functionality.

PatientModule implements DICOM PS3.3 C.7.1.1 Patient Module.
Required for all RTDoseIOD instances.
"""

from pyrt_dicom.modules import PatientModule
from pyrt_dicom.enums import PatientSex
from pyrt_dicom.validators import ValidationResult


class TestPatientModule:
    """Test PatientModule (M - Mandatory) functionality."""
    
    def test_from_required_elements_success(self):
        """Test successful creation with required elements."""
        patient = PatientModule.from_required_elements(
            patient_name="<PERSON><PERSON>^<PERSON>^Jr^^",
            patient_id="12345",
            patient_birth_date="19900101",
            patient_sex=PatientSex.MALE
        )
        
        assert patient.PatientName == "Doe^John^Jr^^"
        assert patient.PatientID == "12345"
        assert patient.PatientBirthDate == "19900101"
        assert patient.PatientSex == "M"
    
    def test_required_elements_validation(self):
        """Test validation of required elements."""
        # Test that all patient elements are Type 2 (can be empty but required)
        # All elements have default empty string values per DICOM specification
        patient = PatientModule.from_required_elements()
        
        # Should be able to create with all default empty values
        assert patient.PatientName == ""
        assert patient.PatientID == ""
        assert patient.PatientBirthDate == ""
        assert patient.PatientSex == ""
        
        # Test with specific empty values (Type 2 - allowed to be empty but not None)
        patient = PatientModule.from_required_elements(
            patient_name="",  # Empty but not None (Type 2)
            patient_id="12345",  # Type 2
            patient_birth_date="",  # Type 2
            patient_sex=""  # Type 2
        )
        
        # Should be able to create with empty Type 2 elements
        assert patient.PatientName == ""
        assert patient.PatientID == "12345"
    
    def test_invalid_sex_enum(self):
        """Test invalid patient sex enum handling."""
        # Should be able to create with invalid sex value
        patient = PatientModule.from_required_elements(
            patient_name="Doe^John",
            patient_id="12345",
            patient_birth_date="19900101",
            patient_sex="INVALID"  # Invalid enum value - should be allowed at creation
        )
        
        # Validation should fail with invalid sex value
        validation_result = patient.validate()
        assert len(validation_result.warnings) > 0  # Invalid enum values generate warnings
        
        # Test with valid sex value
        patient = PatientModule.from_required_elements(
            patient_name="Doe^John",
            patient_id="12345",
            patient_birth_date="19900101",
            patient_sex=PatientSex.MALE
        )
        validation_result = patient.validate()
        assert len(validation_result.errors) == 0
    
    def test_person_name_formatting(self):
        """Test proper person name formatting."""
        patient = PatientModule.from_required_elements(
            patient_name="Last^First^Middle^Prefix^Suffix",
            patient_id="12345",
            patient_birth_date="",
            patient_sex=""
        )
        
        assert "Last" in str(patient.PatientName)
        assert "First" in str(patient.PatientName)
    
    def test_with_optional_elements(self):
        """Test adding optional elements."""
        patient = PatientModule.from_required_elements(
            patient_name="Doe^John",
            patient_id="12345",
            patient_birth_date="19900101",
            patient_sex=PatientSex.MALE
        ).with_optional_elements(
            patient_comments="Test patient comments",
            quality_control_subject="NO"
        )
        
        assert hasattr(patient, 'PatientComments')
        assert hasattr(patient, 'QualityControlSubject')
        assert patient.PatientComments == "Test patient comments"
        assert patient.QualityControlSubject == "NO"
    
    def test_patient_id_validation(self):
        """Test patient ID validation."""
        patient = PatientModule.from_required_elements(
            patient_name="Test^Patient",
            patient_id="HOSPITAL123",
            patient_birth_date="",
            patient_sex=""
        )
        
        assert patient.PatientID == "HOSPITAL123"
    
    def test_birth_date_formats(self):
        """Test various birth date formats."""
        valid_dates = ["19900101", "20000229", "19700430"]
        
        for date in valid_dates:
            patient = PatientModule.from_required_elements(
                patient_name="Test^Patient",
                patient_id="TEST001",
                patient_birth_date=date,
                patient_sex=""
            )
            assert patient.PatientBirthDate == date
    
    def test_sex_enum_values(self):
        """Test all valid patient sex enum values."""
        sex_values = [PatientSex.MALE, PatientSex.FEMALE, PatientSex.OTHER]
        
        for sex in sex_values:
            patient = PatientModule.from_required_elements(
                patient_name="Test^Patient",
                patient_id="TEST001",
                patient_birth_date="",
                patient_sex=sex
            )
            assert patient.PatientSex == sex.value
    
    def test_validation_method_exists(self):
        """Test that validation method exists and is callable."""
        # Test with valid data
        patient = PatientModule.from_required_elements(
            patient_name="Test^Patient",
            patient_id="12345",
            patient_birth_date="",
            patient_sex=""
        )
        
        assert hasattr(patient, 'validate')
        assert callable(patient.validate)
        
        # Test validation result structure
        validation_result = patient.validate()
        assert validation_result is not None
        assert isinstance(validation_result, ValidationResult)
        assert hasattr(validation_result, 'errors')
        assert hasattr(validation_result, 'warnings')
        assert isinstance(validation_result.errors, list)
        assert isinstance(validation_result.warnings, list)

        # Test with invalid data
        patient = PatientModule.from_required_elements(
            patient_name="Test^Patient",
            patient_id="12345",  # Valid patient ID
            patient_birth_date="",
            patient_sex="INVALID"  # Invalid enum value
        )
        
        validation_result = patient.validate()
        # Invalid enum values should generate warnings, not errors
        assert len(validation_result.warnings) > 0
        
        # Test validation returns expected structure
        result = patient.validate()
        assert result is not None
        assert isinstance(result, ValidationResult)
        assert hasattr(result, 'errors')
        assert hasattr(result, 'warnings')
        assert isinstance(result.errors, list)
        assert isinstance(result.warnings, list)