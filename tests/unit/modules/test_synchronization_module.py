"""
Test SynchronizationModule functionality.

SynchronizationModule implements DICOM PS3.3 C.7.4.2 Synchronization Module.
Contains attributes necessary to uniquely identify a Frame of Reference that 
establishes the temporal relationship of SOP Instances.
"""

from pyrt_dicom.modules import SynchronizationModule
from pyrt_dicom.enums.synchronization_enums import (
    SynchronizationTrigger,
    AcquisitionTimeSynchronized,
    TimeDistributionProtocol
)
from pyrt_dicom.validators import ValidationResult


class TestSynchronizationModule:
    """Test SynchronizationModule functionality."""
    
    def test_from_required_elements_success(self):
        """Test successful creation with required elements."""
        sync = SynchronizationModule.from_required_elements(
            synchronization_frame_of_reference_uid="*******.*******.9.10",
            synchronization_trigger=SynchronizationTrigger.SOURCE,
            acquisition_time_synchronized=AcquisitionTimeSynchronized.YES
        )
        
        assert sync.SynchronizationFrameOfReferenceUID == "*******.*******.9.10"
        assert sync.SynchronizationTrigger == "SOURCE"
        assert sync.AcquisitionTimeSynchronized == "Y"
    
    def test_required_elements_with_string_values(self):
        """Test creation with string enum values."""
        sync = SynchronizationModule.from_required_elements(
            synchronization_frame_of_reference_uid="*******.*******.9.10",
            synchronization_trigger="EXTERNAL",
            acquisition_time_synchronized="N"
        )
        
        assert sync.SynchronizationFrameOfReferenceUID == "*******.*******.9.10"
        assert sync.SynchronizationTrigger == "EXTERNAL"
        assert sync.AcquisitionTimeSynchronized == "N"
    
    def test_with_optional_elements(self):
        """Test adding optional elements."""
        sync = SynchronizationModule.from_required_elements(
            synchronization_frame_of_reference_uid="*******.*******.9.10",
            synchronization_trigger=SynchronizationTrigger.SOURCE,
            acquisition_time_synchronized=AcquisitionTimeSynchronized.YES
        ).with_optional_elements(
            trigger_source_or_type="LINAC_001",
            time_source="NTP_SERVER_001",
            time_distribution_protocol=TimeDistributionProtocol.NTP,
            ntp_source_address="*************"
        )
        
        assert hasattr(sync, 'TriggerSourceOrType')
        assert hasattr(sync, 'TimeSource')
        assert hasattr(sync, 'TimeDistributionProtocol')
        assert hasattr(sync, 'NTPSourceAddress')
        assert sync.TriggerSourceOrType == "LINAC_001"
        assert sync.TimeSource == "NTP_SERVER_001"
        assert sync.TimeDistributionProtocol == "NTP"
        assert sync.NTPSourceAddress == "*************"
    
    def test_with_synchronization_channel(self):
        """Test adding synchronization channel."""
        sync = SynchronizationModule.from_required_elements(
            synchronization_frame_of_reference_uid="*******.*******.9.10",
            synchronization_trigger=SynchronizationTrigger.EXTERNAL,
            acquisition_time_synchronized=AcquisitionTimeSynchronized.YES
        ).with_synchronization_channel(
            synchronization_channel=[1, 2]
        )
        
        assert hasattr(sync, 'SynchronizationChannel')
        assert sync.SynchronizationChannel == [1, 2]
    
    def test_synchronization_channel_validation(self):
        """Test synchronization channel validation."""
        sync = SynchronizationModule.from_required_elements(
            synchronization_frame_of_reference_uid="*******.*******.9.10",
            synchronization_trigger=SynchronizationTrigger.EXTERNAL,
            acquisition_time_synchronized=AcquisitionTimeSynchronized.YES
        )
        
        # Test invalid channel specification (wrong number of values)
        try:
            sync.with_synchronization_channel([1])  # Should be [M,C] pair
            assert False, "Should have raised ValueError"
        except ValueError as e:
            assert "exactly 2 values" in str(e)
    
    def test_synchronization_trigger_properties(self):
        """Test synchronization trigger property methods."""
        # Test SOURCE trigger
        sync_source = SynchronizationModule.from_required_elements(
            synchronization_frame_of_reference_uid="*******.*******.9.10",
            synchronization_trigger=SynchronizationTrigger.SOURCE,
            acquisition_time_synchronized=AcquisitionTimeSynchronized.YES
        )
        assert sync_source.is_source_synchronized == True
        assert sync_source.is_externally_synchronized == False
        assert sync_source.is_passthrough_synchronized == False
        assert sync_source.has_no_trigger == False
        
        # Test EXTERNAL trigger
        sync_external = SynchronizationModule.from_required_elements(
            synchronization_frame_of_reference_uid="*******.*******.9.10",
            synchronization_trigger=SynchronizationTrigger.EXTERNAL,
            acquisition_time_synchronized=AcquisitionTimeSynchronized.YES
        )
        assert sync_external.is_source_synchronized == False
        assert sync_external.is_externally_synchronized == True
        assert sync_external.is_passthrough_synchronized == False
        assert sync_external.has_no_trigger == False
    
    def test_time_synchronization_properties(self):
        """Test time synchronization property methods."""
        # Test time synchronized
        sync_time_yes = SynchronizationModule.from_required_elements(
            synchronization_frame_of_reference_uid="*******.*******.9.10",
            synchronization_trigger=SynchronizationTrigger.SOURCE,
            acquisition_time_synchronized=AcquisitionTimeSynchronized.YES
        )
        assert sync_time_yes.is_time_synchronized == True
        
        # Test time not synchronized
        sync_time_no = SynchronizationModule.from_required_elements(
            synchronization_frame_of_reference_uid="*******.*******.9.10",
            synchronization_trigger=SynchronizationTrigger.SOURCE,
            acquisition_time_synchronized=AcquisitionTimeSynchronized.NO
        )
        assert sync_time_no.is_time_synchronized == False
    
    def test_protocol_properties(self):
        """Test time distribution protocol properties."""
        sync = SynchronizationModule.from_required_elements(
            synchronization_frame_of_reference_uid="*******.*******.9.10",
            synchronization_trigger=SynchronizationTrigger.SOURCE,
            acquisition_time_synchronized=AcquisitionTimeSynchronized.YES
        )
        
        # Test NTP protocol
        sync.with_optional_elements(time_distribution_protocol=TimeDistributionProtocol.NTP)
        assert sync.uses_ntp_protocol == True
        assert sync.uses_precision_time_protocol == False
        
        # Test PTP protocol
        sync.with_optional_elements(time_distribution_protocol=TimeDistributionProtocol.PTP)
        assert sync.uses_ntp_protocol == False
        assert sync.uses_precision_time_protocol == True
        
        # Test SNTP protocol (should be considered NTP)
        sync.with_optional_elements(time_distribution_protocol=TimeDistributionProtocol.SNTP)
        assert sync.uses_ntp_protocol == True
    
    def test_synchronization_channel_property(self):
        """Test synchronization channel property."""
        sync = SynchronizationModule.from_required_elements(
            synchronization_frame_of_reference_uid="*******.*******.9.10",
            synchronization_trigger=SynchronizationTrigger.EXTERNAL,
            acquisition_time_synchronized=AcquisitionTimeSynchronized.YES
        )
        
        # Initially no synchronization channel
        assert sync.has_synchronization_channel == False
        
        # Add synchronization channel
        sync.with_synchronization_channel([3, 4])
        assert sync.has_synchronization_channel == True
    
    def test_all_synchronization_trigger_enum_values(self):
        """Test all valid synchronization trigger enum values."""
        trigger_values = [
            SynchronizationTrigger.SOURCE,
            SynchronizationTrigger.EXTERNAL,
            SynchronizationTrigger.PASSTHRU,
            SynchronizationTrigger.NO_TRIGGER
        ]
        
        for trigger in trigger_values:
            sync = SynchronizationModule.from_required_elements(
                synchronization_frame_of_reference_uid="*******.*******.9.10",
                synchronization_trigger=trigger,
                acquisition_time_synchronized=AcquisitionTimeSynchronized.YES
            )
            assert sync.SynchronizationTrigger == trigger.value
    
    def test_all_time_distribution_protocol_enum_values(self):
        """Test all valid time distribution protocol enum values."""
        protocol_values = [
            TimeDistributionProtocol.NTP,
            TimeDistributionProtocol.IRIG,
            TimeDistributionProtocol.GPS,
            TimeDistributionProtocol.SNTP,
            TimeDistributionProtocol.PTP
        ]
        
        for protocol in protocol_values:
            sync = SynchronizationModule.from_required_elements(
                synchronization_frame_of_reference_uid="*******.*******.9.10",
                synchronization_trigger=SynchronizationTrigger.SOURCE,
                acquisition_time_synchronized=AcquisitionTimeSynchronized.YES
            ).with_optional_elements(time_distribution_protocol=protocol)
            
            assert sync.TimeDistributionProtocol == protocol.value
    
    def test_validation_method_exists(self):
        """Test that validation method exists and is callable."""
        sync = SynchronizationModule.from_required_elements(
            synchronization_frame_of_reference_uid="*******.*******.9.10",
            synchronization_trigger=SynchronizationTrigger.SOURCE,
            acquisition_time_synchronized=AcquisitionTimeSynchronized.YES
        )
        
        assert hasattr(sync, 'validate')
        assert callable(sync.validate)
        
        # Test validation result structure
        validation_result = sync.validate()
        assert validation_result is not None
        assert isinstance(validation_result, ValidationResult)
        assert hasattr(validation_result, 'errors')
        assert hasattr(validation_result, 'warnings')
        assert isinstance(validation_result.errors, list)
        assert isinstance(validation_result.warnings, list)
    