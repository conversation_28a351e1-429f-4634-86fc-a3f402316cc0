"""
Test ClinicalTrialSeriesModule functionality.

ClinicalTrialSeriesModule implements DICOM PS3.3 C.7.3.2 Clinical Trial Series Module.
- Clinical Trial Coordinating Center Name is Type 2 (required but may be empty)
- Other attributes are Type 3 (optional)
"""

from pyrt_dicom.modules.modules.clinical_trial_series_module import ClinicalTrialSeriesModule
from pyrt_dicom.validators import ValidationResult


class TestClinicalTrialSeriesModule:
    """Test ClinicalTrialSeriesModule functionality."""
    
    def test_from_required_elements_success(self):
        """Test successful creation with required Type 2 element."""
        trial_series = ClinicalTrialSeriesModule.from_required_elements(
            clinical_trial_coordinating_center_name="Research Medical Center"
        )
        
        # Module should be created successfully with required element
        assert trial_series is not None
        assert isinstance(trial_series, ClinicalTrialSeriesModule)
        
        dataset = trial_series.to_dataset()
        assert dataset.ClinicalTrialCoordinatingCenterName == "Research Medical Center"
    
    def test_with_optional_elements_coordinating_center(self):
        """Test that coordinating center is set during construction."""
        trial_series = ClinicalTrialSeriesModule.from_required_elements(
            clinical_trial_coordinating_center_name="Research Center A"
        )
        
        dataset = trial_series.to_dataset()
        assert hasattr(dataset, 'ClinicalTrialCoordinatingCenterName')
        assert dataset.ClinicalTrialCoordinatingCenterName == "Research Center A"
    
    def test_with_optional_elements_series_id(self):
        """Test adding series identification information."""
        trial_series = ClinicalTrialSeriesModule.from_required_elements()
        trial_series.with_optional_elements(
            clinical_trial_series_id="SERIES001",
            clinical_trial_series_description="Baseline CT imaging"
        )
        
        dataset = trial_series.to_dataset()
        assert hasattr(dataset, 'ClinicalTrialSeriesID')
        assert hasattr(dataset, 'ClinicalTrialSeriesDescription')
        assert dataset.ClinicalTrialSeriesID == "SERIES001"
        assert dataset.ClinicalTrialSeriesDescription == "Baseline CT imaging"
    
    def test_with_optional_elements_issuer_info(self):
        """Test adding issuer information."""
        trial_series = ClinicalTrialSeriesModule.from_required_elements()
        trial_series.with_optional_elements(
            clinical_trial_series_id="SERIES001",
            issuer_of_clinical_trial_series_id="HOSPITAL_A"
        )
        
        dataset = trial_series.to_dataset()
        assert hasattr(dataset, 'ClinicalTrialSeriesID')
        assert hasattr(dataset, 'IssuerOfClinicalTrialSeriesID')
        assert dataset.ClinicalTrialSeriesID == "SERIES001"
        assert dataset.IssuerOfClinicalTrialSeriesID == "HOSPITAL_A"
    
    def test_with_optional_elements_all_fields(self):
        """Test adding all optional elements."""
        trial_series = ClinicalTrialSeriesModule.from_required_elements(
            clinical_trial_coordinating_center_name="National Research Center"
        ).with_optional_elements(
            clinical_trial_series_id="TRIAL_ABC_SERIES_001",
            issuer_of_clinical_trial_series_id="NRC_IMAGING",
            clinical_trial_series_description="Pre-treatment baseline imaging series"
        )
        
        dataset = trial_series.to_dataset()
        assert dataset.ClinicalTrialCoordinatingCenterName == "National Research Center"
        assert dataset.ClinicalTrialSeriesID == "TRIAL_ABC_SERIES_001"
        assert dataset.IssuerOfClinicalTrialSeriesID == "NRC_IMAGING"
        assert dataset.ClinicalTrialSeriesDescription == "Pre-treatment baseline imaging series"
    
    def test_none_values_not_set(self):
        """Test that None values are not set as attributes."""
        trial_series = ClinicalTrialSeriesModule.from_required_elements()
        trial_series.with_optional_elements(
            clinical_trial_series_id="SERIES001",
            issuer_of_clinical_trial_series_id=None,
            clinical_trial_series_description=None
        )
        
        # Only non-None values should be set
        dataset = trial_series.to_dataset()
        # Coordinating center name should be empty string by default (Type 2)
        assert hasattr(dataset, 'ClinicalTrialCoordinatingCenterName')
        assert dataset.ClinicalTrialCoordinatingCenterName == ""
        assert hasattr(dataset, 'ClinicalTrialSeriesID')
        assert not hasattr(dataset, 'IssuerOfClinicalTrialSeriesID')
        assert not hasattr(dataset, 'ClinicalTrialSeriesDescription')
        assert dataset.ClinicalTrialSeriesID == "SERIES001"
    
    def test_has_coordinating_center_info_property(self):
        """Test has_coordinating_center_info property."""
        # Test with empty coordinating center name (default)
        trial_series = ClinicalTrialSeriesModule.from_required_elements()
        assert not trial_series.has_coordinating_center_info
        
        # Test with actual coordinating center name
        trial_series = ClinicalTrialSeriesModule.from_required_elements(
            clinical_trial_coordinating_center_name="Test Center"
        )
        assert trial_series.has_coordinating_center_info
    
    def test_has_series_identification_property(self):
        """Test has_series_identification property."""
        trial_series = ClinicalTrialSeriesModule.from_required_elements()
        
        # Initially should be False
        assert not trial_series.has_series_identification
        
        # After adding series ID
        trial_series.with_optional_elements(clinical_trial_series_id="SERIES001")
        assert trial_series.has_series_identification
        
        # Reset and test with description only
        trial_series = ClinicalTrialSeriesModule.from_required_elements()
        trial_series.with_optional_elements(clinical_trial_series_description="Test description")
        assert trial_series.has_series_identification
    
    def test_has_issuer_info_property(self):
        """Test has_issuer_info property."""
        trial_series = ClinicalTrialSeriesModule.from_required_elements()
        
        # Initially should be False
        assert not trial_series.has_issuer_info
        
        # After adding issuer information
        trial_series.with_optional_elements(
            issuer_of_clinical_trial_series_id="TEST_ISSUER"
        )
        assert trial_series.has_issuer_info
    
    def test_method_chaining(self):
        """Test that with_optional_elements returns self for method chaining."""
        trial_series = ClinicalTrialSeriesModule.from_required_elements(
            clinical_trial_coordinating_center_name="Test Center"
        )
        
        result = trial_series.with_optional_elements(
            clinical_trial_series_id="SERIES001"
        )
        
        # Should return self
        assert result is trial_series
        
        dataset = trial_series.to_dataset()
        assert dataset.ClinicalTrialCoordinatingCenterName == "Test Center"
        assert dataset.ClinicalTrialSeriesID == "SERIES001"
    
    def test_validation_method_exists(self):
        """Test that validation method exists and is callable."""
        trial_series = ClinicalTrialSeriesModule.from_required_elements(
            clinical_trial_coordinating_center_name="Test Center"
        ).with_optional_elements(
            clinical_trial_series_id="SERIES001"
        )
        
        assert hasattr(trial_series, 'validate')
        assert callable(trial_series.validate)
        
        # Test validation result structure
        validation_result = trial_series.validate()
        assert validation_result is not None
        assert isinstance(validation_result, ValidationResult)
        assert hasattr(validation_result, "errors")
        assert hasattr(validation_result, "warnings")
        assert isinstance(validation_result.errors, list)
        assert isinstance(validation_result.warnings, list)
    
    def test_empty_module_validation(self):
        """Test validation of empty module (all elements are optional)."""
        trial_series = ClinicalTrialSeriesModule.from_required_elements()
        
        # Empty module should validate successfully since all elements are optional
        validation_result = trial_series.validate()
        assert validation_result.error_count == 0
    
    def test_to_dataset_generation(self):
        """Test that to_dataset() generates correct DICOM datasets."""
        trial_series = ClinicalTrialSeriesModule.from_required_elements(
            clinical_trial_coordinating_center_name="Test Center"
        ).with_optional_elements(
            clinical_trial_series_id="SERIES001",
            issuer_of_clinical_trial_series_id="TEST_ISSUER",
            clinical_trial_series_description="Test series description"
        )
        
        dataset = trial_series.to_dataset()
        
        # Verify dataset type and contents
        from pydicom import Dataset
        assert isinstance(dataset, Dataset)
        assert len(dataset) > 0
        
        # Verify all elements are present
        assert hasattr(dataset, 'ClinicalTrialCoordinatingCenterName')
        assert hasattr(dataset, 'ClinicalTrialSeriesID')
        assert hasattr(dataset, 'IssuerOfClinicalTrialSeriesID')
        assert hasattr(dataset, 'ClinicalTrialSeriesDescription')
        
        # Verify values are correct
        assert dataset.ClinicalTrialCoordinatingCenterName == "Test Center"
        assert dataset.ClinicalTrialSeriesID == "SERIES001"
        assert dataset.IssuerOfClinicalTrialSeriesID == "TEST_ISSUER"
        assert dataset.ClinicalTrialSeriesDescription == "Test series description"