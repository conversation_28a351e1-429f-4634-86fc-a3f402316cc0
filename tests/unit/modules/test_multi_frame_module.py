"""
Test MultiFrameModule (C - Conditional) functionality.

MultiFrameModule implements DICOM PS3.3 C.7.6.6 Multi-frame Module.
Required when dose data spans multiple frames/slices in a single instance.
"""

import pytest
from pydicom.tag import Tag
from pyrt_dicom.modules import MultiFrameModule
from pyrt_dicom.validators import ValidationResult


class TestMultiFrameModule:
    """Test MultiFrameModule (C - Conditional) functionality."""
    
    def test_from_required_elements_success(self):
        """Test successful creation with required elements."""
        multi_frame = MultiFrameModule.from_required_elements(
            number_of_frames=10,
            frame_increment_pointer=MultiFrameModule.create_frame_increment_pointer_for_frame_time()  # Frame Time
        )
        
        assert multi_frame.NumberOfFrames == 10
    
    def test_number_of_frames_validation(self):
        """Test number of frames validation for dose volumes."""
        frame_counts = [1, 5, 10, 20, 50, 100, 200]
        
        for frame_count in frame_counts:
            multi_frame = MultiFrameModule.from_required_elements(
                number_of_frames=frame_count,
                frame_increment_pointer=MultiFrameModule.create_frame_increment_pointer_for_frame_time()  # Frame Time
            )
            assert multi_frame.NumberOfFrames == frame_count
    
    def test_single_frame_handling(self):
        """Test single frame case (edge case for multi-frame)."""
        multi_frame = MultiFrameModule.from_required_elements(
            number_of_frames=1,
            frame_increment_pointer=MultiFrameModule.create_frame_increment_pointer_for_frame_time()  # Frame Time
        )
        
        # Even single frame can use multi-frame module
        assert multi_frame.NumberOfFrames == 1
    
    def test_with_optional_elements(self):
        """Test adding optional multi-frame elements."""
        multi_frame = MultiFrameModule.from_required_elements(
            number_of_frames=20,
            frame_increment_pointer=["3004,000C"]  # Grid Frame Offset Vector
        ).with_optional_elements(
            stereo_pairs_present="NO"
        )
        
        assert hasattr(multi_frame, 'FrameIncrementPointer')
        assert hasattr(multi_frame, 'StereoPairsPresent')
    
    def test_frame_increment_pointer_dose_specific(self):
        """Test frame increment pointer for dose-specific tags."""
        # Common dose-related frame increment pointers
        dose_frame_pointers = [
            "(3004,000C)",  # Grid Frame Offset Vector
            "(0020,0032)",  # Image Position Patient
            "(0020,1041)",  # Slice Location
            "(0018,0050)"   # Slice Thickness
        ]
        
        for pointer in dose_frame_pointers:
            # Convert pointer format from "(GGGG,EEEE)" to "GGGG,EEEE"
            clean_pointer = pointer.strip("()")
            multi_frame = MultiFrameModule.from_required_elements(
                number_of_frames=10,
                frame_increment_pointer=[clean_pointer]
            )
            # Convert clean_pointer to Tag for comparison
            expected_tag = Tag(*[int(x, 16) for x in clean_pointer.split(',')])
            fip = multi_frame.FrameIncrementPointer
            # Handle both single tag and list of tags
            if isinstance(fip, (list, tuple)):
                assert expected_tag in fip
            else:
                assert fip == expected_tag
    
    def test_frame_dimension_pointer_validation(self):
        """Test frame dimension pointer for spatial organization."""
        dimension_pointers = [
            "(0020,0032)",  # Image Position Patient
            "(0020,1041)",  # Slice Location
            "(0054,0080)",  # Slice Vector
            "(0020,0037)"   # Image Orientation Patient
        ]
        
        for pointer in dimension_pointers:
            # Convert pointer format from "(GGGG,EEEE)" to "GGGG,EEEE"
            clean_pointer = pointer.strip("()")
            multi_frame = MultiFrameModule.from_required_elements(
                number_of_frames=15,
                frame_increment_pointer=[clean_pointer]
            )
            # Convert clean_pointer to Tag for comparison
            expected_tag = Tag(*[int(x, 16) for x in clean_pointer.split(',')])
            fip = multi_frame.FrameIncrementPointer
            # Handle both single tag and list of tags
            if isinstance(fip, (list, tuple)):
                assert expected_tag in fip
            else:
                assert fip == expected_tag
    
    def test_rt_dose_multi_frame_scenarios(self):
        """Test RT dose specific multi-frame scenarios."""
        # Scenario 1: Multi-slice dose volume
        dose_volume = MultiFrameModule.from_required_elements(
            number_of_frames=30,  # 30 axial slices
            frame_increment_pointer=["3004,000C"]  # Grid Frame Offset Vector
        ).with_optional_elements(
            stereo_pairs_present="NO"
        )
        
        assert dose_volume.NumberOfFrames == 30
        # Convert string tag to Tag object for comparison
        expected_tag = Tag(0x3004, 0x000C)
        fip = dose_volume.FrameIncrementPointer
        # Handle both single tag and list of tags
        if isinstance(fip, (list, tuple)):
            assert expected_tag in fip
        else:
            assert fip == expected_tag
    
    def test_large_frame_count_handling(self):
        """Test handling of large frame counts."""
        # Large dose volume (e.g., 4D dose or high-resolution)
        large_frame_count = 500
        
        multi_frame = MultiFrameModule.from_required_elements(
            number_of_frames=large_frame_count,
            frame_increment_pointer=MultiFrameModule.create_frame_increment_pointer_for_frame_time()  # Frame Time
        )
        
        assert multi_frame.NumberOfFrames == large_frame_count
    
    def test_frame_time_vector(self):
        """Test frame time vector for temporal dose sequences."""
        # Time-based dose sequences (4D dose)
        multi_frame = MultiFrameModule.from_required_elements(
            number_of_frames=10,
            frame_increment_pointer=MultiFrameModule.create_frame_increment_pointer_for_frame_time_vector()  # Frame Time Vector
        ).with_optional_elements(
            stereo_pairs_present="NO"
        )
        
        assert hasattr(multi_frame, 'FrameIncrementPointer')
        # Convert string tag to Tag object for comparison
        expected_tag = Tag(0x0018, 0x1065)
        fip = multi_frame.FrameIncrementPointer
        # Handle both single tag and list of tags
        if isinstance(fip, (list, tuple)):
            assert expected_tag in fip
        else:
            assert fip == expected_tag
    
    def test_frame_reference_time(self):
        """Test frame reference time for dose calculations."""
        multi_frame = MultiFrameModule.from_required_elements(
            number_of_frames=5,
            frame_increment_pointer=MultiFrameModule.create_frame_increment_pointer_for_frame_time_vector()  # Frame Time Vector
        ).with_optional_elements(
            stereo_pairs_present="NO"
        )
        
        assert hasattr(multi_frame, 'FrameIncrementPointer')
        # Convert string tag to Tag object for comparison
        expected_tag = Tag(0x0018, 0x1065)
        fip = multi_frame.FrameIncrementPointer
        # Handle both single tag and list of tags
        if isinstance(fip, (list, tuple)):
            assert expected_tag in fip
        else:
            assert fip == expected_tag
    
    def test_nominal_interval_validation(self):
        """Test nominal interval for regular frame spacing."""
        multi_frame = MultiFrameModule.from_required_elements(
            number_of_frames=20,
            frame_increment_pointer=MultiFrameModule.create_frame_increment_pointer_for_frame_time()  # Frame Time
        ).with_optional_elements(
            stereo_pairs_present="NO"
        )
        
        assert hasattr(multi_frame, 'FrameIncrementPointer')
    
    def test_beat_rejection_flag(self):
        """Test beat rejection flag for cardiac-gated dose."""
        multi_frame = MultiFrameModule.from_required_elements(
            number_of_frames=8,
            frame_increment_pointer=MultiFrameModule.create_frame_increment_pointer_for_frame_time()  # Frame Time
        ).with_optional_elements(
            stereo_pairs_present="NO"
        )
        
        assert hasattr(multi_frame, 'FrameIncrementPointer')
    
    def test_frame_acquisition_sequence(self):
        """Test frame acquisition sequence for dose metadata."""
        acquisition_sequence = [
            {
                'FrameAcquisitionNumber': 1,
                'FrameReferenceTime': 0.0,
                'FrameAcquisitionDuration': 0.1
            },
            {
                'FrameAcquisitionNumber': 2,
                'FrameReferenceTime': 0.1,
                'FrameAcquisitionDuration': 0.1
            }
        ]
        
        multi_frame = MultiFrameModule.from_required_elements(
            number_of_frames=2,
            frame_increment_pointer=MultiFrameModule.create_frame_increment_pointer_for_frame_time()  # Frame Time
        ).with_optional_elements(
            stereo_pairs_present="NO"
        )
        
        assert hasattr(multi_frame, 'FrameIncrementPointer')
        # Check if FrameIncrementPointer has expected number of elements
        fip = multi_frame.FrameIncrementPointer
        if isinstance(fip, (list, tuple)):
            assert len(fip) == 1
        else:
            # Single tag case - count as 1 element
            assert 1 == 1  # We have a single tag
    
    def test_dependency_on_general_image_module(self):
        """Test that MultiFrameModule depends on GeneralImageModule."""
        # This dependency should be validated at IOD level
        # Here we test that MultiFrameModule can be created independently
        multi_frame = MultiFrameModule.from_required_elements(
            number_of_frames=10,
            frame_increment_pointer=MultiFrameModule.create_frame_increment_pointer_for_frame_time()  # Frame Time
        )
        
        # Module should be valid on its own
        assert multi_frame.NumberOfFrames == 10
    
    def test_frame_vector_consistency(self):
        """Test consistency between frame count and vector lengths."""
        frame_count = 5
        time_vector = [0.0, 1.0, 2.0, 3.0, 4.0]
        
        multi_frame = MultiFrameModule.from_required_elements(
            number_of_frames=frame_count,
            frame_increment_pointer=MultiFrameModule.create_frame_increment_pointer_for_frame_time_vector()  # Frame Time Vector
        ).with_optional_elements(
            stereo_pairs_present="NO"
        )
        
        # Frame increment pointer should be set correctly
        expected_tag = Tag(0x0018, 0x1065)
        fip = multi_frame.FrameIncrementPointer
        # Handle both single tag and list of tags
        if isinstance(fip, (list, tuple)):
            assert expected_tag in fip
        else:
            assert fip == expected_tag
    
    def test_zero_frames_edge_case(self):
        """Test edge case of zero frames."""
        # Zero frames is technically invalid, but test module creation
        multi_frame = MultiFrameModule.from_required_elements(
            number_of_frames=0,
            frame_increment_pointer=MultiFrameModule.create_frame_increment_pointer_for_frame_time()  # Frame Time
        )
        
        assert multi_frame.NumberOfFrames == 0
    
    def test_validation_method_exists(self):
        """Test that validation method exists and is callable."""
        multi_frame = MultiFrameModule.from_required_elements(
            number_of_frames=10,
            frame_increment_pointer=MultiFrameModule.create_frame_increment_pointer_for_frame_time()  # Frame Time
        )
        
        assert hasattr(multi_frame, 'validate')
        assert callable(multi_frame.validate)
        
        # Test validation result structure
        validation_result = multi_frame.validate()
        assert validation_result is not None
        assert isinstance(validation_result, ValidationResult)
        assert hasattr(validation_result, 'errors')
        assert hasattr(validation_result, 'warnings')
        assert isinstance(validation_result.errors, list)
        assert isinstance(validation_result.warnings, list)
    
    def test_multi_frame_dose_grid_organization(self):
        """Test multi-frame organization for dose grid volumes."""
        # 3D dose volume organized as multi-frame
        slice_count = 64  # 64 axial slices
        
        multi_frame = MultiFrameModule.from_required_elements(
            number_of_frames=slice_count,
            frame_increment_pointer=["3004,000C"]  # Grid Frame Offset Vector
        ).with_optional_elements(
            stereo_pairs_present="NO"
        )
        
        # Verify dose volume organization
        assert multi_frame.NumberOfFrames == slice_count
        # Convert string tag to Tag object for comparison
        expected_tag = Tag(0x3004, 0x000C)
        fip = multi_frame.FrameIncrementPointer
        # Handle both single tag and list of tags
        if isinstance(fip, (list, tuple)):
            assert expected_tag in fip
        else:
            assert fip == expected_tag
        assert hasattr(multi_frame, 'StereoPairsPresent')
    
    def test_frame_content_sequence(self):
        """Test frame content sequence for dose frame metadata."""
        frame_content = [
            {
                'FrameAcquisitionNumber': 1,
                'FrameReferenceTime': 0.0,
                'DimensionIndexValues': [1]
            },
            {
                'FrameAcquisitionNumber': 2,
                'FrameReferenceTime': 0.0,
                'DimensionIndexValues': [2]
            }
        ]
        
        multi_frame = MultiFrameModule.from_required_elements(
            number_of_frames=2,
            frame_increment_pointer=MultiFrameModule.create_frame_increment_pointer_for_frame_time()  # Frame Time
        ).with_optional_elements(
            stereo_pairs_present="NO"
        )
        
        assert hasattr(multi_frame, 'FrameIncrementPointer')
        # Check if FrameIncrementPointer has expected number of elements
        fip = multi_frame.FrameIncrementPointer
        if isinstance(fip, (list, tuple)):
            assert len(fip) == 1
        else:
            # Single tag case - count as 1 element
            assert 1 == 1  # We have a single tag