"""
Test RTDoseModule (M - Mandatory) functionality.

RTDoseModule implements DICOM PS3.3 C.8.8.3 RT Dose Module.
Required for all RTDoseIOD instances.
"""

import pytest
import numpy as np
from pyrt_dicom.modules import RTDoseModule
from pyrt_dicom.enums import DoseUnits, DoseType, DoseSummationType
from pyrt_dicom.validators import ValidationResult


class TestRTDoseModule:
    """Test RTDoseModule (M - Mandatory) functionality."""
    
    def test_from_required_elements_success(self):
        """Test successful creation with required elements."""
        dose = RTDoseModule.from_required_elements(
            dose_units=DoseUnits.GY,
            dose_type=DoseType.PHYSICAL,
            dose_summation_type=DoseSummationType.PLAN
        )
        
        assert dose.DoseUnits == "GY"
        assert dose.DoseType == "PHYSICAL"
        assert dose.DoseSummationType == "PLAN"
    
    def test_dose_units_validation(self):
        """Test dose units enum validation."""
        dose_array = np.ones((5, 5, 5), dtype=np.uint32)
        
        for units in [DoseUnits.GY, DoseUnits.RELATIVE]:
            dose = RTDoseModule.from_required_elements(
                dose_units=units,
                dose_type=DoseType.PHYSICAL,
                dose_summation_type=DoseSummationType.PLAN
            )
            assert dose.DoseUnits == units.value
    
    def test_dose_type_validation(self):
        """Test dose type enum validation."""
        dose_array = np.ones((5, 5, 5), dtype=np.uint32)
        
        for dose_type in [DoseType.PHYSICAL, DoseType.EFFECTIVE, DoseType.ERROR]:
            dose = RTDoseModule.from_required_elements(
                dose_units=DoseUnits.GY,
                dose_type=dose_type,
                dose_summation_type=DoseSummationType.PLAN
            )
            assert dose.DoseType == dose_type.value
    
    def test_pixel_data_array_handling(self):
        """Test pixel data array handling for dose distributions."""
        # Test different array sizes and data types
        test_arrays = [
            np.ones((10, 10, 10), dtype=np.uint16),
            np.ones((64, 64, 32), dtype=np.uint32),
            np.random.rand(5, 5, 5).astype(np.float32)
        ]
        
        for pixel_data in test_arrays:
            dose = RTDoseModule.from_required_elements(
                dose_units=DoseUnits.GY,
                dose_type=DoseType.PHYSICAL,
                dose_summation_type=DoseSummationType.PLAN
            ).with_pixel_data_elements(
                samples_per_pixel=1,
                photometric_interpretation="MONOCHROME2",
                bits_allocated=32,
                bits_stored=32,
                high_bit=31,
                pixel_representation=0,
                dose_grid_scaling=0.001
            )
            
            # Store pixel data manually since the module doesn't handle arrays yet
            dose.PixelData = pixel_data.tobytes()
            
            # Verify pixel data is stored correctly
            assert hasattr(dose, 'PixelData')
    
    def test_photometric_interpretation_values(self):
        """Test various photometric interpretation values."""
        dose_array = np.ones((5, 5, 5), dtype=np.uint32)
        valid_interpretations = ["MONOCHROME1", "MONOCHROME2"]
        
        for interpretation in valid_interpretations:
            dose = RTDoseModule.from_required_elements(
                dose_units=DoseUnits.GY,
                dose_type=DoseType.PHYSICAL,
                dose_summation_type=DoseSummationType.PLAN
            ).with_pixel_data_elements(
                samples_per_pixel=1,
                photometric_interpretation=interpretation,
                bits_allocated=32,
                bits_stored=32,
                high_bit=31,
                pixel_representation=0,
                dose_grid_scaling=0.001
            )
            dose.PixelData = dose_array.tobytes()
            assert dose.PhotometricInterpretation == interpretation
    
    def test_bits_configuration(self):
        """Test various bits allocated/stored configurations."""
        dose_array = np.ones((5, 5, 5), dtype=np.uint32)
        
        # Test 16-bit configuration
        dose = RTDoseModule.from_required_elements(
            dose_units=DoseUnits.GY,
            dose_type=DoseType.PHYSICAL,
            dose_summation_type=DoseSummationType.PLAN
        ).with_pixel_data_elements(
            samples_per_pixel=1,
            photometric_interpretation="MONOCHROME2",
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            pixel_representation=0,
            dose_grid_scaling=0.001
        )
        dose.PixelData = dose_array.tobytes()
        
        assert dose.BitsAllocated == 16
        assert dose.BitsStored == 16
        assert dose.HighBit == 15
    
    def test_with_optional_elements(self):
        """Test adding optional dose-specific elements."""
        dose = RTDoseModule.from_required_elements(
            dose_units=DoseUnits.GY,
            dose_type=DoseType.PHYSICAL,
            dose_summation_type=DoseSummationType.PLAN
        ).with_optional_elements(
            dose_comment="Test dose distribution",
            normalization_point=[0.0, 0.0, 0.0],
            content_date="20240101",
            content_time="120000"
        )
        
        assert hasattr(dose, 'DoseComment')
        assert hasattr(dose, 'NormalizationPoint')
        assert hasattr(dose, 'ContentDate')
        assert hasattr(dose, 'ContentTime')
    
    def test_dose_grid_scaling_validation(self):
        """Test dose grid scaling factor validation."""
        dose_array = np.ones((5, 5, 5), dtype=np.uint32)
        scaling_factors = [0.001, 0.01, 0.1, 1.0, 10.0]
        
        for scaling in scaling_factors:
            dose = RTDoseModule.from_required_elements(
                dose_units=DoseUnits.GY,
                dose_type=DoseType.PHYSICAL,
                dose_summation_type=DoseSummationType.PLAN
            ).with_pixel_data_elements(
                samples_per_pixel=1,
                photometric_interpretation="MONOCHROME2",
                bits_allocated=32,
                bits_stored=32,
                high_bit=31,
                pixel_representation=0,
                dose_grid_scaling=scaling
            )
            dose.PixelData = dose_array.tobytes()
            assert dose.DoseGridScaling == scaling
    
    def test_normalization_point_formats(self):
        """Test various normalization point coordinate formats."""
        dose_array = np.ones((5, 5, 5), dtype=np.uint32)
        
        # Test different coordinate systems
        normalization_points = [
            (0.0, 0.0, 0.0),          # Origin
            (10.0, -5.0, 15.0),       # Arbitrary point
            (-100.0, 200.0, -50.0)    # Extended range
        ]
        
        for point in normalization_points:
            dose = RTDoseModule.from_required_elements(
                dose_units=DoseUnits.GY,
                dose_type=DoseType.PHYSICAL,
                dose_summation_type=DoseSummationType.PLAN
            ).with_pixel_data_elements(
                samples_per_pixel=1,
                photometric_interpretation="MONOCHROME2",
                bits_allocated=32,
                bits_stored=32,
                high_bit=31,
                pixel_representation=0,
                dose_grid_scaling=0.001
            ).with_optional_elements(
                normalization_point=list(point)
            )
            dose.PixelData = dose_array.tobytes()
            assert dose.NormalizationPoint == list(point)
    
    def test_dose_summation_type_validation(self):
        """Test dose summation type enum validation."""
        dose_array = np.ones((5, 5, 5), dtype=np.uint32)
        
        for summation_type in [DoseSummationType.PLAN, DoseSummationType.MULTI_PLAN, DoseSummationType.FRACTION]:
            dose = RTDoseModule.from_required_elements(
                dose_units=DoseUnits.GY,
                dose_type=DoseType.PHYSICAL,
                dose_summation_type=summation_type
            ).with_pixel_data_elements(
                samples_per_pixel=1,
                photometric_interpretation="MONOCHROME2",
                bits_allocated=32,
                bits_stored=32,
                high_bit=31,
                pixel_representation=0,
                dose_grid_scaling=0.001
            )
            dose.PixelData = dose_array.tobytes()
            assert dose.DoseSummationType == summation_type.value
    
    def test_large_dose_array_handling(self):
        """Test handling of large dose arrays (memory efficiency)."""
        # Test realistic RT dose array size
        large_dose_array = np.ones((128, 128, 64), dtype=np.uint32)
        
        dose = RTDoseModule.from_required_elements(
            dose_units=DoseUnits.GY,
            dose_type=DoseType.PHYSICAL,
            dose_summation_type=DoseSummationType.PLAN
        ).with_pixel_data_elements(
            samples_per_pixel=1,
            photometric_interpretation="MONOCHROME2",
            bits_allocated=32,
            bits_stored=32,
            high_bit=31,
            pixel_representation=0,
            dose_grid_scaling=0.001
        )
        dose.PixelData = large_dose_array.tobytes()
        
        # Verify module can handle large arrays
        assert hasattr(dose, 'PixelData')
        # Check array shape is preserved
        assert large_dose_array.shape == (128, 128, 64)
    
    def test_validation_method_exists(self):
        """Test that validation method exists and is callable."""
        dose_array = np.ones((5, 5, 5), dtype=np.uint32)
        dose = RTDoseModule.from_required_elements(
            dose_units=DoseUnits.GY,
            dose_type=DoseType.PHYSICAL,
            dose_summation_type=DoseSummationType.PLAN
        ).with_pixel_data_elements(
            samples_per_pixel=1,
            photometric_interpretation="MONOCHROME2",
            bits_allocated=32,
            bits_stored=32,
            high_bit=31,
            pixel_representation=0,
            dose_grid_scaling=0.001
        )
        dose.PixelData = dose_array.tobytes()
        
        assert hasattr(dose, 'validate')
        assert callable(dose.validate)
        
        # Test validation result structure
        validation_result = dose.validate()
        assert validation_result is not None
        assert isinstance(validation_result, ValidationResult)
        assert hasattr(validation_result, 'errors')
        assert hasattr(validation_result, 'warnings')
        assert isinstance(validation_result.errors, list)
        assert isinstance(validation_result.warnings, list)
    
    def test_rt_dose_specific_requirements(self):
        """Test RT dose specific DICOM requirements."""
        dose_array = np.ones((10, 10, 10), dtype=np.uint32)
        
        # RT Dose must have specific pixel representation
        dose = RTDoseModule.from_required_elements(
            dose_units=DoseUnits.GY,
            dose_type=DoseType.PHYSICAL,
            dose_summation_type=DoseSummationType.PLAN
        ).with_pixel_data_elements(
            samples_per_pixel=1,
            photometric_interpretation="MONOCHROME2",
            bits_allocated=32,
            bits_stored=32,
            high_bit=31,
            pixel_representation=0,  # Unsigned integers for dose
            dose_grid_scaling=0.001
        )
        dose.PixelData = dose_array.tobytes()
        
        # Verify RT dose specific requirements
        assert dose.SamplesPerPixel == 1  # Grayscale
        assert dose.PixelRepresentation == 0  # Unsigned
        assert dose.DoseUnits in ["GY", "RELATIVE"]
        assert dose.DoseType in ["PHYSICAL", "EFFECTIVE", "ERROR"]