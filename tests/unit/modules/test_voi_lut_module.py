"""
Test VoiLutModule functionality.

VoiLutModule implements DICOM PS3.3 C.11.2 VOI LUT Module.
Describes the VOI LUT transformation that converts pixel values to values
that are meaningful for display.
"""

from pydicom import Dataset
from pyrt_dicom.modules import VoiLutModule
from pyrt_dicom.enums import VoiLutFunction
from pyrt_dicom.validators import ValidationResult


class TestVoiLutModule:
    """Test VoiLutModule functionality."""
    
    @staticmethod
    def create_dataset_lut_item(lut_descriptor, lut_data, lut_explanation=None):
        """Helper to create proper Dataset instances for VOI LUT sequence."""
        item = Dataset()
        item.LUTDescriptor = lut_descriptor
        item.LUTData = lut_data
        if lut_explanation is not None:
            item.LUTExplanation = lut_explanation
        return item
    
    def test_from_required_elements_success(self):
        """Test successful creation with required elements (none for this module)."""
        voi_lut = VoiLutModule.from_required_elements()
        
        # VOI LUT Module has no Type 1 or Type 2 elements
        # Should create successfully but not be configured yet
        assert not voi_lut.is_configured
        assert not voi_lut.has_voi_lut_sequence
        assert not voi_lut.has_window_parameters
        assert not voi_lut.has_optional_elements
    
    def test_with_voi_lut_sequence(self):
        """Test creation with VOI LUT Sequence (Type 1C)."""
        lut_item = self.create_dataset_lut_item(
            lut_descriptor=[256, 0, 8],
            lut_data=[0, 1, 2, 3, 4, 5],
            lut_explanation="Test display LUT"
        )
        
        voi_lut = VoiLutModule.from_required_elements().with_voi_lut_sequence(
            voi_lut_sequence=[lut_item]
        )
        
        assert voi_lut.is_configured
        assert voi_lut.has_voi_lut_sequence
        assert not voi_lut.has_window_parameters
        assert hasattr(voi_lut, 'VOILUTSequence')
        assert len(voi_lut.VOILUTSequence) == 1
        assert voi_lut.VOILUTSequence[0].LUTDescriptor == [256, 0, 8]
        assert voi_lut.VOILUTSequence[0].LUTData == [0, 1, 2, 3, 4, 5]
        assert voi_lut.VOILUTSequence[0].LUTExplanation == "Test display LUT"
    
    def test_with_window_parameters(self):
        """Test creation with Window Center/Width parameters (Type 1C)."""
        voi_lut = VoiLutModule.from_required_elements().with_window_parameters(
            window_center=[2048.0],
            window_width=[4096.0],
            window_explanation=["Standard CT window"],
            voi_lut_function=VoiLutFunction.LINEAR
        )
        
        assert voi_lut.is_configured
        assert not voi_lut.has_voi_lut_sequence
        assert voi_lut.has_window_parameters
        assert voi_lut.has_optional_elements
        assert hasattr(voi_lut, 'WindowCenter')
        assert hasattr(voi_lut, 'WindowWidth')
        assert hasattr(voi_lut, 'WindowCenterWidthExplanation')
        assert hasattr(voi_lut, 'VOILUTFunction')
        assert voi_lut.WindowCenter == "2048.0"
        assert voi_lut.WindowWidth == "4096.0"
        assert voi_lut.WindowCenterWidthExplanation == "Standard CT window"
        assert voi_lut.VOILUTFunction == "LINEAR"
    
    def test_with_optional_elements_empty(self):
        """Test that with_optional_elements rejects any arguments."""
        voi_lut = VoiLutModule.from_required_elements()
        
        # Should succeed with no arguments
        result = voi_lut.with_optional_elements()
        assert result is voi_lut
        
        # Should raise ValueError with any arguments
        try:
            voi_lut.with_optional_elements(invalid_param="test")
            assert False, "Should have raised ValueError"
        except ValueError as e:
            assert "has no optional elements" in str(e)
            assert "invalid_param" in str(e)
    
    def test_create_voi_lut_item(self):
        """Test VOI LUT item creation helper method."""
        # Test with all parameters
        item_with_explanation = VoiLutModule.create_voi_lut_item(
            lut_descriptor=[256, 0, 8],
            lut_data=[0, 1, 2, 3],
            lut_explanation="Display LUT"
        )
        
        assert item_with_explanation['LUTDescriptor'] == [256, 0, 8]
        assert item_with_explanation['LUTData'] == [0, 1, 2, 3]
        assert item_with_explanation['LUTExplanation'] == "Display LUT"
        
        # Test without explanation
        item_without_explanation = VoiLutModule.create_voi_lut_item(
            lut_descriptor=[512, 0, 16],
            lut_data=[100, 200, 300, 400]
        )
        
        assert item_without_explanation['LUTDescriptor'] == [512, 0, 16]
        assert item_without_explanation['LUTData'] == [100, 200, 300, 400]
        assert 'LUTExplanation' not in item_without_explanation
    
    def test_multiple_window_parameters(self):
        """Test creation with multiple window center/width pairs."""
        voi_lut = VoiLutModule.from_required_elements().with_window_parameters(
            window_center=[1024.0, 2048.0, 512.0],
            window_width=[2048.0, 4096.0, 1024.0],
            window_explanation=["Bone", "Soft tissue", "Lung"]
        )
        
        assert voi_lut.is_configured
        assert voi_lut.has_window_parameters
        # Window values are stored as lists when multiple values provided
        assert voi_lut.WindowCenter == [1024.0, 2048.0, 512.0]
        assert voi_lut.WindowWidth == [2048.0, 4096.0, 1024.0]
        assert voi_lut.WindowCenterWidthExplanation == ["Bone", "Soft tissue", "Lung"]
    
    def test_window_parameters_validation(self):
        """Test window parameter validation during creation."""
        voi_lut = VoiLutModule.from_required_elements()
        
        # Test mismatched window center/width counts
        try:
            voi_lut.with_window_parameters(
                window_center=[1024.0, 2048.0],
                window_width=[2048.0]  # Different count
            )
            assert False, "Should have raised ValueError"
        except ValueError as e:
            assert "same number of values" in str(e)
        
        # Test invalid window width (< 1)
        try:
            voi_lut.with_window_parameters(
                window_center=[1024.0],
                window_width=[0.5]  # Invalid width
            )
            assert False, "Should have raised ValueError"
        except ValueError as e:
            assert "Window Width must be >= 1" in str(e)
        
        # Test mismatched explanation count
        try:
            voi_lut.with_window_parameters(
                window_center=[1024.0, 2048.0],
                window_width=[2048.0, 4096.0],
                window_explanation=["Only one explanation"]  # Wrong count
            )
            assert False, "Should have raised ValueError"
        except ValueError as e:
            assert "Window explanation must match" in str(e)
    
    def test_voi_lut_sequence_validation(self):
        """Test VOI LUT sequence validation during creation."""
        voi_lut = VoiLutModule.from_required_elements()
        
        # Test empty sequence
        try:
            voi_lut.with_voi_lut_sequence(voi_lut_sequence=[])
            assert False, "Should have raised ValueError"
        except ValueError as e:
            assert "must contain at least one item" in str(e)
    
    def test_voi_lut_function_enum_values(self):
        """Test all valid VOI LUT function enum values."""
        function_values = [VoiLutFunction.LINEAR, VoiLutFunction.LINEAR_EXACT, VoiLutFunction.SIGMOID]
        
        for function in function_values:
            voi_lut = VoiLutModule.from_required_elements().with_window_parameters(
                window_center=[2048.0],
                window_width=[4096.0],
                voi_lut_function=function
            )
            assert voi_lut.VOILUTFunction == function.value
    
    def test_window_parameters_without_optional(self):
        """Test window parameters without optional elements."""
        voi_lut = VoiLutModule.from_required_elements().with_window_parameters(
            window_center=[1024.0],
            window_width=[2048.0]
        )
        
        assert voi_lut.is_configured
        assert voi_lut.has_window_parameters
        assert not voi_lut.has_optional_elements
        assert not hasattr(voi_lut, 'WindowCenterWidthExplanation')
        assert not hasattr(voi_lut, 'VOILUTFunction')
    
    def test_multiple_voi_lut_items(self):
        """Test creation with multiple VOI LUT items."""
        item1 = self.create_dataset_lut_item(
            lut_descriptor=[256, 0, 8],
            lut_data=[0, 1, 2],
            lut_explanation="First LUT"
        )
        
        item2 = self.create_dataset_lut_item(
            lut_descriptor=[512, 0, 16],
            lut_data=[100, 200, 300]
        )
        
        voi_lut = VoiLutModule.from_required_elements().with_voi_lut_sequence(
            voi_lut_sequence=[item1, item2]
        )
        
        assert voi_lut.is_configured
        assert voi_lut.has_voi_lut_sequence
        assert len(voi_lut.VOILUTSequence) == 2
        assert voi_lut.VOILUTSequence[0].LUTExplanation == "First LUT"
        assert not hasattr(voi_lut.VOILUTSequence[1], 'LUTExplanation')
    
    def test_property_checks(self):
        """Test module property checking methods."""
        # Test unconfigured module
        voi_lut = VoiLutModule.from_required_elements()
        assert not voi_lut.is_configured
        assert not voi_lut.has_voi_lut_sequence
        assert not voi_lut.has_window_parameters
        assert not voi_lut.has_optional_elements
        
        # Test with VOI LUT sequence
        lut_item = self.create_dataset_lut_item([256, 0, 8], [0, 1, 2])
        voi_lut_with_sequence = VoiLutModule.from_required_elements().with_voi_lut_sequence([lut_item])
        assert voi_lut_with_sequence.is_configured
        assert voi_lut_with_sequence.has_voi_lut_sequence
        assert not voi_lut_with_sequence.has_window_parameters
        assert not voi_lut_with_sequence.has_optional_elements
        
        # Test with window parameters and optional elements
        voi_lut_with_windows = VoiLutModule.from_required_elements().with_window_parameters(
            window_center=[1024.0],
            window_width=[2048.0],
            window_explanation=["Test"],
            voi_lut_function=VoiLutFunction.SIGMOID
        )
        assert voi_lut_with_windows.is_configured
        assert not voi_lut_with_windows.has_voi_lut_sequence
        assert voi_lut_with_windows.has_window_parameters
        assert voi_lut_with_windows.has_optional_elements
    
    def test_validation_method_exists(self):
        """Test that validation method exists and is callable."""
        voi_lut = VoiLutModule.from_required_elements().with_window_parameters(
            window_center=[2048.0],
            window_width=[4096.0]
        )
        
        assert hasattr(voi_lut, 'validate')
        assert callable(voi_lut.validate)
        
        # Test validation result structure
        validation_result = voi_lut.validate()
        assert validation_result is not None
        assert isinstance(validation_result, ValidationResult)
        assert hasattr(validation_result, 'errors')
        assert hasattr(validation_result, 'warnings')
        assert isinstance(validation_result.errors, list)
        assert isinstance(validation_result.warnings, list)
    
    def test_string_voi_lut_function(self):
        """Test VOI LUT function with string values."""
        voi_lut = VoiLutModule.from_required_elements().with_window_parameters(
            window_center=[2048.0],
            window_width=[4096.0],
            voi_lut_function="SIGMOID"  # String instead of enum
        )
        
        assert voi_lut.VOILUTFunction == "SIGMOID"
    
    def test_method_chaining(self):
        """Test that methods return self for chaining."""
        voi_lut = VoiLutModule.from_required_elements()
        
        # Test that with_optional_elements returns self
        result = voi_lut.with_optional_elements()
        assert result is voi_lut
        
        # Test that with_window_parameters returns self
        result = voi_lut.with_window_parameters(
            window_center=[1024.0],
            window_width=[2048.0]
        )
        assert result is voi_lut
        
        # Test that with_voi_lut_sequence returns self
        new_voi_lut = VoiLutModule.from_required_elements()
        lut_item = self.create_dataset_lut_item([256, 0, 8], [0, 1, 2])
        result = new_voi_lut.with_voi_lut_sequence([lut_item])
        assert result is new_voi_lut