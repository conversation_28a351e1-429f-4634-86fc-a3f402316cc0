"""
Test RTPatientSetupModule functionality.

RTPatientSetupModule implements DICOM PS3.3 C.8.8.12 RT Patient Setup Module.
Contains information describing patient positioning and fixation devices.
"""

from pydicom import Dataset
from pyrt_dicom.modules import RTPatientSetupModule
from pyrt_dicom.enums.rt_enums import (
    FixationDeviceType, ShieldingDeviceType, SetupTechnique, SetupDeviceType,
    RespiratoryMotionCompensationTechnique, RespiratorySignalSource
)
from pyrt_dicom.enums.series_enums import PatientPosition
from pyrt_dicom.validators import ValidationResult


class TestRTPatientSetupModule:
    """Test RTPatientSetupModule functionality."""
    
    def _dict_to_dataset(self, data_dict):
        """Convert dictionary to Dataset for pydicom compatibility."""
        dataset = Dataset()
        for key, value in data_dict.items():
            setattr(dataset, key, value)
        return dataset
    
    def test_from_required_elements_success(self):
        """Test successful creation with required elements."""
        patient_setup_item = RTPatientSetupModule.create_patient_setup_item(
            patient_setup_number=1,
            patient_position=PatientPosition.HFS
        )
        
        # Convert dictionary to Dataset for pydicom compatibility
        patient_setup_dataset = self._dict_to_dataset(patient_setup_item)
        
        module = RTPatientSetupModule.from_required_elements(
            patient_setup_sequence=[patient_setup_dataset]
        )
        
        assert hasattr(module, 'PatientSetupSequence')
        assert len(module.PatientSetupSequence) == 1
        assert module.PatientSetupSequence[0].PatientSetupNumber == 1
        assert module.PatientSetupSequence[0].PatientPosition == "HFS"
    
    def test_patient_setup_item_creation(self):
        """Test creation of patient setup sequence items."""
        # Test with patient position
        setup_item = RTPatientSetupModule.create_patient_setup_item(
            patient_setup_number=1,
            patient_position=PatientPosition.HFS,
            patient_setup_label="Standard Setup"
        )
        
        assert setup_item['PatientSetupNumber'] == 1
        assert setup_item['PatientPosition'] == "HFS"
        assert setup_item['PatientSetupLabel'] == "Standard Setup"
        
        # Test with patient additional position
        setup_item = RTPatientSetupModule.create_patient_setup_item(
            patient_setup_number=2,
            patient_additional_position="Custom Position"
        )
        
        assert setup_item['PatientSetupNumber'] == 2
        assert setup_item['PatientAdditionalPosition'] == "Custom Position"
        assert 'PatientPosition' not in setup_item
    
    def test_patient_setup_conditional_requirement(self):
        """Test conditional requirement for patient position or additional position."""
        # Should raise error when neither position nor additional position provided
        try:
            RTPatientSetupModule.create_patient_setup_item(
                patient_setup_number=1
            )
            assert False, "Should have raised ValueError"
        except ValueError as e:
            assert "Either Patient Position or Patient Additional Position must be present" in str(e)
    
    def test_fixation_device_item_creation(self):
        """Test creation of fixation device sequence items."""
        fixation_device = RTPatientSetupModule.create_fixation_device_item(
            fixation_device_type=FixationDeviceType.MASK,
            fixation_device_label="Head Mask",
            fixation_device_description="Custom thermoplastic mask"
        )
        
        assert fixation_device['FixationDeviceType'] == "MASK"
        assert fixation_device['FixationDeviceLabel'] == "Head Mask"
        assert fixation_device['FixationDeviceDescription'] == "Custom thermoplastic mask"
    
    def test_shielding_device_item_creation(self):
        """Test creation of shielding device sequence items."""
        shielding_device = RTPatientSetupModule.create_shielding_device_item(
            shielding_device_type=ShieldingDeviceType.EYE,
            shielding_device_label="Eye Shield",
            shielding_device_description="Custom eye shield"
        )
        
        assert shielding_device['ShieldingDeviceType'] == "EYE"
        assert shielding_device['ShieldingDeviceLabel'] == "Eye Shield"
        assert shielding_device['ShieldingDeviceDescription'] == "Custom eye shield"
    
    def test_setup_device_item_creation(self):
        """Test creation of setup device sequence items."""
        setup_device = RTPatientSetupModule.create_setup_device_item(
            setup_device_type=SetupDeviceType.LASER_POINTER,
            setup_device_label="Positioning Laser",
            setup_device_parameter=90.0,
            setup_device_description="Room laser system"
        )
        
        assert setup_device['SetupDeviceType'] == "LASER_POINTER"
        assert setup_device['SetupDeviceLabel'] == "Positioning Laser"
        assert setup_device['SetupDeviceParameter'] == 90.0
        assert setup_device['SetupDeviceDescription'] == "Room laser system"
    
    def test_motion_synchronization_item_creation(self):
        """Test creation of motion synchronization sequence items."""
        motion_sync = RTPatientSetupModule.create_motion_synchronization_item(
            respiratory_motion_compensation_technique=RespiratoryMotionCompensationTechnique.BREATH_HOLD,
            respiratory_signal_source=RespiratorySignalSource.BELT,
            respiratory_motion_compensation_technique_description="Deep inspiration breath hold"
        )
        
        assert motion_sync['RespiratoryMotionCompensationTechnique'] == "BREATH_HOLD"
        assert motion_sync['RespiratorySignalSource'] == "BELT"
        assert motion_sync['RespiratoryMotionCompensationTechniqueDescription'] == "Deep inspiration breath hold"
    
    def test_referenced_setup_image_item_creation(self):
        """Test creation of referenced setup image sequence items."""
        setup_image = RTPatientSetupModule.create_referenced_setup_image_item(
            referenced_sop_class_uid="1.2.840.10008.*******.1.481.1",
            referenced_sop_instance_uid="*******.*******.9",
            setup_image_comment="Setup verification image"
        )
        
        assert setup_image['ReferencedSOPClassUID'] == "1.2.840.10008.*******.1.481.1"
        assert setup_image['ReferencedSOPInstanceUID'] == "*******.*******.9"
        assert setup_image['SetupImageComment'] == "Setup verification image"
    
    def test_code_sequence_item_creation(self):
        """Test creation of code sequence items."""
        code_item = RTPatientSetupModule.create_code_sequence_item(
            code_value="123456",
            coding_scheme_designator="DCM",
            code_meaning="Example Code"
        )
        
        assert code_item['CodeValue'] == "123456"
        assert code_item['CodingSchemeDesignator'] == "DCM"
        assert code_item['CodeMeaning'] == "Example Code"
    
    def test_module_properties(self):
        """Test module convenience properties."""
        # Test empty module
        module = RTPatientSetupModule.from_required_elements(
            patient_setup_sequence=[]
        )
        
        # Even empty sequence creates the attribute, so has_patient_setups is True
        assert module.has_patient_setups
        assert module.patient_setup_count == 0
        assert module.get_patient_setup_numbers() == []
        
        # Test module with setups
        setup_item1 = RTPatientSetupModule.create_patient_setup_item(
            patient_setup_number=1,
            patient_position=PatientPosition.HFS
        )
        setup_item2 = RTPatientSetupModule.create_patient_setup_item(
            patient_setup_number=2,
            patient_position=PatientPosition.HFP
        )
        
        # Convert dictionaries to Datasets
        setup_dataset1 = self._dict_to_dataset(setup_item1)
        setup_dataset2 = self._dict_to_dataset(setup_item2)
        
        module = RTPatientSetupModule.from_required_elements(
            patient_setup_sequence=[setup_dataset1, setup_dataset2]
        )
        
        assert module.has_patient_setups
        assert module.patient_setup_count == 2
        assert module.get_patient_setup_numbers() == [1, 2]
        
        # Test getting setup by number
        found_setup = module.get_patient_setup_by_number(1)
        assert found_setup is not None
        assert found_setup.PatientSetupNumber == 1
        
        not_found_setup = module.get_patient_setup_by_number(99)
        assert not_found_setup is None
    
    def test_with_optional_elements(self):
        """Test with_optional_elements method."""
        setup_item = RTPatientSetupModule.create_patient_setup_item(
            patient_setup_number=1,
            patient_position=PatientPosition.HFS
        )
        
        # Convert dictionary to Dataset
        setup_dataset = self._dict_to_dataset(setup_item)
        
        module = RTPatientSetupModule.from_required_elements(
            patient_setup_sequence=[setup_dataset]
        ).with_optional_elements()
        
        # Should return self for method chaining
        assert isinstance(module, RTPatientSetupModule)
        assert module.has_patient_setups
    
    def test_has_fixation_devices(self):
        """Test has_fixation_devices property."""
        # Test without fixation devices
        setup_item = RTPatientSetupModule.create_patient_setup_item(
            patient_setup_number=1,
            patient_position=PatientPosition.HFS
        )
        
        setup_dataset = self._dict_to_dataset(setup_item)
        
        module = RTPatientSetupModule.from_required_elements(
            patient_setup_sequence=[setup_dataset]
        )
        
        assert not module.has_fixation_devices()
        
        # Test with fixation devices
        fixation_device = RTPatientSetupModule.create_fixation_device_item(
            fixation_device_type=FixationDeviceType.MASK,
            fixation_device_label="Head Mask"
        )
        
        # Convert fixation device to Dataset
        fixation_dataset = self._dict_to_dataset(fixation_device)
        
        setup_item_with_fixation = RTPatientSetupModule.create_patient_setup_item(
            patient_setup_number=1,
            patient_position=PatientPosition.HFS,
            fixation_device_sequence=[fixation_dataset]
        )
        
        setup_dataset_with_fixation = self._dict_to_dataset(setup_item_with_fixation)
        
        module = RTPatientSetupModule.from_required_elements(
            patient_setup_sequence=[setup_dataset_with_fixation]
        )
        
        assert module.has_fixation_devices()
    
    def test_has_motion_synchronization(self):
        """Test has_motion_synchronization property."""
        # Test without motion synchronization
        setup_item = RTPatientSetupModule.create_patient_setup_item(
            patient_setup_number=1,
            patient_position=PatientPosition.HFS
        )
        
        setup_dataset = self._dict_to_dataset(setup_item)
        
        module = RTPatientSetupModule.from_required_elements(
            patient_setup_sequence=[setup_dataset]
        )
        
        assert not module.has_motion_synchronization()
        
        # Test with motion synchronization
        motion_sync = RTPatientSetupModule.create_motion_synchronization_item(
            respiratory_motion_compensation_technique=RespiratoryMotionCompensationTechnique.BREATH_HOLD,
            respiratory_signal_source=RespiratorySignalSource.BELT
        )
        
        # Convert motion sync to Dataset
        motion_dataset = self._dict_to_dataset(motion_sync)
        
        setup_item_with_motion = RTPatientSetupModule.create_patient_setup_item(
            patient_setup_number=1,
            patient_position=PatientPosition.HFS,
            motion_synchronization_sequence=[motion_dataset]
        )
        
        setup_dataset_with_motion = self._dict_to_dataset(setup_item_with_motion)
        
        module = RTPatientSetupModule.from_required_elements(
            patient_setup_sequence=[setup_dataset_with_motion]
        )
        
        assert module.has_motion_synchronization()
    
    def test_validation(self):
        """Test module validation."""
        setup_item = RTPatientSetupModule.create_patient_setup_item(
            patient_setup_number=1,
            patient_position=PatientPosition.HFS
        )
        
        setup_dataset = self._dict_to_dataset(setup_item)
        
        module = RTPatientSetupModule.from_required_elements(
            patient_setup_sequence=[setup_dataset]
        )
        
        assert hasattr(module, 'validate')
        assert callable(module.validate)
        
        # Test validation result structure
        validation_result = module.validate()
        assert validation_result is not None
        assert isinstance(validation_result, ValidationResult)
        assert hasattr(validation_result, 'errors')
        assert hasattr(validation_result, 'warnings')
        assert isinstance(validation_result.errors, list)
        assert isinstance(validation_result.warnings, list)
    