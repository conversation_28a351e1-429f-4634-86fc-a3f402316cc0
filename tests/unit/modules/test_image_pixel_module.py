"""
Test ImagePixelModule (C - Conditional) functionality.

ImagePixelModule implements DICOM PS3.3 C.7.6.3 Image Pixel Module.
Required when GeneralImageModule is present for pixel data representation.
"""

import pytest
import numpy as np
from pyrt_dicom.modules import ImagePixelModule
from pyrt_dicom.validators import ValidationResult


class TestImagePixelModule:
    """Test ImagePixelModule (C - Conditional) functionality."""
    
    def test_from_required_elements_success(self):
        """Test successful creation with required elements."""
        pixel = ImagePixelModule.from_required_elements(
            samples_per_pixel=1,
            photometric_interpretation="MONOCHROME2",
            rows=128,
            columns=128,
            bits_allocated=32,
            bits_stored=32,
            high_bit=31,
            pixel_representation=0
        )
        
        assert pixel.SamplesPerPixel == 1
        assert pixel.PhotometricInterpretation == "MONOCHROME2"
        assert pixel.Rows == 128
        assert pixel.Columns == 128
        assert pixel.BitsAllocated == 32
        assert pixel.BitsStored == 32
        assert pixel.HighBit == 31
        assert pixel.PixelRepresentation in [0, "0"]  # Can be stored as int or string
    
    def test_rt_dose_pixel_characteristics(self):
        """Test RT dose specific pixel characteristics."""
        # RT dose is always grayscale, unsigned integers
        pixel = ImagePixelModule.from_required_elements(
            samples_per_pixel=1,  # Grayscale
            photometric_interpretation="MONOCHROME2",
            rows=64,
            columns=64,
            bits_allocated=32,
            bits_stored=32,
            high_bit=31,
            pixel_representation=0  # Unsigned integers
        )
        
        assert pixel.SamplesPerPixel == 1  # Grayscale only
        assert pixel.PhotometricInterpretation == "MONOCHROME2"
        assert pixel.PixelRepresentation in [0, "0"]  # Unsigned
    
    def test_various_matrix_sizes(self):
        """Test various dose grid matrix sizes."""
        matrix_sizes = [
            (32, 32),    # Small grid
            (64, 64),    # Standard grid
            (128, 128),  # High resolution
            (256, 256),  # Very high resolution
            (64, 128),   # Non-square
            (100, 80)    # Irregular size
        ]
        
        for rows, cols in matrix_sizes:
            pixel = ImagePixelModule.from_required_elements(
                samples_per_pixel=1,
                photometric_interpretation="MONOCHROME2",
                rows=rows,
                columns=cols,
                bits_allocated=32,
                bits_stored=32,
                high_bit=31,
                pixel_representation=0
            )
            assert pixel.Rows == rows
            assert pixel.Columns == cols
    
    def test_bits_configuration_validation(self):
        """Test various bits allocated/stored configurations."""
        bits_configs = [
            (16, 16, 15),  # 16-bit
            (32, 32, 31),  # 32-bit
            (16, 12, 11),  # 12-bit stored in 16-bit
            (32, 24, 23)   # 24-bit stored in 32-bit
        ]
        
        for allocated, stored, high_bit in bits_configs:
            pixel = ImagePixelModule.from_required_elements(
                samples_per_pixel=1,
                photometric_interpretation="MONOCHROME2",
                rows=64,
                columns=64,
                bits_allocated=allocated,
                bits_stored=stored,
                high_bit=high_bit,
                pixel_representation=0
            )
            assert pixel.BitsAllocated == allocated
            assert pixel.BitsStored == stored
            assert pixel.HighBit == high_bit
    
    def test_photometric_interpretation_validation(self):
        """Test photometric interpretation values for RT dose."""
        # RT dose typically uses MONOCHROME2
        interpretations = ["MONOCHROME1", "MONOCHROME2"]
        
        for interpretation in interpretations:
            pixel = ImagePixelModule.from_required_elements(
                samples_per_pixel=1,
                photometric_interpretation=interpretation,
                rows=64,
                columns=64,
                bits_allocated=32,
                bits_stored=32,
                high_bit=31,
                pixel_representation=0
            )
            assert pixel.PhotometricInterpretation == interpretation
    
    def test_with_optional_elements(self):
        """Test adding optional pixel elements."""
        pixel_data = np.ones((64, 64), dtype=np.uint32)
        
        pixel = ImagePixelModule.from_required_elements(
            samples_per_pixel=1,
            photometric_interpretation="MONOCHROME2",
            rows=64,
            columns=64,
            bits_allocated=32,
            bits_stored=32,
            high_bit=31,
            pixel_representation=0
        ).with_pixel_data(pixel_data=pixel_data.tobytes()).with_color_configuration(planar_configuration=0).with_pixel_aspect_ratio(pixel_aspect_ratio=[1, 1]).with_optional_elements(
            smallest_image_pixel_value=0,
            largest_image_pixel_value=65535
        )
        
        assert hasattr(pixel, 'PixelData')
        assert hasattr(pixel, 'PlanarConfiguration')
        assert hasattr(pixel, 'PixelAspectRatio')
        assert hasattr(pixel, 'SmallestImagePixelValue')
        assert hasattr(pixel, 'LargestImagePixelValue')
    
    def test_pixel_data_array_types(self):
        """Test various pixel data array types and shapes."""
        array_configs = [
            (np.uint16, (64, 64)),
            (np.uint32, (128, 128)),
            (np.float32, (32, 32)),
            (np.uint16, (100, 80))  # Non-square
        ]
        
        for dtype, shape in array_configs:
            pixel_data = np.ones(shape, dtype=dtype)
            
            pixel = ImagePixelModule.from_required_elements(
                samples_per_pixel=1,
                photometric_interpretation="MONOCHROME2",
                rows=shape[0],
                columns=shape[1],
                bits_allocated=32,
                bits_stored=32,
                high_bit=31,
                pixel_representation=0
            ).with_pixel_data(pixel_data=pixel_data.tobytes())
            
            assert hasattr(pixel, 'PixelData')
    
    def test_planar_configuration_validation(self):
        """Test planar configuration for grayscale images."""
        # RT dose is always grayscale (1 sample per pixel)
        pixel = ImagePixelModule.from_required_elements(
            samples_per_pixel=1,
            photometric_interpretation="MONOCHROME2",
            rows=64,
            columns=64,
            bits_allocated=32,
            bits_stored=32,
            high_bit=31,
            pixel_representation=0
        ).with_color_configuration(planar_configuration=0)  # Not applicable for grayscale but can be present
        
        assert pixel.PlanarConfiguration in [0, "0"]  # Can be stored as int or string
    
    def test_pixel_value_range_validation(self):
        """Test pixel value range validation."""
        pixel = ImagePixelModule.from_required_elements(
            samples_per_pixel=1,
            photometric_interpretation="MONOCHROME2",
            rows=64,
            columns=64,
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            pixel_representation=0
        ).with_optional_elements(
            smallest_image_pixel_value=0,
            largest_image_pixel_value=65535  # 16-bit max
        )
        
        assert pixel.SmallestImagePixelValue == 0
        assert pixel.LargestImagePixelValue == 65535
    
    def test_large_matrix_handling(self):
        """Test handling of large dose matrices."""
        # Large dose grid (512x512)
        large_rows, large_cols = 512, 512
        
        pixel = ImagePixelModule.from_required_elements(
            samples_per_pixel=1,
            photometric_interpretation="MONOCHROME2",
            rows=large_rows,
            columns=large_cols,
            bits_allocated=32,
            bits_stored=32,
            high_bit=31,
            pixel_representation=0
        )
        
        assert pixel.Rows == large_rows
        assert pixel.Columns == large_cols
    
    def test_consistency_validation(self):
        """Test consistency between pixel parameters."""
        # High bit must be less than bits allocated
        pixel = ImagePixelModule.from_required_elements(
            samples_per_pixel=1,
            photometric_interpretation="MONOCHROME2",
            rows=64,
            columns=64,
            bits_allocated=16,
            bits_stored=12,
            high_bit=11,  # bits_stored - 1
            pixel_representation=0
        )
        
        assert pixel.HighBit == pixel.BitsStored - 1
        assert pixel.BitsStored <= pixel.BitsAllocated
    
    def test_rt_dose_unsigned_integers(self):
        """Test RT dose unsigned integer requirements."""
        # RT dose values are always non-negative (unsigned)
        pixel = ImagePixelModule.from_required_elements(
            samples_per_pixel=1,
            photometric_interpretation="MONOCHROME2",
            rows=64,
            columns=64,
            bits_allocated=32,
            bits_stored=32,
            high_bit=31,
            pixel_representation=0  # Must be unsigned for dose
        )
        
        assert pixel.PixelRepresentation in [0, "0"]  # Unsigned integers
    
    def test_pixel_aspect_ratio_square(self):
        """Test square pixel aspect ratio for dose grids."""
        pixel = ImagePixelModule.from_required_elements(
            samples_per_pixel=1,
            photometric_interpretation="MONOCHROME2",
            rows=64,
            columns=64,
            bits_allocated=32,
            bits_stored=32,
            high_bit=31,
            pixel_representation=0
        ).with_pixel_aspect_ratio(pixel_aspect_ratio=[1, 1])  # Square pixels
        
        assert pixel.PixelAspectRatio == [1, 1]
    
    def test_dependency_on_general_image_module(self):
        """Test that ImagePixelModule depends on GeneralImageModule."""
        # This dependency should be validated at IOD level
        # Here we test that ImagePixelModule can be created independently
        pixel = ImagePixelModule.from_required_elements(
            samples_per_pixel=1,
            photometric_interpretation="MONOCHROME2",
            rows=64,
            columns=64,
            bits_allocated=32,
            bits_stored=32,
            high_bit=31,
            pixel_representation=0
        )
        
        # Module should be valid on its own
        assert pixel.SamplesPerPixel == 1
    
    def test_validation_method_exists(self):
        """Test that validation method exists and is callable."""
        pixel = ImagePixelModule.from_required_elements(
            samples_per_pixel=1,
            photometric_interpretation="MONOCHROME2",
            rows=64,
            columns=64,
            bits_allocated=32,
            bits_stored=32,
            high_bit=31,
            pixel_representation=0
        )
        
        assert hasattr(pixel, 'validate')
        assert callable(pixel.validate)
        
        # Test validation result structure
        validation_result = pixel.validate()
        assert validation_result is not None
        assert isinstance(validation_result, ValidationResult)
        assert hasattr(validation_result, 'errors')
        assert hasattr(validation_result, 'warnings')
        assert isinstance(validation_result.errors, list)
        assert isinstance(validation_result.warnings, list)
    
    def test_memory_efficiency_large_arrays(self):
        """Test memory efficiency with large pixel arrays."""
        # Simulate large dose grid without actually allocating
        rows, cols = 1024, 1024  # 1K x 1K grid
        
        pixel = ImagePixelModule.from_required_elements(
            samples_per_pixel=1,
            photometric_interpretation="MONOCHROME2",
            rows=rows,
            columns=cols,
            bits_allocated=32,
            bits_stored=32,
            high_bit=31,
            pixel_representation=0
        )
        
        # Verify dimensions without pixel data
        assert pixel.Rows == rows
        assert pixel.Columns == cols
        
        # Calculate expected memory usage
        expected_bytes = rows * cols * 4  # 32-bit = 4 bytes per pixel
        assert expected_bytes == 1024 * 1024 * 4  # 4MB