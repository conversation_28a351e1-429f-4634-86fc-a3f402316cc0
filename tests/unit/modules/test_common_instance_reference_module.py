"""
Test CommonInstanceReferenceModule functionality.

CommonInstanceReferenceModule implements DICOM PS3.3 C.12.2 Common Instance Reference Module.
Describes hierarchical relationships of any SOP Instances referenced from other Modules.
"""

from pyrt_dicom.modules import CommonInstanceReferenceModule
from pyrt_dicom.validators import ValidationResult


class TestCommonInstanceReferenceModule:
    """Test CommonInstanceReferenceModule functionality."""
    
    def test_from_required_elements_success(self):
        """Test successful creation with no required elements (all are Type 1C)."""
        reference = CommonInstanceReferenceModule.from_required_elements()
        
        # Module should be created successfully since all elements are Type 1C
        assert reference is not None
        assert isinstance(reference, CommonInstanceReferenceModule)
    
    def test_with_referenced_series(self):
        """Test adding referenced series sequence."""
        reference = CommonInstanceReferenceModule.from_required_elements()

        # Create referenced instance item
        referenced_instance = CommonInstanceReferenceModule.create_referenced_instance_item(
            referenced_sop_class_uid="1.2.840.10008.*******.1.2",
            referenced_sop_instance_uid="*******.*******.10"
        )

        # Create referenced series item
        referenced_series = CommonInstanceReferenceModule.create_referenced_series_item(
            series_instance_uid="*******.*******.9",
            referenced_instance_sequence=[referenced_instance]
        )

        # Add referenced series to module
        reference.with_referenced_series(
            referenced_series_sequence=[referenced_series]
        )

        # Test using to_dataset() method for dataset access
        dataset = reference.to_dataset()
        assert hasattr(dataset, 'ReferencedSeriesSequence')
        assert len(dataset.ReferencedSeriesSequence) == 1
        assert reference.has_referenced_series is True
        assert reference.referenced_series_count == 1
    
    def test_with_other_studies(self):
        """Test adding studies containing other referenced instances."""
        reference = CommonInstanceReferenceModule.from_required_elements()

        # Create other study item
        other_study = CommonInstanceReferenceModule.create_other_study_item(
            study_instance_uid="*******.*******.11"
        )

        # Add other studies to module
        reference.with_other_studies(
            studies_containing_other_referenced_instances_sequence=[other_study]
        )

        # Test using to_dataset() method for dataset access
        dataset = reference.to_dataset()
        assert hasattr(dataset, 'StudiesContainingOtherReferencedInstancesSequence')
        assert len(dataset.StudiesContainingOtherReferencedInstancesSequence) == 1
        assert reference.has_other_studies is True
        assert reference.other_studies_count == 1
    
    def test_create_referenced_instance_item(self):
        """Test creating referenced instance sequence items."""
        # Test without frame numbers
        instance_item = CommonInstanceReferenceModule.create_referenced_instance_item(
            referenced_sop_class_uid="1.2.840.10008.*******.1.2",
            referenced_sop_instance_uid="*******.*******.10"
        )
        
        assert hasattr(instance_item, 'ReferencedSOPClassUID')
        assert hasattr(instance_item, 'ReferencedSOPInstanceUID')
        assert instance_item.ReferencedSOPClassUID == "1.2.840.10008.*******.1.2"
        assert instance_item.ReferencedSOPInstanceUID == "*******.*******.10"
        assert not hasattr(instance_item, 'ReferencedFrameNumber')
        
        # Test with frame numbers
        instance_item_with_frames = CommonInstanceReferenceModule.create_referenced_instance_item(
            referenced_sop_class_uid="1.2.840.10008.*******.1.2",
            referenced_sop_instance_uid="*******.*******.10",
            referenced_frame_number=[1, 2, 3]
        )
        
        assert hasattr(instance_item_with_frames, 'ReferencedFrameNumber')
        assert instance_item_with_frames.ReferencedFrameNumber == [1, 2, 3]
        
        # Test with segment numbers
        instance_item_with_segments = CommonInstanceReferenceModule.create_referenced_instance_item(
            referenced_sop_class_uid="1.2.840.10008.*******.1.66.4",
            referenced_sop_instance_uid="*******.*******.10",
            referenced_segment_number=[1, 2, 3]
        )
        
        assert hasattr(instance_item_with_segments, 'ReferencedSegmentNumber')
        assert instance_item_with_segments.ReferencedSegmentNumber == [1, 2, 3]
        
        # Test with both frame and segment numbers
        instance_item_with_both = CommonInstanceReferenceModule.create_referenced_instance_item(
            referenced_sop_class_uid="1.2.840.10008.*******.1.66.4",
            referenced_sop_instance_uid="*******.*******.10",
            referenced_frame_number=[1, 2, 3],
            referenced_segment_number=[4, 5, 6]
        )
        
        assert hasattr(instance_item_with_both, 'ReferencedFrameNumber')
        assert hasattr(instance_item_with_both, 'ReferencedSegmentNumber')
        assert instance_item_with_both.ReferencedFrameNumber == [1, 2, 3]
        assert instance_item_with_both.ReferencedSegmentNumber == [4, 5, 6]
    
    def test_create_referenced_series_item(self):
        """Test creating referenced series sequence items."""
        referenced_instance = CommonInstanceReferenceModule.create_referenced_instance_item(
            referenced_sop_class_uid="1.2.840.10008.*******.1.2",
            referenced_sop_instance_uid="*******.*******.10"
        )
        
        series_item = CommonInstanceReferenceModule.create_referenced_series_item(
            series_instance_uid="*******.*******.9",
            referenced_instance_sequence=[referenced_instance]
        )
        
        assert hasattr(series_item, 'SeriesInstanceUID')
        assert hasattr(series_item, 'ReferencedInstanceSequence')
        assert series_item.SeriesInstanceUID == "*******.*******.9"
        assert len(series_item.ReferencedInstanceSequence) == 1
    
    def test_create_other_study_item(self):
        """Test creating other study sequence items."""
        # Test without referenced series
        study_item = CommonInstanceReferenceModule.create_other_study_item(
            study_instance_uid="*******.*******.11"
        )
        
        assert hasattr(study_item, 'StudyInstanceUID')
        assert study_item.StudyInstanceUID == "*******.*******.11"
        assert not hasattr(study_item, 'ReferencedSeriesSequence')
        
        # Test with referenced series (using proper Dataset objects)
        referenced_instance = CommonInstanceReferenceModule.create_referenced_instance_item(
            referenced_sop_class_uid="1.2.840.10008.*******.1.2",
            referenced_sop_instance_uid="*******.*******.10"
        )
        referenced_series = [CommonInstanceReferenceModule.create_referenced_series_item(
            series_instance_uid="*******.*******.9",
            referenced_instance_sequence=[referenced_instance]
        )]
        
        study_item_with_series = CommonInstanceReferenceModule.create_other_study_item(
            study_instance_uid="*******.*******.11",
            referenced_series_sequence=referenced_series
        )
        
        assert hasattr(study_item_with_series, 'ReferencedSeriesSequence')
        assert len(study_item_with_series.ReferencedSeriesSequence) == 1
    
    def test_properties_empty_module(self):
        """Test properties on empty module."""
        reference = CommonInstanceReferenceModule.from_required_elements()
        
        assert reference.has_referenced_series is False
        assert reference.has_other_studies is False
        assert reference.referenced_series_count == 0
        assert reference.other_studies_count == 0
        assert reference.has_any_references is False
        assert reference.total_instance_count == 0
    
    def test_with_optional_elements_no_options(self):
        """Test with_optional_elements method (should have no valid options)."""
        reference = CommonInstanceReferenceModule.from_required_elements()
        
        # Should work with no arguments
        result = reference.with_optional_elements()
        assert result is reference  # Should return self for chaining
    
    def test_with_optional_elements_invalid_args(self):
        """Test with_optional_elements with invalid arguments should raise error."""
        reference = CommonInstanceReferenceModule.from_required_elements()
        
        # Should raise ValueError for unexpected arguments
        try:
            reference.with_optional_elements(invalid_arg="value")
            assert False, "Expected ValueError for invalid optional element"
        except ValueError as e:
            assert "no optional elements" in str(e)
            assert "invalid_arg" in str(e)
    
    def test_to_dataset_method(self):
        """Test to_dataset() method generates proper DICOM dataset."""
        reference = CommonInstanceReferenceModule.from_required_elements()

        # Test empty module
        dataset = reference.to_dataset()
        assert isinstance(dataset, type(reference.to_dataset()))
        assert len(dataset) == 0  # Empty module should have no elements

        # Test module with data
        referenced_instance = CommonInstanceReferenceModule.create_referenced_instance_item(
            referenced_sop_class_uid="1.2.840.10008.*******.1.2",
            referenced_sop_instance_uid="*******.*******.10"
        )
        referenced_series = CommonInstanceReferenceModule.create_referenced_series_item(
            series_instance_uid="*******.*******.9",
            referenced_instance_sequence=[referenced_instance]
        )
        reference.with_referenced_series(referenced_series_sequence=[referenced_series])

        dataset = reference.to_dataset()
        assert len(dataset) > 0
        assert hasattr(dataset, 'ReferencedSeriesSequence')
        assert len(dataset.ReferencedSeriesSequence) == 1

    def test_validation_method_exists(self):
        """Test that validation method exists and is callable."""
        reference = CommonInstanceReferenceModule.from_required_elements()

        assert hasattr(reference, 'validate')
        assert callable(reference.validate)

        # Test validation result structure
        validation_result = reference.validate()
        assert validation_result is not None
        assert isinstance(validation_result, ValidationResult)
        assert hasattr(validation_result, 'errors')
        assert hasattr(validation_result, 'warnings')
        assert isinstance(validation_result.errors, list)
        assert isinstance(validation_result.warnings, list)
    
    def test_method_chaining(self):
        """Test method chaining functionality."""
        reference = CommonInstanceReferenceModule.from_required_elements()

        # Create sample data
        referenced_instance = CommonInstanceReferenceModule.create_referenced_instance_item(
            referenced_sop_class_uid="1.2.840.10008.*******.1.2",
            referenced_sop_instance_uid="*******.*******.10"
        )

        referenced_series = CommonInstanceReferenceModule.create_referenced_series_item(
            series_instance_uid="*******.*******.9",
            referenced_instance_sequence=[referenced_instance]
        )

        other_study = CommonInstanceReferenceModule.create_other_study_item(
            study_instance_uid="*******.*******.11"
        )

        # Test method chaining
        result = reference.with_referenced_series(
            referenced_series_sequence=[referenced_series]
        ).with_other_studies(
            studies_containing_other_referenced_instances_sequence=[other_study]
        ).with_optional_elements()

        assert result is reference
        assert reference.has_referenced_series is True
        assert reference.has_other_studies is True
        assert reference.referenced_series_count == 1
        assert reference.other_studies_count == 1
        assert reference.has_any_references is True
        assert reference.total_instance_count == 1
    
    def test_total_instance_count_comprehensive(self):
        """Test total_instance_count property with multiple instances."""
        reference = CommonInstanceReferenceModule.from_required_elements()

        # Create multiple referenced instances
        instance1 = CommonInstanceReferenceModule.create_referenced_instance_item(
            referenced_sop_class_uid="1.2.840.10008.*******.1.2",
            referenced_sop_instance_uid="*******.*******.10"
        )
        instance2 = CommonInstanceReferenceModule.create_referenced_instance_item(
            referenced_sop_class_uid="1.2.840.10008.*******.1.2",
            referenced_sop_instance_uid="*******.*******.11"
        )

        # Create referenced series with multiple instances
        referenced_series = CommonInstanceReferenceModule.create_referenced_series_item(
            series_instance_uid="*******.*******.9",
            referenced_instance_sequence=[instance1, instance2]
        )

        # Add to module
        reference.with_referenced_series(
            referenced_series_sequence=[referenced_series]
        )

        # Should count 2 instances in referenced series
        assert reference.total_instance_count == 2

        # Create other study with referenced series containing instances
        instance3 = CommonInstanceReferenceModule.create_referenced_instance_item(
            referenced_sop_class_uid="1.2.840.10008.*******.1.2",
            referenced_sop_instance_uid="*******.*******.12"
        )

        other_series = CommonInstanceReferenceModule.create_referenced_series_item(
            series_instance_uid="*******.*******.13",
            referenced_instance_sequence=[instance3]
        )

        other_study = CommonInstanceReferenceModule.create_other_study_item(
            study_instance_uid="*******.*******.11",
            referenced_series_sequence=[other_series]
        )

        # Add other studies
        reference.with_other_studies(
            studies_containing_other_referenced_instances_sequence=[other_study]
        )

        # Should count 3 total instances (2 from referenced series + 1 from other study)
        assert reference.total_instance_count == 3
        assert reference.has_any_references is True