"""
Test GeneralStudyModule (M - Mandatory) functionality.

GeneralStudyModule implements DICOM PS3.3 C.7.2.1 General Study Module.
Required for all RTDoseIOD instances.
"""

import pytest
from pydicom.uid import generate_uid
from pyrt_dicom.modules import GeneralStudyModule
from pyrt_dicom.validators import ValidationResult


class TestGeneralStudyModule:
    """Test GeneralStudyModule (M - Mandatory) functionality."""
    
    def test_from_required_elements_success(self):
        """Test successful creation with required elements."""
        study_uid = generate_uid()
        study = GeneralStudyModule.from_required_elements(
            study_instance_uid=study_uid,
            study_date="20240101",
            study_time="120000"
        )
        
        assert study.StudyInstanceUID == study_uid
        assert study.StudyDate == "20240101"
        assert study.StudyTime == "120000"
    
    def test_study_uid_validation(self):
        """Test study instance UID validation."""
        # Valid UID format
        valid_uid = "1.2.3.4.5.6.7.8.9"
        study = GeneralStudyModule.from_required_elements(
            study_instance_uid=valid_uid,
            study_date="",
            study_time=""
        )
        
        assert study.StudyInstanceUID == valid_uid
    
    def test_date_time_validation(self):
        """Test DICOM date/time format validation."""
        study = GeneralStudyModule.from_required_elements(
            study_instance_uid=generate_uid(),
            study_date="20240101",  # YYYYMMDD format
            study_time="123045.123456"  # HHMMSS.FFFFFF format
        )
        
        assert study.StudyDate == "20240101"
        assert study.StudyTime == "123045.123456"
    
    def test_generated_uid_uniqueness(self):
        """Test that generated UIDs are unique."""
        study1 = GeneralStudyModule.from_required_elements(
            study_instance_uid=generate_uid(),
            study_date="",
            study_time=""
        )
        study2 = GeneralStudyModule.from_required_elements(
            study_instance_uid=generate_uid(),
            study_date="",
            study_time=""
        )
        
        assert study1.StudyInstanceUID != study2.StudyInstanceUID
    
    def test_with_optional_elements(self):
        """Test adding optional study information."""
        study = GeneralStudyModule.from_required_elements(
            study_instance_uid=generate_uid(),
            study_date="20240101",
            study_time="120000",
            study_id="STUDY001",
            accession_number="ACC12345"
        ).with_optional_elements(
            study_description="RT Treatment Planning Study"
        )
        
        assert hasattr(study, 'StudyDescription')
        assert hasattr(study, 'StudyID')
        assert hasattr(study, 'AccessionNumber')
    
    def test_empty_date_time_allowed(self):
        """Test that empty date/time are allowed (Type 2)."""
        study = GeneralStudyModule.from_required_elements(
            study_instance_uid=generate_uid(),
            study_date="",  # Empty but not None
            study_time=""   # Empty but not None
        )
        
        assert study.StudyDate == ""
        assert study.StudyTime == ""
    
    def test_various_date_formats(self):
        """Test various valid DICOM date formats."""
        valid_dates = [
            "20240101",     # Standard date
            "20240229",     # Leap year
            "20231231",     # End of year
            "19700101"      # Historical date
        ]
        
        for date in valid_dates:
            study = GeneralStudyModule.from_required_elements(
                study_instance_uid=generate_uid(),
                study_date=date,
                study_time=""
            )
            assert study.StudyDate == date
    
    def test_various_time_formats(self):
        """Test various valid DICOM time formats."""
        valid_times = [
            "120000",           # HHMMSS
            "120000.000",       # HHMMSS.FFF
            "120000.123456",    # HHMMSS.FFFFFF
            "000000",           # Midnight
            "235959"            # End of day
        ]
        
        for time in valid_times:
            study = GeneralStudyModule.from_required_elements(
                study_instance_uid=generate_uid(),
                study_date="",
                study_time=time
            )
            assert study.StudyTime == time
    
    def test_validation_method_exists(self):
        """Test that validation method exists and is callable."""
        study = GeneralStudyModule.from_required_elements(
            study_instance_uid=generate_uid(),
            study_date="",
            study_time=""
        )
        
        assert hasattr(study, 'validate')
        assert callable(study.validate)
        
        # Test validation result structure
        validation_result = study.validate()
        assert validation_result is not None
        assert isinstance(validation_result, ValidationResult)
        assert hasattr(validation_result, 'errors')
        assert hasattr(validation_result, 'warnings')
        assert isinstance(validation_result.errors, list)
        assert isinstance(validation_result.warnings, list)