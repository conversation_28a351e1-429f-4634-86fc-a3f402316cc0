"""
Test RTFractionSchemeModule functionality.

RTFractionSchemeModule implements DICOM PS3.3 C.8.8.13 RT Fraction Scheme Module.
Contains information describing the fractionation of the treatment plan.
"""

from pydicom import Dataset
from pyrt_dicom.modules import RTFractionSchemeModule
from pyrt_dicom.enums.rt_enums import BeamDoseMeaning
from pyrt_dicom.enums.dose_enums import DoseType
from pyrt_dicom.validators import ValidationResult


class TestRTFractionSchemeModule:
    """Test RTFractionSchemeModule functionality."""
    
    @staticmethod
    def _dict_to_dataset(data_dict: dict) -> Dataset:
        """Convert dictionary to Dataset for sequence items."""
        dataset = Dataset()
        for key, value in data_dict.items():
            setattr(dataset, key, value)
        return dataset
    
    def test_from_required_elements_success(self):
        """Test successful creation with required elements."""
        # Create basic fraction group using Dataset directly
        fraction_group = Dataset()
        fraction_group.FractionGroupNumber = 1
        fraction_group.NumberOfFractionsPlanned = 25
        fraction_group.NumberOfBeams = 0
        fraction_group.NumberOfBrachyApplicationSetups = 0
        
        fraction_scheme = RTFractionSchemeModule.from_required_elements(
            fraction_group_sequence=[fraction_group]
        )
        
        assert hasattr(fraction_scheme, 'FractionGroupSequence')
        assert len(fraction_scheme.FractionGroupSequence) == 1
        assert fraction_scheme.FractionGroupSequence[0].FractionGroupNumber == 1
        assert fraction_scheme.FractionGroupSequence[0].NumberOfFractionsPlanned == 25
    
    def test_create_fraction_group_item_basic(self):
        """Test creation of basic fraction group item."""
        fraction_group = RTFractionSchemeModule.create_fraction_group_item(
            fraction_group_number=1,
            number_of_fractions_planned=30,
            number_of_beams=0,  # Set to 0 to avoid conditional validation
            number_of_brachy_application_setups=0
        )
        
        assert fraction_group['FractionGroupNumber'] == 1
        assert fraction_group['NumberOfFractionsPlanned'] == 30
        assert fraction_group['NumberOfBeams'] == 0
        assert fraction_group['NumberOfBrachyApplicationSetups'] == 0
    
    def test_create_fraction_group_item_with_beams(self):
        """Test creation of fraction group item with referenced beams."""
        referenced_beam_sequence = [
            RTFractionSchemeModule.create_referenced_beam_item(
                referenced_beam_number=1,
                beam_dose=180.0,
                beam_meterset=100.0
            )
        ]
        
        fraction_group = RTFractionSchemeModule.create_fraction_group_item(
            fraction_group_number=1,
            number_of_fractions_planned=25,
            number_of_beams=1,
            number_of_brachy_application_setups=0,
            referenced_beam_sequence=referenced_beam_sequence
        )
        
        assert 'ReferencedBeamSequence' in fraction_group
        assert len(fraction_group['ReferencedBeamSequence']) == 1
        assert fraction_group['ReferencedBeamSequence'][0]['ReferencedBeamNumber'] == 1
        assert fraction_group['ReferencedBeamSequence'][0]['BeamDose'] == 180.0
        assert fraction_group['ReferencedBeamSequence'][0]['BeamMeterset'] == 100.0
    
    def test_create_referenced_beam_item_basic(self):
        """Test creation of basic referenced beam item."""
        beam_item = RTFractionSchemeModule.create_referenced_beam_item(
            referenced_beam_number=5
        )
        
        assert beam_item['ReferencedBeamNumber'] == 5
        assert 'BeamDose' not in beam_item  # Optional element not provided
    
    def test_create_referenced_beam_item_with_optional_elements(self):
        """Test creation of referenced beam item with optional elements."""
        beam_item = RTFractionSchemeModule.create_referenced_beam_item(
            referenced_beam_number=3,
            beam_dose=250.5,
            beam_meterset=120.0,
            beam_dose_meaning=BeamDoseMeaning.BEAM_LEVEL,  # Use valid enum
            beam_dose_type=DoseType.PHYSICAL,
            beam_dose_specification_point=[10.0, 20.0, 30.0],
            average_beam_dose_point_depth=15.5,
            beam_dose_point_depth=12.3
        )
        
        assert beam_item['ReferencedBeamNumber'] == 3
        assert beam_item['BeamDose'] == 250.5
        assert beam_item['BeamMeterset'] == 120.0
        assert beam_item['BeamDoseMeaning'] == BeamDoseMeaning.BEAM_LEVEL.value
        assert beam_item['BeamDoseType'] == DoseType.PHYSICAL.value
        assert beam_item['BeamDoseSpecificationPoint'] == [10.0, 20.0, 30.0]
        assert beam_item['AverageBeamDosePointDepth'] == 15.5
        assert beam_item['BeamDosePointDepth'] == 12.3
    
    def test_create_referenced_brachy_application_setup_item(self):
        """Test creation of referenced brachy application setup item."""
        brachy_item = RTFractionSchemeModule.create_referenced_brachy_application_setup_item(
            referenced_brachy_application_setup_number=1,
            brachy_application_setup_dose=100.0,
            brachy_application_setup_dose_specification_point=[5.0, 10.0, 15.0]
        )
        
        assert brachy_item['ReferencedBrachyApplicationSetupNumber'] == 1
        assert brachy_item['BrachyApplicationSetupDose'] == 100.0
        assert brachy_item['BrachyApplicationSetupDoseSpecificationPoint'] == [5.0, 10.0, 15.0]
    
    def test_create_referenced_dose_item(self):
        """Test creation of referenced dose item."""
        dose_item = RTFractionSchemeModule.create_referenced_dose_item(
            referenced_dose_reference_number=2
        )
        
        assert dose_item['ReferencedDoseReferenceNumber'] == 2
    
    def test_create_referenced_dose_reference_item(self):
        """Test creation of referenced dose reference item."""
        dose_ref_item = RTFractionSchemeModule.create_referenced_dose_reference_item(
            referenced_dose_reference_number=3
        )
        
        assert dose_ref_item['ReferencedDoseReferenceNumber'] == 3
    
    def test_create_definition_source_item(self):
        """Test creation of definition source item."""
        definition_item = RTFractionSchemeModule.create_definition_source_item(
            definition_source_description="Test definition source"
        )
        
        assert definition_item['DefinitionSourceDescription'] == "Test definition source"
        
        # Test empty item
        empty_item = RTFractionSchemeModule.create_definition_source_item()
        assert len(empty_item) == 0
    
    def test_conditional_validation_beams(self):
        """Test conditional validation for beams."""
        try:
            # Should raise ValueError when number_of_beams > 0 but no referenced_beam_sequence
            RTFractionSchemeModule.create_fraction_group_item(
                fraction_group_number=1,
                number_of_fractions_planned=25,
                number_of_beams=1,  # > 0
                number_of_brachy_application_setups=0
                # missing referenced_beam_sequence
            )
            assert False, "Should have raised ValueError"
        except ValueError as e:
            assert "Referenced Beam Sequence is required" in str(e)
    
    def test_conditional_validation_brachy(self):
        """Test conditional validation for brachy application setups."""
        try:
            # Should raise ValueError when number_of_brachy_application_setups > 0 but no sequence
            RTFractionSchemeModule.create_fraction_group_item(
                fraction_group_number=1,
                number_of_fractions_planned=25,
                number_of_beams=0,
                number_of_brachy_application_setups=1  # > 0
                # missing referenced_brachy_application_setup_sequence
            )
            assert False, "Should have raised ValueError"
        except ValueError as e:
            assert "Referenced Brachy Application Setup Sequence is required" in str(e)
    
    def test_with_optional_elements(self):
        """Test with_optional_elements method."""
        fraction_group_data = RTFractionSchemeModule.create_fraction_group_item(
            fraction_group_number=1,
            number_of_fractions_planned=25,
            number_of_beams=0,
            number_of_brachy_application_setups=0
        )
        fraction_group_sequence = [self._dict_to_dataset(fraction_group_data)]
        
        fraction_scheme = RTFractionSchemeModule.from_required_elements(
            fraction_group_sequence=fraction_group_sequence
        ).with_optional_elements()
        
        # Should return self for method chaining
        assert isinstance(fraction_scheme, RTFractionSchemeModule)
    
    def test_has_fraction_groups_property(self):
        """Test has_fraction_groups property."""
        # Test with fraction groups
        fraction_group_data = RTFractionSchemeModule.create_fraction_group_item(
            fraction_group_number=1,
            number_of_fractions_planned=25,
            number_of_beams=0,
            number_of_brachy_application_setups=0
        )
        fraction_group_sequence = [self._dict_to_dataset(fraction_group_data)]
        
        fraction_scheme = RTFractionSchemeModule.from_required_elements(
            fraction_group_sequence=fraction_group_sequence
        )
        
        assert fraction_scheme.has_fraction_groups is True
        
        # Test empty module
        empty_module = RTFractionSchemeModule()
        assert empty_module.has_fraction_groups is False
    
    def test_fraction_group_count_property(self):
        """Test fraction_group_count property."""
        # Test with multiple fraction groups using Dataset objects
        group1 = Dataset()
        group1.FractionGroupNumber = 1
        group1.NumberOfFractionsPlanned = 20
        group1.NumberOfBeams = 0
        group1.NumberOfBrachyApplicationSetups = 0
        
        group2 = Dataset()
        group2.FractionGroupNumber = 2
        group2.NumberOfFractionsPlanned = 10
        group2.NumberOfBeams = 0
        group2.NumberOfBrachyApplicationSetups = 0
        
        fraction_scheme = RTFractionSchemeModule.from_required_elements(
            fraction_group_sequence=[group1, group2]
        )
        
        assert fraction_scheme.fraction_group_count == 2
        
        # Test empty module
        empty_module = RTFractionSchemeModule()
        assert empty_module.fraction_group_count == 0
    
    def test_get_total_fractions_planned(self):
        """Test get_total_fractions_planned method."""
        # Create Dataset objects for fraction groups
        group1 = Dataset()
        group1.FractionGroupNumber = 1
        group1.NumberOfFractionsPlanned = 20
        group1.NumberOfBeams = 0
        group1.NumberOfBrachyApplicationSetups = 0
        
        group2 = Dataset()
        group2.FractionGroupNumber = 2
        group2.NumberOfFractionsPlanned = 15
        group2.NumberOfBeams = 0
        group2.NumberOfBrachyApplicationSetups = 0
        
        fraction_scheme = RTFractionSchemeModule.from_required_elements(
            fraction_group_sequence=[group1, group2]
        )
        
        assert fraction_scheme.get_total_fractions_planned() == 35  # 20 + 15
        
        # Test empty module
        empty_module = RTFractionSchemeModule()
        assert empty_module.get_total_fractions_planned() == 0
    
    def test_validation_method_exists(self):
        """Test that validation method exists and is callable."""
        # Create Dataset object for fraction group
        group = Dataset()
        group.FractionGroupNumber = 1
        group.NumberOfFractionsPlanned = 25
        group.NumberOfBeams = 0
        group.NumberOfBrachyApplicationSetups = 0
        
        fraction_scheme = RTFractionSchemeModule.from_required_elements(
            fraction_group_sequence=[group]
        )
        
        assert hasattr(fraction_scheme, 'validate')
        assert callable(fraction_scheme.validate)
        
        # Test validation result structure
        validation_result = fraction_scheme.validate()
        assert validation_result is not None
        
        # Test ValidationResult functionality
        assert hasattr(validation_result, 'is_valid')
        assert hasattr(validation_result, 'has_errors')
        assert hasattr(validation_result, 'has_warnings')
        assert hasattr(validation_result, 'error_count')
        assert hasattr(validation_result, 'warning_count')
        assert hasattr(validation_result, 'errors')
        assert hasattr(validation_result, 'warnings')
        
        # Test that we can still get dict format for backward compatibility
        dict_result = validation_result.to_dict()
        assert "errors" in dict_result
        assert "warnings" in dict_result
        assert isinstance(dict_result["errors"], list)
        assert isinstance(dict_result["warnings"], list)