{"cells": [{"cell_type": "markdown", "id": "764fccf9", "metadata": {}, "source": ["# DICOM Module Parser\n", "\n", "This notebook will parse the DICOM module definitions from the DICOM standard and generate the necessary code for the module classes.\n", "\n", "First, we begin by downloading Part 03 of the DICOM standard."]}, {"cell_type": "code", "execution_count": 2, "id": "2806852f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading from cache: dicom_standard/dicom_standard_part03.html\n", "To download a fresh copy, either delete dicom_standard/dicom_standard_part03.html or set force_download=True\n", "Retrieved 34934074 characters of HTML\n"]}], "source": ["import os\n", "import requests\n", "from pathlib import Path\n", "\n", "def get_dicom_standard_html(cache_dir='dicom_standard', force_download=False):\n", "    \"\"\"\n", "    Download or load the DICOM standard HTML page.\n", "    \n", "    Args:\n", "        cache_dir (str): Directory to store the cached HTML file\n", "        force_download (bool): If True, always download a fresh copy\n", "        \n", "    Returns:\n", "        str: The HTML content of the DICOM standard page\n", "    \"\"\"\n", "    url = \"https://dicom.nema.org/medical/dicom/current/output/html/part03.html\"\n", "    os.makedirs(cache_dir, exist_ok=True)\n", "    cache_path = Path(cache_dir) / \"dicom_standard_part03.html\"\n", "    \n", "    # If file exists and we're not forcing a download, load from cache\n", "    if cache_path.exists() and not force_download:\n", "        print(f\"Loading from cache: {cache_path}\")\n", "        print(f\"To download a fresh copy, either delete {cache_path} or set force_download=True\")\n", "        return cache_path.read_text(encoding='utf-8')\n", "    \n", "    # Download the file\n", "    print(f\"Downloading DICOM standard from {url}...\")\n", "    response = requests.get(url)\n", "    response.raise_for_status()\n", "    \n", "    # Save to cache\n", "    cache_path.write_text(response.text, encoding='utf-8')\n", "    print(f\"Saved to {cache_path}\")\n", "    return response.text\n", "\n", "# Configure the DICOM output directory\n", "dicom_output_dir = \"dicom_standard\"\n", "\n", "# Get the HTML content (will download if not cached)\n", "html_content = get_dicom_standard_html(dicom_output_dir)\n", "\n", "# To force a fresh download, use:\n", "# html_content = get_dicom_standard_html(force_download=True)\n", "\n", "print(f\"Retrieved {len(html_content)} characters of HTML\")\n"]}, {"cell_type": "markdown", "id": "5a838e76", "metadata": {}, "source": ["## Process the HTML content using Beautiful Soup\n", "\n", "Note: This may take awhile..."]}, {"cell_type": "code", "execution_count": 3, "id": "23e9c895", "metadata": {}, "outputs": [], "source": ["from bs4 import BeautifulSoup\n", "soup = BeautifulSoup(html_content, 'html.parser')"]}, {"cell_type": "markdown", "id": "f72b70ac", "metadata": {}, "source": ["## Extract the HTML content for the radiotherapy-specific IODs\n", "\n", "- CT Image IOD\n", "- RT Dose IOD\n", "- RT Plan IOD\n", "- RT Structure Set IOD\n", "- RT Image IOD"]}, {"cell_type": "code", "execution_count": 7, "id": "ae1b194f", "metadata": {}, "outputs": [], "source": ["from html_to_markdown import HTMLToMarkdownConverter\n", "\n", "import re\n", "def get_text(html, additional_chars=None):\n", "    \"\"\"Remove non-printable characters from HTML content.\n", "    \n", "    Args:\n", "        html: BeautifulSoup object or HTML string\n", "        additional_chars: Optional list of additional characters to remove\n", "    \"\"\"\n", "    # Convert to string if it's a BeautifulSoup object\n", "    text = html.prettify() if hasattr(html, 'prettify') else str(html)\n", "\n", "    # Replace the non-printing spacing character with a simple space\n", "    text = re.sub(r\"Â \", \" \", text)\n", "    text = re.sub(r\"Â\", \" \", text)\n", "    \n", "    # Default problematic characters to remove\n", "    default_chars = \"Ââ€â€œâ€\\x9d\"\n", "    chars_to_remove = set(default_chars + (additional_chars or ''))\n", "    \n", "    # Remove non-printable ASCII and specified Unicode characters\n", "    cleaned = ''.join(\n", "        char for char in text \n", "        if (32 <= ord(char) <= 126 or char in '\\n\\r\\t') \n", "        and char not in chars_to_remove\n", "    )\n", "\n", "    # Remove spaces before a period or comma\n", "    cleaned = re.sub(r'\\s+([.,])', r'\\1', cleaned)\n", "    \n", "    return cleaned\n", "\n", "iod_output_dir = f\"{dicom_output_dir}/iods\"\n", "os.makedirs(iod_output_dir, exist_ok=True)\n", "\n", "rt_iods = {\n", "    \"RT Dose IOD\": None,\n", "    \"RT Plan IOD\": None,\n", "    \"RT Image IOD\": None,\n", "    \"RT Structure Set IOD\": None,\n", "    \"CT Image IOD\": None\n", "}\n", "\n", "section_div = None\n", "for h2 in soup.find_all('h2'):\n", "    for iod, value in list(rt_iods.items()):\n", "        if iod in h2.text and value is None:\n", "            section_div = h2.find_parent('div', class_='section')\n", "            if section_div:\n", "                # Save the section HTML to the dicom_standard folder\n", "                output = section_div.prettify().replace(\"Â\", \"\")\n", "                iod_file = iod.replace(\" \", \"_\").lower()\n", "                with open(f\"{iod_output_dir}/{iod_file}.html\", \"w\") as f:\n", "                    f.write(output)\n", "                converter = HTMLToMarkdownConverter()\n", "                text = converter.convert(output)\n", "                rt_iods[iod] = text\n", "                with open(Path(f\"{iod_output_dir}/{iod_file}.md\"), \"w\") as f:\n", "                    f.write(text)"]}, {"cell_type": "markdown", "id": "6744acd5", "metadata": {}, "source": ["## Next, extract the modules for the IODs above"]}, {"cell_type": "code", "execution_count": 9, "id": "24d8e66d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Patient: Patient (C.7.1.1) - M\n", "Patient: Clinical Trial Subject (C.7.1.3) - U\n", "Study: General Study (C.7.2.1) - M\n", "Study: Patient Study (C.7.2.2) - U\n", "Study: Clinical Trial Study (C.7.2.3) - U\n", "Series: RT Series (C.8.8.1) - M\n", "Series: Clinical Trial Series (C.7.3.2) - U\n", "Frame of Reference: Frame of Reference (C.7.4.1) - M\n", "Equipment: General Equipment (C.7.5.1) - M\n", "Dose: General Image (C.7.6.1) - C - Required if dose data contains grid-based doses.\n", "Dose: Image Plane (C.7.6.2) - C - Required if dose data contains grid-based doses.\n", "Dose: Image Pixel (C.7.6.3) - C - Required if dose data contains grid-based doses.\n", "Dose: Multi-frame (C.7.6.6) - C - Required if dose data contains grid-based doses and pixel data is multi-frame data.\n", "Dose: RT <PERSON> (C.8.8.3) - M\n", "Dose: RT DVH (C.8.8.4) - U\n", "Dose: SOP Common (C.12.1) - M\n", "Dose: Common Instance Reference (C.12.2) - U\n", "Dose: Frame Extraction (C.12.3) - C - Required if the SOP Instance was created in response to a Frame-Level retrieve request\n", "Plan: RT General Plan (C.8.8.9) - M\n", "Plan: RT Prescription (C.8.8.10) - U\n", "Plan: RT Tolerance Tables (C.8.8.11) - U\n", "Plan: RT Patient Setup (C.8.8.12) - U\n", "Plan: RT Fraction Scheme (C.8.8.13) - U\n", "Plan: RT Beams (C.8.8.14) - C - Required if RT Fraction Scheme Module exists and Number of Beams (300A,0080) is greater than zero for one or more fraction groups. Shall not be present, if RT Brachy Application Setups Module is present. May be present otherwise.\n", "Plan: RT Brachy Application Setups (C.8.8.15) - C - Required if RT Fraction Scheme Module exists and Number of Brachy Application Setups (300A,00A0) is greater than zero for one or more fraction groups. Shall not be present, if RT Beams Module is present. May be present otherwise.\n", "Plan: Approval (C.8.8.16) - U\n", "Plan: General Reference (C.12.4) - U\n", "Acquisition: General Acquisition (C.7.10.1) - M\n", "Image: Contrast/Bolus (C.7.6.4) - C - Required if contrast media was used in this image.\n", "Image: Cine (C.7.6.5) - C - Required if Multi-frame Image is a cine image.\n", "Image: <PERSON><PERSON> (C.7.6.12) - U\n", "Image: RT Image (C.8.8.2) - M\n", "Image: Modality LUT (C.11.1) - U\n", "Image: VOI LUT (C.11.2) - U\n", "Structure Set: Structure Set (C.8.8.5) - M\n", "Structure Set: R<PERSON><PERSON> Contour (C.8.8.6) - M\n", "Structure Set: RT ROI Observations (C.8.8.8) - M\n", "Series: General Series (C.7.3.1) - M\n", "Frame of Reference: Synchronization (C.7.4.2) - C - Required if time synchronization was applied.\n", "Image: Enhanced Patient Orientation (C.7.6.30) - U\n", "Image: Specimen (C.7.6.22) - U\n", "Image: CT Image (C.8.2.1) - M\n", "Image: Multi-energy CT Image (C.8.2.2) - C - Required if Multi-energy CT Acquisition (0018,9361) is YES.\n", "Image: Overlay Plane (C.9.2) - U\n", "Done. 44 modules parsed.\n"]}], "source": ["import re\n", "from pydicom.datadict import get_entry\n", "\n", "# DICOM VR to Python type mapping\n", "vr_to_python_type = {\n", "    \"AE\": \"str\",\n", "    \"AS\": \"str\",\n", "    \"CS\": \"str\",\n", "    \"DA\": \"str\",\n", "    \"DS\": \"str, float, int\",\n", "    \"DT\": \"str\",\n", "    \"IS\": \"str, int\",\n", "    \"LO\": \"str\",\n", "    \"LT\": \"str\",\n", "    \"PN\": \"str, PersonName\",\n", "    \"SH\": \"str\",\n", "    \"ST\": \"str\",\n", "    \"TM\": \"str\",\n", "    \"UC\": \"str\",\n", "    \"UI\": \"str\",\n", "    \"UR\": \"str\",\n", "    \"UT\": \"str\",\n", "    \"FL\": \"float\",\n", "    \"FD\": \"float\",\n", "    \"SL\": \"int\",\n", "    \"SS\": \"int\",\n", "    \"UL\": \"int\",\n", "    \"US\": \"int\",\n", "    \"OB\": \"bytes\",\n", "    \"OD\": \"bytes\",\n", "    \"OF\": \"bytes\",\n", "    \"OL\": \"bytes\",\n", "    \"OV\": \"bytes\",\n", "    \"OW\": \"bytes\",\n", "    \"UN\": \"bytes\",\n", "    \"AT\": \"int, tuple\",\n", "    \"SQ\": \"list[Dataset]\"\n", "}\n", "\n", "def add_dicom_columns_to_markdown_tables(text):\n", "    \"\"\"\n", "    Add DICOM keyword, VR, and VM columns to markdown tables between Tag and Type columns.\n", "    \n", "    Args:\n", "        text (str): Markdown text containing tables\n", "        \n", "    Returns:\n", "        str: Modified markdown text with additional columns\n", "    \"\"\"\n", "    lines = text.split('\\n')\n", "    modified_lines = []\n", "    \n", "    for line in lines:\n", "        # Check if this is a table row (contains |)\n", "        if '|' in line and line.strip():\n", "            parts = [part.strip() for part in line.split('|')]\n", "            \n", "            # Skip empty or malformed lines\n", "            if len(parts) < 4:\n", "                modified_lines.append(line)\n", "                continue\n", "            \n", "            # Ignore empty parts before the first '|' and after the last '|'\n", "            parts = parts[1:-1]\n", "                \n", "            # Check if this is a header row\n", "            if 'Attribute Name' in parts and 'Tag' in parts and 'Type' in parts and 'Attribute Description' in parts:\n", "                # This is the header row - insert new column headers\n", "                new_parts = []\n", "                for i, part in enumerate(parts):\n", "                    new_parts.append(part)\n", "                    if 'Tag' in part:\n", "                        new_parts.extend(['Keyword', 'VR', 'VM', 'Pydicom Types'])\n", "                modified_line = '| ' + ' | '.join(new_parts) + ' |'\n", "                modified_lines.append(modified_line)\n", "                \n", "            # Check if this is a separator row (contains ---)\n", "            elif any('---' in part for part in parts):\n", "                # This is the separator row - add separators for new columns\n", "                new_parts = []\n", "                for i, part in enumerate(parts):\n", "                    new_parts.append(part)\n", "                    if '---' in part and i == 1:  # Tag column position\n", "                        new_parts.extend(['---', '---', '---', '---'])\n", "                modified_line = '| ' + ' | '.join(new_parts) + ' |'\n", "                modified_lines.append(modified_line)\n", "                \n", "            else:\n", "                # This is a data row - extract tag and get DICOM info\n", "                new_parts = []\n", "                for i, part in enumerate(parts):\n", "                    new_parts.append(part)\n", "                    if i == 1:  # Tag column position\n", "                        # Extract tag from the part\n", "                        tag_match = re.search(r'\\(([0-9A-Fa-f]{4}),([0-9A-Fa-f]{4})\\)', part)\n", "                        \n", "                        if tag_match:\n", "                            # Parse the tag\n", "                            group = int(tag_match.group(1), 16)\n", "                            element = int(tag_match.group(2), 16)\n", "                            tag = (group, element)\n", "                            \n", "                            try:\n", "                                # Get DICOM dictionary info\n", "                                dicom_info = get_entry(tag)\n", "                                # dicom_info is a tuple: (VR, VM, name, is_retired, keyword)\n", "                                keyword = dicom_info[4] if len(dicom_info) > 4 else ''\n", "                                vr = dicom_info[0] if len(dicom_info) > 0 else ''\n", "                                vm = dicom_info[1] if len(dicom_info) > 1 else ''\n", "                                pydicom_types = vr_to_python_type.get(vr, '')\n", "                                \n", "                                new_parts.extend([keyword, vr, vm, pydicom_types])\n", "                            except KeyError:\n", "                                # Tag not found in dictionary - add empty columns\n", "                                new_parts.extend(['', '', '', ''])\n", "                        else:\n", "                            # No valid tag found - add empty columns\n", "                            new_parts.extend(['', '', '', ''])\n", "                \n", "                modified_line = '| ' + ' | '.join(new_parts) + ' |'\n", "                modified_lines.append(modified_line)\n", "        else:\n", "            # Not a table row, keep as is\n", "            modified_lines.append(line)\n", "    \n", "    return '\\n'.join(modified_lines)\n", "\n", "module_output_dir = f\"{dicom_output_dir}/modules\"\n", "os.makedirs(module_output_dir, exist_ok=True)\n", "\n", "modules = []\n", "for iod, markdown in rt_iods.items():\n", "    table = [line[1:-1].split(\"|\") for line in markdown.split(\"\\n\") if \"|\" in line]\n", "    for row in table:\n", "        if row[0].strip() in [\"IE\", \"---\"]:\n", "            continue\n", "        ie, module, ref, usage = [col.strip() for col in row]\n", "        if (module, ref) in modules:\n", "            continue\n", "\n", "        print(f\"{ie}: {module} ({ref}) - {usage}\")\n", "\n", "        # Find the link with the ID sect_C.7.1.1\n", "        link = soup.find('a', id=f'sect_{ref}')\n", "        if not link:\n", "            print(f\"Link not found for {ref}\")\n", "            continue\n", "\n", "        section_div = link.find_parent('div', class_='section')\n", "        if not section_div:\n", "            print(\"Section not found\")\n", "            continue\n", "        \n", "        # Save the section HTML to the dicom_standard/modules folder\n", "        output = get_text(section_div)\n", "        module_file = module.replace(\" \", \"_\").replace(\"/\", \"_\").lower()\n", "        with open(f\"{module_output_dir}/{module_file}.html\", \"w\") as f:\n", "            f.write(output)\n", "        converter = HTMLToMarkdownConverter()\n", "        text = converter.convert(output)\n", "\n", "        # Modify the markdown text attribute table to include the DICOM keyword, VR, VM, and corresponding pydicom type\n", "        text = add_dicom_columns_to_markdown_tables(text)\n", "\n", "        with open(Path(f\"{module_output_dir}/{module_file}.md\"), \"w\") as f:\n", "            f.write(text)\n", "        \n", "        modules.append((module, ref))\n", "    \n", "print(f\"Done. {len(modules)} modules parsed.\")"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 5}